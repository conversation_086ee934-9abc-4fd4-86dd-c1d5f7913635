import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import { Separator } from '@/components/ui/separator';
import AppLayout from '@/layouts/app-layout';
import TabLayout from '@/layouts/TabLayout';
import { BreadcrumbItem, NavItem, SchoolClass, User } from '@/types';
import { Head, usePage } from '@inertiajs/react';
import { Edit, MoreHorizontal, Search, Trash } from 'lucide-react';
import { useEffect, useState } from 'react';
import Create from './Create';
import DeleteStudent from './Delete';
import EditStudent from './Edit';

interface StudentUser extends User {
    pin: number;
}

interface Props {
    students: {
        data: StudentUser[];
        current_page: number;
        last_page: number;
        per_page: number;
        total: number;
        from: number;
        to: number;
    };
}

function Index({ students }: Props) {
    const classes = usePage().props.classes as SchoolClass[];
    const class_id = usePage().props.class_id as number;
    const className = usePage().props.className as string;
    const [searchTerm, setSearchTerm] = useState('');
    const [filteredStudents, setFilteredStudents] = useState<StudentUser[]>(students.data);
    const [editingStudent, setEditingStudent] = useState<StudentUser | null>(null);
    const [deletingStudent, setDeletingStudent] = useState<StudentUser | null>(null);

    // Update filtered students when search term changes
    useEffect(() => {
        if (searchTerm.trim() === '') {
            setFilteredStudents(students.data);
        } else {
            const filtered = students.data.filter((student) => student.name.toLowerCase().includes(searchTerm.toLowerCase()));
            setFilteredStudents(filtered);
        }
    }, [searchTerm, students.data]);

    const breadcrumbs: BreadcrumbItem[] = [
        {
            title: 'Students',
            href: '/students',
        },
    ];

    const sidebarNavItems: NavItem[] = classes.map((classItem: SchoolClass) => ({
        title: classItem.name,
        href: route('students.index', { class_id: classItem.id }),
        route: route('students.index', { class_id: classItem.id }),
        isActive: classItem.id == class_id,
    }));

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Students" />
            <TabLayout title="Students" description="Manage your students" sidebarNavItems={sidebarNavItems}>
                <div className="w-full">
                    <div className="mx-auto w-full px-4 sm:px-6 lg:px-8">
                        <div className="mb-6 flex items-center justify-between">
                            <h2 className="text-2xl font-semibold">{className}</h2>
                            <Create />
                        </div>
                        <Separator className="my-6" />

                        {/* Search and filter section */}
                        <div className="mb-6">
                            <div className="relative">
                                <Search className="absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 text-gray-500" />
                                <Input
                                    placeholder="Search students by name..."
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                    className="pl-10"
                                />
                            </div>
                        </div>

                        {/* Student list section */}
                        {students.data.length > 0 && (
                            <div className="overflow-hidden rounded-lg border border-gray-200 bg-white shadow">
                                <table className="min-w-full divide-y divide-gray-200">
                                    <thead className="bg-gray-50">
                                        <tr>
                                            <th
                                                scope="col"
                                                className="px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase"
                                            >
                                                Name
                                            </th>
                                            <th
                                                scope="col"
                                                className="px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase"
                                            >
                                                PIN
                                            </th>
                                            <th
                                                scope="col"
                                                className="px-6 py-3 text-right text-xs font-medium tracking-wider text-gray-500 uppercase"
                                            >
                                                Actions
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody className="divide-y divide-gray-200 bg-white">
                                        {filteredStudents.map((student) => (
                                            <tr key={student.id} className="hover:bg-gray-50">
                                                <td className="px-6 py-4 whitespace-nowrap">
                                                    <div className="flex items-center">
                                                        <div className="flex h-10 w-10 flex-shrink-0 items-center justify-center rounded-full bg-blue-950 text-sm font-bold text-white">
                                                            {student.name.charAt(0).toUpperCase()}
                                                        </div>
                                                        <div className="ml-4">
                                                            <div className="text-sm font-medium text-gray-900">{student.name}</div>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td className="px-6 py-4 text-sm whitespace-nowrap text-gray-500">{student.pin}</td>
                                                <td className="px-6 py-4 text-right text-sm whitespace-nowrap">
                                                    <DropdownMenu>
                                                        <DropdownMenuTrigger className="cursor-pointer rounded-md p-1 text-gray-400 hover:bg-gray-100">
                                                            <MoreHorizontal className="h-5 w-5" />
                                                        </DropdownMenuTrigger>
                                                        <DropdownMenuContent align="end">
                                                            <DropdownMenuItem className="cursor-pointer" onClick={() => setEditingStudent(student)}>
                                                                <Edit className="mr-2 h-4 w-4" />
                                                                Edit
                                                            </DropdownMenuItem>
                                                            <DropdownMenuItem
                                                                variant="destructive"
                                                                className="cursor-pointer"
                                                                onClick={() => setDeletingStudent(student)}
                                                            >
                                                                <Trash className="mr-2 h-4 w-4" />
                                                                Delete
                                                            </DropdownMenuItem>
                                                        </DropdownMenuContent>
                                                    </DropdownMenu>
                                                </td>
                                            </tr>
                                        ))}
                                    </tbody>
                                </table>
                                {filteredStudents.length === 0 && searchTerm !== '' && (
                                    <div className="p-6 text-center text-gray-500">No students found matching "{searchTerm}"</div>
                                )}
                            </div>
                        )}

                        {/* Empty state when no students */}
                        {students.data.length === 0 && (
                            <div className="flex flex-col items-center justify-center rounded-lg border-2 border-dashed border-gray-300 p-12 text-center">
                                <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth={2}
                                        d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"
                                    />
                                </svg>
                                <h3 className="mt-2 text-sm font-medium text-gray-900">No students</h3>
                                <p className="mt-1 text-sm text-gray-500">Get started by adding a new student to this class.</p>
                                <div className="mt-6">
                                    <Create />
                                </div>
                            </div>
                        )}

                        {/* Pagination */}
                        {students && students.last_page > 1 && searchTerm === '' && (
                            <div className="mt-6 flex items-center justify-between">
                                <div className="text-sm text-gray-700">
                                    Showing {students.from} to {students.to} of {students.total} results
                                </div>
                                <div className="flex space-x-2">
                                    {Array.from({ length: students.last_page }, (_, i) => i + 1).map((page) => (
                                        <a
                                            key={page}
                                            href={`/students?class_id=${class_id}&page=${page}`}
                                            className={`rounded px-3 py-1 ${
                                                page === students.current_page
                                                    ? 'bg-blue-950 text-white'
                                                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                                            }`}
                                        >
                                            {page}
                                        </a>
                                    ))}
                                </div>
                            </div>
                        )}
                    </div>
                </div>

                {/* Edit Dialog */}
                {editingStudent && (
                    <EditStudent
                        student={editingStudent}
                        open={!!editingStudent}
                        onOpenChange={(open: boolean) => !open && setEditingStudent(null)}
                    />
                )}

                {/* Delete Dialog */}
                {deletingStudent && (
                    <DeleteStudent
                        student={deletingStudent}
                        open={!!deletingStudent}
                        onOpenChange={(open: boolean) => !open && setDeletingStudent(null)}
                    />
                )}
            </TabLayout>
        </AppLayout>
    );
}

export default Index;
