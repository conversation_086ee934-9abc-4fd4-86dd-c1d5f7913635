<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Home Page</title>
        <link rel="stylesheet" href="styles.css">
        <script src="auth.js"></script>

        <style>
            :root {
                --color-orange: #FF9800;
                --color-yellow: #ffd93d;
                --color-green: #4caf50;
                --color-purple: #9C27B0;
                --color-gray: #F5F5F5;
                --color-text-gray: #666;
            }
            
            body {
                background-color: var(--color-gray);
                margin: 0;
                padding: 0;
                font-family: "Fredoka", sans-serif;
            }
            
            .container {
                max-width: 800px;
                margin: 0 auto;
                padding: 0;
            }
            
            .header {
                background-color: white;
                padding: 20px;
                border-bottom-left-radius: 30px;
                border-bottom-right-radius: 30px;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
                margin-bottom: 20px;
            }
            
            .profile-section {
                display: flex;
                align-items: center;
                margin-bottom: 20px;
                justify-content: space-between;
            }
            
            .profile-left {
                display: flex;
                align-items: center;
            }
            
            .avatar {
                width: 50px;
                height: 50px;
                border-radius: 25px;
                background-color: var(--color-blue);
                display: flex;
                align-items: center;
                justify-content: center;
                overflow: hidden;
                cursor: pointer;
            }
            
            .avatar img {
                width: 100%;
                height: 100%;
                object-fit: cover;
            }
            
            .welcome-text {
                flex: 1;
                margin-left: 15px;
                text-align: left;
            }
            
            .greeting {
                color: var(--color-text-gray);
                font-weight: 400;
                margin: 0;
            }
            
            .username {
                color: var(--color-blue);
                font-weight: 700;
                margin: 0;
                font-size: 1.25rem;
            }
            
            .notification-badge {
                padding: 8px;
                background-color: #F0F0F0;
                border-radius: 20px;
                cursor: pointer;
                margin-left: auto;
            }
            
            .stats-container {
                display: flex;
                justify-content: space-between;
                margin-top: 10px;
                gap: 15px;
            }
            
            .stat-card {
                background-color: white;
                padding: 15px;
                border-radius: 15px;
                align-items: center;
                width: calc(33.33% - 10px);
                box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12);
                text-align: center;
            }
            
            .stat-icon {
                width: 24px;
                height: 24px;
                margin: 0 auto;
            }
            
            .stat-number {
                font-size: 18px;
                font-weight: 600;
                color: var(--color-blue);
                margin: 5px 0;
            }
            
            .stat-label {
                font-size: 12px;
                font-weight: 400;
                color: var(--color-text-gray);
                margin: 2px 0;
            }
            
            .main-content {
                padding: 20px;
            }
            
            .title {
                color: var(--color-blue);
                font-weight: 700;
                text-align: center;
                margin: 10px 0;
                font-size: 1.5rem;
            }
            
            .subtitle {
                color: var(--color-text-gray);
                font-weight: 500;
                text-align: center;
                margin-bottom: 20px;
            }
            
            .grid {
                display: flex;
                flex-wrap: wrap;
                justify-content: space-between;
                margin-top: 10px;
            }
            
            .card-wrapper {
                width: calc(50% - 10px);
                margin-bottom: 20px;
                border-radius: 16px;
                cursor: pointer;
            }
            
            .card {
                border-radius: 16px;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
                height: 100%;
            }
            
            .card-content {
                align-items: center;
                padding: 20px;
                text-align: center;
            }
            
            .card-icon {
                width: 40px;
                height: 40px;
                margin: 0 auto;
            }
            
            .card-text {
                color: white;
                font-weight: 600;
                margin-top: 10px;
                font-size: 1.25rem;
            }
            
            .subjects-text {
                color: rgba(255, 255, 255, 0.8);
                font-size: 12px;
                font-weight: 400;
                margin-top: 5px;
            }
            
            .blue-bg { background-color: var(--color-blue); }
            .orange-bg { background-color: var(--color-orange); }
            .yellow-bg { background-color: var(--color-yellow); }
            .green-bg { background-color: var(--color-green); }
            .purple-bg { background-color: var(--color-purple); }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <div class="profile-section">
                    <div class="profile-left">
                        <div class="avatar" id="profileAvatar">
                            <img src="assets/welcome/male.png" alt="Profile">
                        </div>
                        <div class="welcome-text">
                            <p class="greeting">Welcome back,</p>
                            <p class="username user-name"
                                id="usernameDisplay">Student!
                                👋</p>
                        </div>
                    </div>
                    <div class="notification-badge logout-button">
                        <img src="assets/icons/logout.png" width="20"
                            height="20" alt="Notification" onclick="logout()">
                    </div>
                </div>

                <!-- Stats Section -->
                <div class="stats-container" id="statsContainer">
                    <div class="stat-card">
                        <img src="assets/icons/book.png" alt="Lessons"
                            class="stat-icon">
                        <p class="stat-number" id="lessonsCount">12</p>
                        <p class="stat-label">Lessons</p>
                    </div>
                    <div class="stat-card">
                        <img src="assets/icons/star.png" alt="Progress"
                            class="stat-icon">
                        <p class="stat-number" id="progressPercent">45%</p>
                        <p class="stat-label">Progress</p>
                    </div>
                    <div class="stat-card">
                        <img src="assets/icons/award.png" alt="Awards"
                            class="stat-icon">
                        <p class="stat-number" id="awardsCount">3</p>
                        <p class="stat-label">Awards</p>
                    </div>
                </div>
            </div>

            <!-- Main Content -->
            <div class="main-content">
                <h1 class="title">Pick Your Class! 🎓</h1>
                <p class="subtitle">Choose your Class to start learning</p>

                <div class="grid" id="classGrid">
                    <!-- Hard-coded class cards -->
                    <div class="card-wrapper" onclick="selectClass(1)">
                        <div class="card blue-bg">
                            <div class="card-content">
                                <img src="assets/icons/book.png"
                                    alt="Standard 1" class="card-icon">
                                <p class="card-text">Standard 1</p>
                                <p class="subjects-text">3 Subjects</p>
                            </div>
                        </div>
                    </div>

                    <div class="card-wrapper" onclick="selectClass(2)">
                        <div class="card orange-bg">
                            <div class="card-content">
                                <img src="assets/icons/book.png"
                                    alt="Standard 2" class="card-icon">
                                <p class="card-text">Standard 2</p>
                                <p class="subjects-text">4 Subjects</p>
                            </div>
                        </div>
                    </div>

                    <div class="card-wrapper" onclick="selectClass(4)">
                        <div class="card yellow-bg">
                            <div class="card-content">
                                <img src="assets/icons/book.png"
                                    alt="Standard 4" class="card-icon">
                                <p class="card-text">Standard 4</p>
                                <p class="subjects-text">6 Subjects</p>
                            </div>
                        </div>
                    </div>

                    <div class="card-wrapper" onclick="goToNthano()">
                        <div class="card purple-bg">
                            <div class="card-content">
                                <img src="assets/welcome/tiger.png" alt="Nthano"
                                    class="card-icon">
                                <p class="card-text">Nthano</p>
                                <p class="subjects-text">2 Stories</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <script>
            // Function to handle class selection
            function selectClass(classId) {
                window.location.href = `subjects/standard-${classId}.html`;
            }
            
            // Function to go to Nthano (stories) page
            function goToNthano() {
                window.location.href = 'nthano.html';
            }
        </script>
    </body>
</html>