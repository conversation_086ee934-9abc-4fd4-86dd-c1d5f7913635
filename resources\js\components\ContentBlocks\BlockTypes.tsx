// These are the content block types supported by our system

import { ContentBlockType } from '@/types';
import { Archive, Heading1, Heading2, Heading3, Image, List, ListOrdered, ListVideo, Mic, Type } from 'lucide-react';

export interface BlockTypeDefinition {
    type: ContentBlockType;
    icon: React.ReactNode;
    label: string;
    description: string;
    hasMedia: boolean;
    hasAudio: boolean;
    hasContent: boolean;
    hasChildren: boolean;
    allowedAsChild: boolean;
    parentTypes?: ContentBlockType[];
}

export const BLOCK_TYPES: Record<ContentBlockType, BlockTypeDefinition> = {
    heading: {
        type: 'heading',
        icon: <Heading1 className="h-5 w-5" />,
        label: 'Heading',
        description: 'A section title or subtitle',
        hasMedia: false,
        hasAudio: true,
        hasContent: true,
        hasChildren: false,
        allowedAsChild: false,
    },
    paragraph: {
        type: 'paragraph',
        icon: <List className="h-5 w-5" />,
        label: 'Paragraph',
        description: 'A block of text content',
        hasMedia: false,
        hasAudio: true,
        hasContent: true,
        hasChildren: false,
        allowedAsChild: false,
    },
    image: {
        type: 'image',
        icon: <Image className="h-5 w-5" />,
        label: 'Image',
        description: 'An image with optional caption',
        hasMedia: true,
        hasAudio: true,
        hasContent: true, // For caption
        hasChildren: false,
        allowedAsChild: false,
    },
    video: {
        type: 'video',
        icon: <ListVideo className="h-5 w-5" />,
        label: 'Video',
        description: 'A video with optional caption',
        hasMedia: true,
        hasAudio: false,
        hasContent: true, // For caption
        hasChildren: false,
        allowedAsChild: false,
    },
    audio: {
        type: 'audio',
        icon: <Mic className="h-5 w-5" />,
        label: 'Audio',
        description: 'An audio clip with optional caption',
        hasMedia: true,
        hasAudio: false,
        hasContent: true, // For caption
        hasChildren: false,
        allowedAsChild: false,
    },
    list: {
        type: 'list',
        icon: <List className="h-5 w-5" />,
        label: 'List',
        description: 'A bulleted list',
        hasMedia: false,
        hasAudio: true,
        hasContent: false,
        hasChildren: true,
        allowedAsChild: false,
    },
    listItem: {
        type: 'listItem',
        icon: <ListOrdered className="h-5 w-5" />,
        label: 'List Item',
        description: 'An item in a list',
        hasMedia: false,
        hasAudio: true,
        hasContent: true,
        hasChildren: false,
        allowedAsChild: true,
        parentTypes: ['list'],
    },
    zip: {
        type: 'zip',
        icon: <Archive className="h-5 w-5" />,
        label: 'HTML5 Content',
        description: 'Upload a zip file containing HTML5 content',
        hasMedia: true,
        hasAudio: true,
        hasContent: true,
        hasChildren: false,
        allowedAsChild: false,
    },
    wysiwyg: {
        type: 'wysiwyg',
        icon: <Type className="h-5 w-5" />,
        label: 'Rich Text',
        description: 'Create formatted content with headings, lists, links, and styling',
        hasMedia: false,
        hasAudio: true,
        hasContent: false, // Content is stored in attributes.html_content
        hasChildren: false,
        allowedAsChild: false,
    },
};

export const ROOT_BLOCK_TYPES: ContentBlockType[] = ['heading', 'paragraph', 'wysiwyg', 'image', 'video', 'audio', 'list', 'zip'];

export const CHILD_BLOCK_TYPES: ContentBlockType[] = ['listItem'];

export const getHeadingLevelOptions = () => [
    { value: '1', label: 'Heading 1', icon: <Heading1 className="h-5 w-5" /> },
    { value: '2', label: 'Heading 2', icon: <Heading2 className="h-5 w-5" /> },
    { value: '3', label: 'Heading 3', icon: <Heading3 className="h-5 w-5" /> },
];

export const getBlockTypeForParent = (parentType: ContentBlockType | null): ContentBlockType[] => {
    if (!parentType) {
        return ROOT_BLOCK_TYPES;
    }

    return Object.values(BLOCK_TYPES)
        .filter((blockType) => blockType.allowedAsChild && (!blockType.parentTypes || blockType.parentTypes.includes(parentType)))
        .map((blockType) => blockType.type);
};
