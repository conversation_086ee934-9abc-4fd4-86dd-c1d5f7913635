<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Standard 1 Subjects</title>
        <link rel="stylesheet" href="../../styles.css">
        <style>
        :root {
            --color-blue: #1c407c;
            --color-yellow: #ffd93d;
            --color-dark-yellow: #e6c235;
            --color-white: #ffffff;
            --color-red: #ff5252;
            --color-green: #4caf50;
            --color-dark-green: #388e3c;
            --color-gray: #F5F5F5;
            --color-text-gray: #666;
        }
        
        body {
            background-color: var(--color-yellow);
            margin: 0;
            padding: 0;
            font-family: "Fredoka", sans-serif;
            color: var(--color-blue);
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 0;
        }
        
        .header {
            padding: 16px;
            background-color: var(--color-yellow);
            padding-bottom: 20px;
        }
        
        .header-top {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
        }
        
        .back-button {
            background-color: var(--color-blue);
            margin-right: 8px;
            padding: 8px;
            border-radius: 10px;
            border: 2px solid var(--color-dark-yellow);
            border-top-width: 0;
            cursor: pointer;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .back-button img {
            width: 24px;
            height: 24px;
        }
        
        .header-title {
            font-weight: 700;
            font-size: 24px;
            color: var(--color-blue);
            flex: 1;
            margin-left: 16px;
        }
        
        .header-subtitle {
            font-weight: 400;
            font-size: 16px;
            color: var(--color-blue);
            margin-left: 40px;
        }
        
        .content {
            flex: 1;
            border-top-left-radius: 24px;
            border-top-right-radius: 24px;
            background-color: var(--color-white);
            padding: 16px;
            min-height: 80vh;
        }
        
        .progress-card {
            background-color: var(--color-blue);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 24px;
            color: var(--color-white);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }
        
        .progress-card-header {
            display: flex;
            align-items: center;
            margin-bottom: 16px;
        }
        
        .progress-card-icon {
            width: 32px;
            height: 32px;
            margin-right: 12px;
        }
        
        .progress-card-title {
            font-weight: 700;
            font-size: 20px;
        }
        
        .progress-stats {
            display: flex;
            justify-content: space-between;
            margin-bottom: 16px;
        }
        
        .progress-stat-item {
            text-align: center;
            flex: 1;
        }
        
        .progress-stat-value {
            font-weight: 700;
            font-size: 24px;
            margin-bottom: 4px;
        }
        
        .progress-stat-label {
            font-weight: 400;
            font-size: 14px;
            opacity: 0.8;
        }
        
        .progress-bar-container {
            height: 12px;
            background-color: rgba(255, 255, 255, 0.2);
            border-radius: 6px;
            margin-bottom: 8px;
            overflow: hidden;
        }
        
        .progress-bar {
            height: 100%;
            background-color: var(--color-yellow);
            border-radius: 6px;
            width: 0%;
            transition: width 1s ease-in-out;
        }
        
        .progress-label {
            font-size: 14px;
            opacity: 0.8;
            margin-bottom: 16px;
        }
        
        .quiz-button {
            background-color: var(--color-yellow);
            color: var(--color-blue);
            border: none;
            border-radius: 10px;
            padding: 12px 16px;
            font-weight: 700;
            font-size: 16px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            border: 2px solid var(--color-dark-yellow);
            border-top-width: 0;
        }
        
        .quiz-button img {
            width: 20px;
            height: 20px;
            margin-right: 8px;
        }
        
        .section-title {
            font-weight: 600;
            font-size: 20px;
            color: var(--color-blue);
            margin-bottom: 16px;
        }
        
        .units-container {
            display: flex;
            flex-direction: column;
            gap: 16px;
        }
        
        .unit-item {
            background-color: var(--color-gray);
            border-radius: 16px;
            padding: 16px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            cursor: pointer;
            animation: fadeIn 0.5s ease-in-out;
        }
        
        .unit-header {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
        }
        
        .unit-icon {
            width: 40px;
            height: 40px;
            background-color: var(--color-blue);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--color-yellow);
            font-weight: 700;
            font-size: 18px;
            margin-right: 12px;
        }
        
        .unit-title-container {
            flex: 1;
        }
        
        .unit-title {
            font-weight: 600;
            font-size: 18px;
            color: var(--color-blue);
            margin-bottom: 4px;
        }
        
        .unit-description {
            font-weight: 400;
            font-size: 14px;
            color: var(--color-text-gray);
        }
        
        .unit-progress {
            margin-bottom: 12px;
        }
        
        .unit-progress-text {
            display: flex;
            justify-content: space-between;
            font-size: 14px;
            color: var(--color-text-gray);
            margin-bottom: 4px;
        }
        
        .unit-progress-bar-container {
            height: 8px;
            background-color: #E0E0E0;
            border-radius: 4px;
            overflow: hidden;
        }
        
        .unit-progress-bar {
            height: 100%;
            background-color: var(--color-green);
            border-radius: 4px;
            width: 0%;
            transition: width 1s ease-in-out;
        }
        
        .unit-stats {
            display: flex;
            gap: 16px;
        }
        
        .unit-stat {
            display: flex;
            align-items: center;
        }
        
        .unit-stat-icon {
            width: 16px;
            height: 16px;
            margin-right: 4px;
        }
        
        .unit-stat-text {
            font-size: 14px;
            color: var(--color-text-gray);
        }
        
        .completed-badge {
            background-color: var(--color-green);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
            margin-left: 8px;
        }
        
        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <div class="header-top">
                    <div class="back-button" onclick="goBack()">
                        <img src="../../assets/icons/arrowleft.png" alt="Back">
                    </div>
                    <h1 class="header-title">English</h1>
                </div>
                <p class="header-subtitle">Select a unit to continue</p>
            </div>

            <div class="content">
                <!-- Progress Card -->
                <div class="progress-card">
                    <div class="progress-card-header">
                        <img src="../../assets/icons/book.png" alt="Book"
                            class="progress-card-icon">
                        <h2 class="progress-card-title">English</h2>
                    </div>

                    <div class="progress-stats">
                        <div class="progress-stat-item">
                            <p class="progress-stat-value"
                                id="completedTopics">8</p>
                            <p class="progress-stat-label">Lessons</p>
                        </div>
                        <div class="progress-stat-item">
                            <p class="progress-stat-value"
                                id="quizzesTaken">3</p>
                            <p class="progress-stat-label">Quizzes</p>
                        </div>
                        <div class="progress-stat-item">
                            <p class="progress-stat-value"
                                id="averageScore">85%</p>
                            <p class="progress-stat-label">Awards</p>
                        </div>
                    </div>

                    <div class="progress-bar-container">
                        <div class="progress-bar" id="subjectProgressBar"
                            style="width: 67%"></div>
                    </div>
                    <p class="progress-label">English</p>

                    <button class="quiz-button" onclick="takeQuiz()">
                        <img src="../../assets/icons/book.png" alt="Quiz">
                        Take Quiz
                    </button>
                </div>

                <h3 class="section-title">All units📚</h3>

                <div class="units-container" id="unitsContainer">
                    <!-- Hard-coded unit items -->
                    <div class="unit-item"
                        onclick="window.location.href='english/unit1.html'"
                        style="animation-delay: 500ms">
                        <div class="unit-header">
                            <div class="unit-icon">1</div>
                            <div class="unit-title-container">
                                <h3 class="unit-title">
                                    Unit 1
                                    <span
                                        class="completed-badge">Completed</span>
                                </h3>
                                <p class="unit-description">
                                    My new friends
                                </p>
                            </div>
                        </div>

                        <div class="unit-progress">
                            <div class="unit-progress-text">
                                <span>6/6 completed</span>
                                <span>100%</span>
                            </div>
                            <div class="unit-progress-bar-container">
                                <div class="unit-progress-bar"
                                    style="width: 100%"></div>
                            </div>
                        </div>

                        <div class="unit-stats">
                            <div class="unit-stat">
                                <img src="../../assets/icons/book.png"
                                    alt="Lessons" class="unit-stat-icon">
                                <span class="unit-stat-text">6 topics</span>
                            </div>
                            <div class="unit-stat">
                                <span class="material-icons"
                                    style="font-size: 16px; margin-right: 4px; color: var(--color-text-gray);">schedule</span>
                                <span class="unit-stat-text">35 Minutes</span>
                            </div>
                        </div>
                    </div>

                    <div class="unit-item"
                        onclick="window.location.href='english/unit2.html'"
                        style="animation-delay: 600ms">
                        <div class="unit-header">
                            <div class="unit-icon">2</div>
                            <div class="unit-title-container">
                                <h3 class="unit-title">
                                    Unit 2
                                </h3>
                                <p class="unit-description">
                                    Taking care of the body
                                </p>
                            </div>
                        </div>

                        <div class="unit-progress">
                            <div class="unit-progress-text">
                                <span>2/4 completed</span>
                                <span>50%</span>
                            </div>
                            <div class="unit-progress-bar-container">
                                <div class="unit-progress-bar"
                                    style="width: 50%"></div>
                            </div>
                        </div>

                        <div class="unit-stats">
                            <div class="unit-stat">
                                <img src="../../assets/icons/book.png"
                                    alt="Lessons" class="unit-stat-icon">
                                <span class="unit-stat-text">4 Maphunziro</span>
                            </div>
                            <div class="unit-stat">
                                <span class="material-icons"
                                    style="font-size: 16px; margin-right: 4px; color: var(--color-text-gray);">schedule</span>
                                <span class="unit-stat-text">25 Mphindi</span>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>

        <script>
        function goBack() {
            window.history.back();
        }
        
        function takeQuiz() {
            window.location.href = '../quiz/quiz.html?class_id=1&subject_id=3';
        }
        </script>
    </body>
</html>
