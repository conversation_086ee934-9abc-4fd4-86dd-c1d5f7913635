<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('content_blocks', function (Blueprint $table) {
            $table->id();
            $table->foreignId('lesson_id')->constrained('lessons')->onDelete('cascade');
            $table->string('type'); // heading, paragraph, image, video, audio, list, listItem
            $table->text('content')->nullable(); // For text-based content
            $table->string('media_path')->nullable(); // For images, videos, audio files
            $table->string('audio_path')->nullable(); // For optional audio narration
            $table->integer('order')->default(0); // For ordering content blocks
            $table->foreignId('parent_id')->nullable(); // For nested structures like lists
            $table->json('attributes')->nullable(); // For additional attributes like heading level, etc.
            $table->timestamps();

            // Add foreign key for self-referencing relationship
            $table->foreign('parent_id')
                ->references('id')
                ->on('content_blocks')
                ->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('content_blocks');
    }
};
