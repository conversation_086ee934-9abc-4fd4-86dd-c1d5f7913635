import { ContentBlock } from '@/types';
import { useEffect, useState } from 'react';

interface ContentBlockViewerProps {
    block: ContentBlock;
}

export function ContentBlockViewer({ block }: ContentBlockViewerProps) {
    const [mediaUrl, setMediaUrl] = useState<string | null>(null);
    const [audioUrl, setAudioUrl] = useState<string | null>(null);

    useEffect(() => {
        // Set media URL for image, video, or audio blocks
        if (block.media_path && ['image', 'video', 'audio'].includes(block.type)) {
            setMediaUrl(`/storage/${block.media_path}`);
        }

        // Set audio URL for narration if available
        if (block.audio_path) {
            setAudioUrl(`/storage/${block.audio_path}`);
        }
    }, [block]);

    // Function to render heading with dynamic heading level
    const renderHeading = () => {
        const level = Number((block.attributes?.level as string) || '1');

        // Safely render the appropriate heading level
        switch (level) {
            case 1:
                return <h1 className="mb-4 font-bold">{block.content}</h1>;
            case 2:
                return <h2 className="mb-4 font-bold">{block.content}</h2>;
            case 3:
                return <h3 className="mb-4 font-bold">{block.content}</h3>;
            case 4:
                return <h4 className="mb-4 font-bold">{block.content}</h4>;
            case 5:
                return <h5 className="mb-4 font-bold">{block.content}</h5>;
            case 6:
                return <h6 className="mb-4 font-bold">{block.content}</h6>;
            default:
                return <h1 className="mb-4 font-bold">{block.content}</h1>;
        }
    };

    const renderContent = () => {
        switch (block.type) {
            case 'heading':
                return renderHeading();

            case 'paragraph':
                return <p className="mb-4 leading-relaxed">{block.content}</p>;

            case 'image':
                return (
                    <div className="mb-4">
                        {mediaUrl && (
                            <figure>
                                <img src={mediaUrl} alt={block.content || 'Lesson image'} className="mx-auto rounded" />
                                {block.content && <figcaption className="mt-2 text-center text-sm text-gray-500">{block.content}</figcaption>}
                            </figure>
                        )}
                    </div>
                );

            case 'video':
                return (
                    <div className="mb-4">
                        {mediaUrl && (
                            <figure>
                                <video
                                    src={mediaUrl}
                                    controls
                                    className="mx-auto rounded"
                                    poster={(block.attributes?.thumbnail as string) || undefined}
                                />
                                {block.content && <figcaption className="mt-2 text-center text-sm text-gray-500">{block.content}</figcaption>}
                            </figure>
                        )}
                    </div>
                );

            case 'audio':
                return (
                    <div className="mb-4">
                        {block.content && <p className="mb-2">{block.content}</p>}
                        {mediaUrl && <audio src={mediaUrl} controls className="w-full" />}
                    </div>
                );

            case 'list':
                return (
                    <ul className="mb-4 list-disc pl-6">
                        {block.children &&
                            block.children.map((item) => (
                                <li key={item.id} className="mb-1">
                                    {item.content}
                                </li>
                            ))}
                    </ul>
                );

            case 'listItem':
                return <li className="mb-1">{block.content}</li>;

            default:
                return <div className="mb-4">{block.content}</div>;
        }
    };

    return (
        <div className="content-block">
            {renderContent()}

            {/* Audio narration player */}
            {audioUrl && (
                <div className="mt-1 flex items-center space-x-2 text-sm">
                    <span className="text-gray-500">Narration:</span>
                    <audio src={audioUrl} controls className="h-8 w-48" />
                </div>
            )}
        </div>
    );
}
