import { Button } from '@/components/ui/button';
import { Calendar } from 'lucide-react';
import { addDays, format } from 'date-fns';
import { useState } from 'react';

interface DateRange {
    from: Date;
    to: Date;
}

interface DateRangePickerProps {
    value: DateRange;
    onChange: (range: DateRange) => void;
    className?: string;
}

const presetRanges = [
    {
        label: 'Last 7 days',
        range: {
            from: addDays(new Date(), -7),
            to: new Date(),
        },
    },
    {
        label: 'Last 30 days',
        range: {
            from: addDays(new Date(), -30),
            to: new Date(),
        },
    },
    {
        label: 'Last 90 days',
        range: {
            from: addDays(new Date(), -90),
            to: new Date(),
        },
    },
    {
        label: 'This month',
        range: {
            from: new Date(new Date().getFullYear(), new Date().getMonth(), 1),
            to: new Date(),
        },
    },
];

export function DateRangePicker({ value, onChange, className }: DateRangePickerProps) {
    const [isOpen, setIsOpen] = useState(false);

    const formatRange = (range: DateRange) => {
        return `${format(range.from, 'MMM d, yyyy')} - ${format(range.to, 'MMM d, yyyy')}`;
    };

    const handlePresetSelect = (preset: typeof presetRanges[0]) => {
        onChange(preset.range);
        setIsOpen(false);
    };

    return (
        <div className={`relative ${className}`}>
            <Button
                variant="outline"
                onClick={() => setIsOpen(!isOpen)}
                className="w-full justify-start text-left font-normal"
            >
                <Calendar className="mr-2 h-4 w-4" />
                {formatRange(value)}
            </Button>
            
            {isOpen && (
                <div className="absolute top-full left-0 z-50 mt-1 w-64 rounded-md border bg-white p-2 shadow-lg">
                    <div className="space-y-1">
                        {presetRanges.map((preset) => (
                            <button
                                key={preset.label}
                                onClick={() => handlePresetSelect(preset)}
                                className="w-full rounded px-2 py-1 text-left text-sm hover:bg-gray-100"
                            >
                                {preset.label}
                            </button>
                        ))}
                    </div>
                </div>
            )}
        </div>
    );
}
