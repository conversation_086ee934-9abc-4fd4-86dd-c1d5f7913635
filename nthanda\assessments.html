<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>My Assessments</title>

        <link rel="stylesheet" href="styles.css">
        <style>
        :root {
            --color-blue: #1c407c;
            --color-yellow: #ffd93d;
            --color-dark-yellow: #e6c235;
            --color-white: #ffffff;
            --color-red: #ff5252;
            --color-green: #4caf50;
            --color-dark-green: #388e3c;
            --color-gray: #F5F5F5;
            --color-text-gray: #666;
        }
        
        body {
            background-color: var(--color-yellow);
            margin: 0;
            padding: 0;
            font-family: "Fredoka", sans-serif;
            color: var(--color-blue);
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 0;
        }
        
        .header {
            padding: 16px;
            background-color: var(--color-yellow);
            padding-bottom: 20px;
        }
        
        .header-top {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
        }
        
        .back-button {
            background-color: var(--color-blue);
            margin-right: 8px;
            padding: 8px;
            border-radius: 10px;
            border: 2px solid var(--color-dark-yellow);
            border-top-width: 0;
            cursor: pointer;
            display: flex;
            justify-content: center;
            align-items: center;
            text-decoration: none;
        }
        
        .back-button::before {
            content: "←";
            color: var(--color-yellow);
            font-size: 20px;
            font-weight: bold;
        }
        
        .header-title {
            font-weight: 700;
            font-size: 28px;
            color: var(--color-blue);
            flex: 1;
            margin: 0;
            margin-left: 16px;
        }
        
        .header-subtitle {
            font-weight: 500;
            font-size: 24px;
            color: var(--color-blue);
            margin-left: 40px;
            margin-bottom: 0;
        }
        
        .content {
            flex: 1;
            border-top-left-radius: 24px;
            border-top-right-radius: 24px;
            background-color: var(--color-white);
            padding: 16px;
            min-height: 80vh;
        }
        
        .filter-section {
            background-color: var(--color-blue);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 24px;
            color: var(--color-white);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }
        
        .filter-row {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            margin-bottom: 16px;
            gap: 12px;
        }
        
        .filter-column {
            flex: 1;
            min-width: 200px;
        }
        
        .filter-label {
            font-weight: 600;
            margin-bottom: 8px;
            display: block;
            color: var(--color-white);
        }
        
        .form-select {
            width: 100%;
            padding: 10px;
            border-radius: 8px;
            border: 2px solid var(--color-dark-yellow);
            background-color: var(--color-white);
            color: var(--color-blue);
            font-weight: 500;
            font-size: 15px;
            appearance: none;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%231c407c' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px 12px;
        }
        
        .reset-button {
            background-color: var(--color-yellow);
            color: var(--color-blue);
            border: none;
            border-radius: 10px;
            padding: 12px 16px;
            font-weight: 700;
            font-size: 16px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            border: 2px solid var(--color-dark-yellow);
            border-top-width: 0;
        }
        
        .reset-button:hover {
            background-color: var(--color-dark-yellow);
        }
        
        .section-title {
            font-weight: 600;
            font-size: 20px;
            color: var(--color-blue);
            margin-bottom: 16px;
        }
        
        .assessments-container {
            display: flex;
            flex-direction: column;
            gap: 16px;
        }
        
        .assessment-item {
            background-color: var(--color-gray);
            border-radius: 16px;
            padding: 16px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            cursor: pointer;
            animation: fadeIn 0.5s ease-in-out;
            text-decoration: none;
        }
        
        .assessment-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }
        
        .assessment-header {
            display: flex;
            flex-direction: row;
            align-items: center;
            margin-bottom: 12px;
        }
        
        .assessment-icon {
            width: 40px;
            height: 40px;
            background-color: var(--color-blue);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--color-yellow);
            font-weight: 700;
            font-size: 18px;
            margin-right: 12px;
        }
        
        .assessment-title-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }
        
        .assessment-title {
            font-weight: 600;
            font-size: 18px;
            color: var(--color-blue);
            margin: 0;
        }
        
        .assessment-subject {
            font-weight: 400;
            font-size: 14px;
            color: var(--color-text-gray);
            margin: 4px 0 0 0;
        }
        
        .assessment-description {
            font-weight: 400;
            font-size: 14px;
            color: var(--color-text-gray);
            margin-bottom: 12px;
        }
        
        .assessment-stats {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
            margin-right: 8px;
        }
        
        .status-not-started {
            background-color: var(--color-text-gray);
            color: white;
        }
        
        .status-in-progress {
            background-color: var(--color-yellow);
            color: var(--color-blue);
        }

        .status-completed {
            background-color: var(--color-green);
            color: var(--color-white);
        }
        
        .status-passed {
            background-color: var(--color-green);
            color: white;
        }
        
        .status-failed {
            background-color: var(--color-red);
            color: white;
        }
        
        .assessment-score {
            font-weight: 700;
            font-size: 18px;
        }
        
        .score-passed {
            color: var(--color-green);
        }
        
        .score-failed {
            color: var(--color-red);
        }
        
        .loading-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 40px 0;
        }
        
        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid rgba(28, 64, 124, 0.1);
            border-left-color: var(--color-blue);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        .loading-text {
            margin-top: 16px;
            color: var(--color-blue);
            font-weight: 500;
        }
        
        .empty-state {
            background-color: var(--color-gray);
            border-radius: 16px;
            padding: 40px;
            text-align: center;
            display: none;
        }
        
        .empty-icon {
            font-size: 48px;
            margin-bottom: 16px;
            color: var(--color-blue);
        }
        
        .empty-title {
            font-weight: 600;
            font-size: 20px;
            color: var(--color-blue);
            margin-bottom: 8px;
        }
        
        .empty-description {
            color: var(--color-text-gray);
            margin-bottom: 24px;
        }
        
        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        @media (max-width: 768px) {
            .filter-row {
                flex-direction: column;
            }
            
            .form-select {
                padding: 10px;
            }
            
            .header-title {
                font-size: 24px;
            }
            
            .header-subtitle {
                font-size: 18px;
                margin-left: 32px;
            }
        }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <div class="header-top">
                    <a class="back-button" href="main.html"
                        title="Back to Main"></a>
                    <h1 class="header-title">My Assessments</h1>
                </div>
                <p class="header-subtitle">Find and take assessments</p>
            </div>

            <div class="content">
                <!-- Filter Section -->
                <div class="filter-section">
                    <div class="filter-row">
                        <div class="filter-column">
                            <label class="filter-label">Filter by
                                Subject</label>
                            <select class="form-select" id="subject-filter">
                                <option value="all">All Subjects</option>
                                <!-- Subject options will be loaded dynamically -->
                            </select>
                        </div>
                        <div class="filter-column">
                            <label class="filter-label">Filter by Status</label>
                            <select class="form-select" id="status-filter">
                                <option value="all">All Status</option>
                                <option value="not_started">Not Started</option>
                                <option value="started">In Progress</option>
                                <option value="passed">Passed</option>
                                <option value="failed">Failed</option>
                            </select>
                        </div>
                        <div class="filter-column">
                            <label class="filter-label">Sort By</label>
                            <select class="form-select" id="sort-by">
                                <option value="newest">Newest First</option>
                                <option value="oldest">Oldest First</option>
                                <option value="title_asc">Title (A-Z)</option>
                                <option value="title_desc">Title (Z-A)</option>
                            </select>
                        </div>
                    </div>

                    <button class="reset-button" id="reset-filters">
                        Reset Filters
                    </button>
                </div>

                <!-- Loading State -->
                <div id="loading-container" class="loading-container">
                    <div class="spinner"></div>
                    <p class="loading-text">Loading your assessments...</p>
                </div>

                <!-- Empty State -->
                <div id="empty-state" class="empty-state">
                    <div class="empty-icon">📋</div>
                    <h3 class="empty-title">No Assessments Found</h3>
                    <p class="empty-description">
                        We couldn't find any assessments matching your
                        filters.<br>
                        Try changing your filter settings or check back later.
                    </p>
                    <button class="reset-button" id="reset-filters-empty">
                        Reset Filters
                    </button>
                </div>

                <h3 class="section-title">All Assessments 📝</h3>

                <!-- Assessment Cards Container -->
                <div id="assessments-container" class="assessments-container">
                    <!-- Assessment cards will be loaded dynamically -->
                </div>
            </div>
        </div>

        <script src="jquery.js"></script>
        <script src="axios.js"></script>
        <script src="auth.js"></script>
        <script src="content.js"></script>
        <script>
        // Get student ID and other params
        let studentId = getStudentDetails().id;
        let subjectId = getUrlParams("subject_id");

        // Configuration
        const API_URL = "https://lomoni.kaizen-mw.com/api";
        const AUTH_TOKEN = localStorage.getItem("auth_token");

        // State Variables
        let allAssessments = [];
        let subjects = new Set();
        let currentFilters = {
            subject: "all",
            status: "all",
            sort: "newest",
        };

        // Initialize the application when document is ready
        $(document).ready(function() {
            // Set up event listeners
            $("#subject-filter").on("change", updateFilters);
            $("#status-filter").on("change", updateFilters);
            $("#sort-by").on("change", updateFilters);
            $("#reset-filters, #reset-filters-empty").on("click", resetFilters);

            // Load assessments
            loadAssessments();
        });

        // Load assessments data from the API
        function loadAssessments() {
            $("#loading-container").show();
            $("#assessments-container").hide();
            $("#empty-state").hide();
            $(".section-title").hide();

            var user_id = getStudentDetails().id;
            var subject_id = getUrlParams("subject_id");

            axios
                .get(`${API_URL}/assessments/${user_id}/${subject_id}`)
                .then(function(response) {
                    if (!response.data.success) {
                        throw new Error(response.data.message || "Failed to load assessments");
                    }

                    allAssessments = response.data.assessments;

                    // Extract unique subjects for filter
                    subjects = new Set();
                    allAssessments.forEach((assessment) => {
                        if (assessment.subject) {
                            subjects.add(assessment.subject);
                        }
                    });

                    // Populate subject filter
                    populateSubjectFilter();

                    // Apply initial filters and display assessments
                    filterAndDisplayAssessments();
                })
                .catch(function(error) {
                    console.error("Failed to load assessments:", error);
                    showError("Failed to load assessments. Please try again later.");
                })
                .finally(function() {
                    $("#loading-container").hide();
                });
        }

        // Populate subject filter with unique subjects
        function populateSubjectFilter() {
            const $subjectFilter = $("#subject-filter");

            // Clear existing options except the first one
            $subjectFilter.find("option:not(:first)").remove();

            // Add subject options
            subjects.forEach(function(subject) {
                $subjectFilter.append(
                    $("<option>", {
                        value: subject,
                        text: subject,
                    })
                );
            });
        }

        // Update filters when user changes a filter
        function updateFilters() {
            currentFilters.subject = $("#subject-filter").val();
            currentFilters.status = $("#status-filter").val();
            currentFilters.sort = $("#sort-by").val();

            filterAndDisplayAssessments();
        }

        // Reset all filters to default values
        function resetFilters() {
            $("#subject-filter").val("all");
            $("#status-filter").val("all");
            $("#sort-by").val("newest");

            updateFilters();
        }

        // Filter and display assessments based on current filters
        function filterAndDisplayAssessments() {
            // Apply filters
            let filteredAssessments = allAssessments.filter(function(assessment) {
                // Filter by subject
                if (
                    currentFilters.subject !== "all" &&
                    assessment.subject !== currentFilters.subject
                ) {
                    return false;
                }

                // Filter by status
                if (
                    currentFilters.status !== "all" &&
                    assessment.status !== currentFilters.status
                ) {
                    return false;
                }

                return true;
            });

            // Apply sorting
            filteredAssessments.sort(function(a, b) {
                switch (currentFilters.sort) {
                    case "newest":
                        // Assuming there's a created_at or similar field
                        return new Date(b.created_at || 0) - new Date(a.created_at || 0);
                    case "oldest":
                        return new Date(a.created_at || 0) - new Date(b.created_at || 0);
                    case "title_asc":
                        return a.title.localeCompare(b.title);
                    case "title_desc":
                        return b.title.localeCompare(a.title);
                    default:
                        return 0;
                }
            });

            // Display filtered assessments
            displayAssessments(filteredAssessments);
        }

        // Display assessments in the UI
        function displayAssessments(assessments) {
            const $assessmentsContainer = $("#assessments-container");
            $assessmentsContainer.empty();

            if (assessments.length === 0) {
                $assessmentsContainer.hide();
                $(".section-title").hide();
                $("#empty-state").show();
                return;
            }

            $assessmentsContainer.show();
            $(".section-title").show();
            $("#empty-state").hide();

            assessments.forEach(function(assessment, index) {
                const $assessment = createAssessmentCard(assessment);
                $assessmentsContainer.append($assessment);
            });
        }

        // Create an assessment card element
        function createAssessmentCard(assessment) {
            // Determine the status text and class
            let statusClass = "status-not-started";
            let statusText = "Not Started";
            
            if (assessment.status === "passed") {
                statusClass = "status-passed";
                statusText = "Passed";
            } else {
                statusClass = "status-failed";
                statusText = "Failed";
            } 

            if(assessment.progress=="not_started"){
                statusClass = "status-not-started";
                statusText = "Not Started";
            } else if (assessment.progress=="started") {
                statusClass = "status-in-progress";
                statusText = "In Progress";
            }else if(assessment.progress=="completed"){
                statusClass = "status-completed";
                statusText = "Completed";
            }
            
            // Determine score display if available
            let scoreHtml = '';
            if (assessment.score !== null) {
                const scoreClass = assessment.status === "passed" ? "score-passed" : "score-failed";
                scoreHtml = `<div class="assessment-score ${scoreClass}">${Math.round(assessment.score)}%</div>`;
            }
            
            return `
                <a href="assessment.html?id=${assessment.id}" class="assessment-item">
                    <div class="assessment-header">
                        <div class="assessment-icon">${assessment.subject ? assessment.subject.charAt(0).toUpperCase() : "A"}</div>
                        <div class="assessment-title-container">
                            <h3 class="assessment-title">
                                ${assessment.title}
                                <span class="status-badge ${statusClass}">${statusText}</span>
                            </h3>
                            <p class="assessment-subject">
                                ${assessment.subject || "General Assessment"}
                                ${assessment.topic ? ` - ${assessment.topic}` : ""}
                            </p>
                        </div>
                    </div>
                    
                    <div class="assessment-description">
                        ${assessment.description || "No description available."}
                    </div>
                    
                    <div class="assessment-stats">
                        <div>
                            <i class="bi bi-book" style="margin-right: 6px; color: var(--color-text-gray);"></i>
                            <span style="color: var(--color-text-gray); font-size: 14px;">Assessment</span>
                        </div>
                        ${scoreHtml}
                    </div>
                </a>
            `;
        }

        // Show error message
        function showError(message) {
            $("#empty-state").show();
            $(".section-title").hide();
            $("#assessments-container").hide();

            const $emptyIcon = $("#empty-state .empty-icon");
            $emptyIcon.text("⚠️");

            const $title = $("#empty-state .empty-title");
            $title.text("Error");

            const $description = $("#empty-state .empty-description");
            $description.text(message);
        }
        </script>
    </body>
</html>