import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { User } from '@/types';
import { format } from 'date-fns';
import { Edit as EditIcon, MoreHorizontal, Trash } from 'lucide-react';

interface UserCardProps {
    user: User;
    onEdit: (user: User) => void;
    onDelete: (user: User) => void;
}

function UserCard({ user, onEdit, onDelete }: UserCardProps) {
    return (
        <div className="overflow-hidden rounded-lg border border-gray-200 bg-white shadow transition-all hover:shadow-md">
            <div className="bg-gray-100 p-4">
                <div className="flex items-center justify-center">
                    <div className="flex h-16 w-16 items-center justify-center rounded-full bg-blue-950 text-2xl font-bold text-white">
                        {user.name.charAt(0).toUpperCase()}
                    </div>
                </div>
            </div>
            <div className="p-4">
                <div className="flex items-center justify-between">
                    <h3 className="line-clamp-1 text-lg font-semibold text-gray-900">{user.name}</h3>
                </div>
                <p className="mb-3 line-clamp-1 text-sm text-gray-600">{user.email}</p>
                <p className="line-clamp-1 text-xs text-gray-500">Added on {format(new Date(user.created_at), 'PPp')}</p>
                <div className="mt-4 flex items-center justify-end">
                    <DropdownMenu>
                        <DropdownMenuTrigger className="cursor-pointer rounded-md p-1 text-gray-400 hover:bg-gray-100">
                            <MoreHorizontal className="h-5 w-5" />
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                            <DropdownMenuItem className="cursor-pointer" onClick={() => onEdit(user)}>
                                <EditIcon className="mr-2 h-4 w-4" />
                                Edit
                            </DropdownMenuItem>
                            <DropdownMenuItem variant="destructive" className="cursor-pointer" onClick={() => onDelete(user)}>
                                <Trash className="mr-2 h-4 w-4" />
                                Delete
                            </DropdownMenuItem>
                        </DropdownMenuContent>
                    </DropdownMenu>
                </div>
            </div>
        </div>
    );
}

export default UserCard;
