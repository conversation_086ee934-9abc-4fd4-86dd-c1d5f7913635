<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class AssessmentQuestionDetail extends Model
{
    protected $table = 'assessment_question_details';

    protected $fillable = [
        'assessment_result_id',
        'assessment_question_id',
        'student_answer',
        'is_correct',
    ];

    public function result()
    {
        return $this->belongsTo(AssessmentResult::class, 'assessment_result_id');
    }

    public function question()
    {
        return $this->belongsTo(AssessmentQuestion::class, 'assessment_question_id');
    }
}