<?php

namespace App\Http\Controllers;

use App\Models\Lesson;
use App\Models\Topic;
use App\Models\Unit;
use App\Models\Subject;
use App\Models\SchoolClass;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;
use Inertia\Inertia;

class LessonController extends Controller
{
    /**
     * Display a listing of lessons for a topic.
     */
    public function index(Request $request)
    {
        $request->validate([
            'topic_id' => 'required|integer|exists:topics,id',
        ]);

        $topic = Topic::findOrFail($request->topic_id);
        $unit = Unit::findOrFail($topic->unit_id);
        $subject = Subject::findOrFail($unit->subject_id);
        $class = SchoolClass::findOrFail($subject->class_id);

        $lessons = Lesson::where('topic_id', $request->topic_id)
            ->orderBy('order')
            ->paginate(10);

        return Inertia::render('lessons/Index', [
            'lessons' => $lessons,
            'topic' => $topic,
            'unit' => $unit,
            'subject' => $subject,
            'class' => $class,
            'openCreateDialog' => $request->has('create'),
        ]);
    }

    /**
     * Store a newly created lesson.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'topic_id' => 'required|integer|exists:topics,id',
        ]);

        // Get the highest order for lessons in this topic
        $maxOrder = Lesson::where('topic_id', $validated['topic_id'])->max('order') ?? 0;

        $lesson = Lesson::create([
            'title' => $validated['title'],
            'description' => $validated['description'],
            'topic_id' => $validated['topic_id'],
            'order' => $maxOrder + 1,
            'is_published' => false,
        ]);

        return redirect()->route('lessons.index', ['topic_id' => $validated['topic_id']]);
    }

    /**
     * Update the specified lesson.
     */
    public function update(Request $request, Lesson $lesson)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
        ]);

        $lesson->update([
            'title' => $validated['title'],
            'description' => $validated['description'],
        ]);

        return redirect()->route('lessons.index', ['topic_id' => $lesson->topic_id]);
    }

    /**
     * Remove the specified lesson.
     */
    public function destroy(Lesson $lesson)
    {
        $topicId = $lesson->topic_id;

        // Delete all content blocks associated with this lesson
        $lesson->contentBlocks()->delete();

        // Delete the lesson
        $lesson->delete();

        // Reorder remaining lessons
        $remainingLessons = Lesson::where('topic_id', $topicId)
            ->orderBy('order')
            ->get();

        foreach ($remainingLessons as $index => $remainingLesson) {
            $remainingLesson->update(['order' => $index + 1]);
        }

        return redirect()->route('lessons.index', ['topic_id' => $topicId]);
    }

    /**
     * Display the lesson content editor.
     */
    public function content(Request $request)
    {
        $request->validate([
            'lesson_id' => 'required|integer|exists:lessons,id',
        ]);

        $lesson = Lesson::with('contentBlocks')->findOrFail($request->lesson_id);
        $topic = Topic::findOrFail($lesson->topic_id);
        $unit = Unit::findOrFail($topic->unit_id);
        $subject = Subject::findOrFail($unit->subject_id);
        $class = SchoolClass::findOrFail($subject->class_id);

        // Get content blocks with their children
        $contentBlocks = $lesson->contentBlocks()
            ->whereNull('parent_id')
            ->with('children')
            ->orderBy('order')
            ->get();

        return Inertia::render('lessons/Content', [
            'lesson' => $lesson,
            'contentBlocks' => $contentBlocks,
            'topic' => $topic,
            'unit' => $unit,
            'subject' => $subject,
            'class' => $class,
        ]);
    }

    /**
     * Reorder lessons within a topic.
     */
    public function reorder(Request $request)
    {
        $validated = $request->validate([
            'lessons' => 'required|array',
            'lessons.*.id' => 'required|integer|exists:lessons,id',
            'lessons.*.order' => 'required|integer|min:1',
            'topic_id' => 'required|integer|exists:topics,id',
        ]);

        foreach ($validated['lessons'] as $lessonData) {
            Lesson::where('id', $lessonData['id'])->update(['order' => $lessonData['order']]);
        }

        return response()->json(['success' => true]);
    }
}
