# Lesson Content Management System

This module provides a dynamic content management system for educational lessons, allowing you to create, organize, and manage rich content for educational topics.

## Features

- **Lesson Management**: Create, edit, and delete lessons for any topic
- **Drag and Drop Reordering**: Easily change the order of lessons and content blocks
- **Rich Content Blocks**: Support for multiple content types:
    - Headings (with different levels)
    - Paragraphs
    - Images (with captions)
    - Videos (with captions)
    - Audio clips
    - Lists (bulleted and ordered)
    - And more...
- **Real-time Preview**: See how your content will appear as you build it

## File Structure

- `Index.tsx`: Main page for listing and reordering lessons
- `Create.tsx`: Dialog for creating new lessons
- `Edit.tsx`: Dialog for editing existing lessons
- `Delete.tsx`: Dialog for confirming lesson deletion
- `Content.tsx`: Page for managing content blocks within a lesson

## Content Block Types

The system supports the following content block types:

1. **Heading**: Section titles with customizable heading levels (H1-H3)
2. **Paragraph**: Text content with formatting
3. **Image**: Images with optional captions and alt text
4. **Video**: Embedded videos with optional captions
5. **Audio**: Audio clips with player controls
6. **List**: Bulleted or ordered lists with list items

Each content block can include:

- Main content (text)
- Media files (images, videos, audio)
- Audio narration (optional)
- Custom attributes (like heading level, etc.)

## Usage Flow

1. From the Topics page, click "Add lesson content" on any topic
2. Create a new lesson or select an existing one
3. Add content blocks by clicking "Add Content Block"
4. Select the type of content you want to add
5. Fill in the content and upload any media as needed
6. Reorder blocks by dragging them to the desired position
7. Save your changes when finished

## Implementation Notes

- The system uses React with TypeScript for the frontend
- Drag and drop functionality is implemented with `@dnd-kit/core` and `@dnd-kit/sortable`
- Content blocks are stored in the database with order and parent-child relationships
- Media files are stored in the `/storage` directory and referenced by path in the database
