{"v": "5.7.1", "fr": 60, "ip": 0, "op": 180, "w": 500, "h": 500, "nm": "Welcome Robot", "ddd": 0, "assets": [], "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "Main Circle", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [0]}, {"t": 180, "s": [360]}]}, "p": {"a": 0, "k": [250, 250]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [100, 100]}, {"t": 90, "s": [120, 120]}, {"t": 180, "s": [100, 100]}]}}, "shapes": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [120, 120]}, "p": {"a": 0, "k": [0, 0]}}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.412, 0.706, 1]}}]}]}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "Outer Ring", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [50]}, {"t": 90, "s": [100]}, {"t": 180, "s": [50]}]}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [0]}, {"t": 180, "s": [-360]}]}, "p": {"a": 0, "k": [250, 250]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}}, "shapes": [{"ty": "gr", "it": [{"ty": "sr", "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "pt": {"a": 0, "k": 5}, "ir": {"a": 0, "k": 80}, "is": {"a": 0, "k": 0}, "or": {"a": 0, "k": 100}, "os": {"a": 0, "k": 0}}, {"ty": "st", "w": {"a": 0, "k": 10}, "c": {"a": 0, "k": [0.424, 0.388, 1, 1]}, "o": {"a": 0, "k": 100}}]}]}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "Stars", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [100]}, {"t": 90, "s": [50]}, {"t": 180, "s": [100]}]}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [0]}, {"t": 180, "s": [360]}]}, "p": {"a": 0, "k": [250, 250]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}}, "shapes": [{"ty": "gr", "it": [{"ty": "sr", "p": {"a": 0, "k": [60, 0]}, "r": {"a": 0, "k": 0}, "pt": {"a": 0, "k": 5}, "ir": {"a": 0, "k": 15}, "is": {"a": 0, "k": 0}, "or": {"a": 0, "k": 30}, "os": {"a": 0, "k": 0}}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.843, 0, 1]}}]}, {"ty": "gr", "it": [{"ty": "sr", "p": {"a": 0, "k": [-60, 0]}, "r": {"a": 0, "k": 0}, "pt": {"a": 0, "k": 5}, "ir": {"a": 0, "k": 15}, "is": {"a": 0, "k": 0}, "or": {"a": 0, "k": 30}, "os": {"a": 0, "k": 0}}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.843, 0, 1]}}]}]}]}