<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\StudentAuthController;
use App\Http\Middleware\StudentLoginRateLimiter;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Student authentication routes
Route::post('/student/login', [StudentAuthController::class, 'login'])
    ->middleware(StudentLoginRateLimiter::class);

// Protected routes (require authentication)
Route::middleware('auth:sanctum')->group(function () {
    Route::post('/student/logout', [StudentAuthController::class, 'logout']);
    Route::get('/student/me', [StudentAuthController::class, 'me']);
    Route::get('/user', function (Request $request) {
        return $request->user();
    });
});
