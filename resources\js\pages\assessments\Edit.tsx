import { useForm } from '@inertiajs/react';
import { useEffect } from 'react';

import { <PERSON><PERSON>, DialogContent, DialogDescription, Di<PERSON>Footer, Di<PERSON>Header, DialogTitle } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

interface Assessment {
    id: number;
    title: string;
    description: string;
    subject_id: number;
    class_id: number;
    unit_id: number;
    topic_id: number;
}

interface Props {
    assessment: Assessment;
    open: boolean;
    onOpenChange: (open: boolean) => void;
}

function EditAssessment({ assessment, open, onOpenChange }: Props) {
    const { data, setData, put, processing, errors, reset } = useForm({
        title: assessment.title,
        description: assessment.description,
        subject_id: assessment.subject_id,
        class_id: assessment.class_id,
        unit_id: assessment.unit_id,
        topic_id: assessment.topic_id,
    });

    useEffect(() => {
        if (open) {
            setData({
                title: assessment.title,
                description: assessment.description,
                subject_id: assessment.subject_id,
                class_id: assessment.class_id,
                unit_id: assessment.unit_id,
                topic_id: assessment.topic_id,
            });
        }
    }, [open, assessment]);

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { name, value } = e.target;
        setData(name as keyof typeof data, value);
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();

        put(route('assessments.update', { assessment: assessment.id }), {
            preserveScroll: true,
            onSuccess: () => {
                onOpenChange(false);
                reset();
            },
        });
    };

    const handleOpenChange = (open: boolean) => {
        onOpenChange(open);
        if (!open) {
            reset();
        }
    };

    return (
        <Dialog open={open} onOpenChange={handleOpenChange}>
            <DialogContent className="sm:max-w-md">
                <DialogHeader>
                    <DialogTitle>Edit Assessment</DialogTitle>
                    <DialogDescription>Update assessment information below.</DialogDescription>
                </DialogHeader>
                <form onSubmit={handleSubmit}>
                    <div className="grid gap-4 py-4">
                        <div className="grid gap-2">
                            <Label htmlFor="title">Assessment Title</Label>
                            <Input
                                id="title"
                                name="title"
                                value={data.title}
                                onChange={handleChange}
                                placeholder="Enter assessment's title"
                                className={errors.title ? 'border-red-500' : ''}
                            />
                            {errors.title && <p className="text-xs text-red-500">{errors.title}</p>}
                        </div>
                    </div>
                    <DialogFooter>
                        <button
                            type="button"
                            onClick={() => handleOpenChange(false)}
                            className="rounded-md px-4 py-2 text-sm font-medium text-gray-600 hover:bg-gray-100"
                            disabled={processing}
                        >
                            Cancel
                        </button>
                        <button
                            type="submit"
                            className="cursor-pointer rounded-md bg-blue-950 px-4 py-2 text-sm font-medium text-white hover:bg-blue-950/90 disabled:opacity-50"
                            disabled={processing}
                        >
                            {processing ? 'Updating...' : 'Update Assessment'}
                        </button>
                    </DialogFooter>
                </form>
            </DialogContent>
        </Dialog>
    );
}

export default EditAssessment;
