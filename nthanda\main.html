<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Standard 1 Subjects</title>
        <link rel="stylesheet" href="styles.css">
        <script src="auth.js"></script>
        <style>
        :root {
            --color-blue: #1c407c;
            --color-yellow: #ffd93d;
            --color-dark-yellow: #e6c235;
            --color-white: #ffffff;
            --color-red: #ff5252;
            --color-green: #4caf50;
            --color-dark-green: #388e3c;
            --color-gray: #F5F5F5;
            --color-text-gray: #666;
        }
        
        body {
            background-color: var(--color-yellow);
            margin: 0;
            padding: 0;
            font-family: "Fredoka", sans-serif;
            color: var(--color-blue);
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 0;
        }
        
        .profile-section {
                display: flex;
                align-items: center;
                margin-bottom: 20px;
                padding: 16px;
                justify-content: space-between;
            }
            
            .profile-left {
                display: flex;
                align-items: center;
            }
            
            .avatar {
                width: 50px;
                height: 50px;
                border-radius: 25px;
                background-color: var(--color-blue);
                display: flex;
                align-items: center;
                justify-content: center;
                overflow: hidden;
                cursor: pointer;
            }
            
            .avatar img {
                width: 100%;
                height: 100%;
                object-fit: cover;
            }
            
            .welcome-text {
                flex: 1;
                margin-left: 15px;
                text-align: left;
            }
            
            .greeting {
                color: var(--color-text-gray);
                font-weight: 400;
                margin: 0;
                text-transform: capitalize;
            }
            
            .username {
                color: var(--color-blue);
                font-weight: 700;
                margin: 0;
                font-size: 1.25rem;
            }
            
            .notification-badge {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 50px;
                height: 50px;
                background-color: #F0F0F0;
                border-radius: 10px;
                cursor: pointer;
                margin-left: auto;
            }
        
        .content {
            flex: 1;
            border-top-left-radius: 24px;
            border-top-right-radius: 24px;
            background-color: var(--color-white);
            padding: 16px;
            min-height: 80vh;
        }
        
        
        /* New subject grid styles */
        .subjects-grid {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            gap: 16px;
        }
        
        .subject-card-wrapper {
            width: calc(50% - 8px);
            animation: fadeIn 0.5s ease-in-out;
        }
        
        .subject-card {
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            cursor: pointer;
            height: 100%;
        }
        
        .subject-content {
            min-height: 200px;
            background-color: var(--color-yellow);
            display: flex;
            flex-direction: column;
            height: 100%;
        }
        
        .subject-icon {
            width: 100%;
            height: 280px;
            object-fit: cover;
            border-radius: 20px 20px 0 0;
        }
        
        .subject-header {
            display: flex;
            flex-direction: row;
            justify-content: center;
            align-items: center;
            padding: 15px 0;
            flex-grow: 1;
        }
        
        .subject-name {
            font-weight: 700;
            font-size: 18px;
            color: var(--color-blue);
            text-align: center;
        }
        
       
        
        /* Responsive styles for different screen sizes */
        @media (max-width: 768px) {
            /* Medium devices (tablets) */
            .subject-card-wrapper {
                width: calc(50% - 8px);
            }
            
            .subject-content {
                min-height: 260px;
            }
            
            .subject-icon {
                height: 200px;
            }
        }
        
        @media (max-width: 480px) {
            /* Small devices (mobile phones) */
            .container {
                padding: 0 8px;
            }
            
            .subject-card-wrapper {
                width: 100%;
                margin-bottom: 16px;
            }
            
            .subject-content {
                min-height: 140px;
            }
            
            .subject-icon {
                height: 220px;
            }
            
            .subject-name {
                font-size: 20px;
            }
            
            .progress-stats {
                flex-direction: row;
            }
            
            .progress-stat-item {
                padding: 0 5px;
            }
        }
        
        /* Keep existing unit styles for compatibility */
       
        
        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
    </head>
    <body>
        <div class="container">
            <div class="profile-section">
                <div class="profile-left">
                    <div class="avatar" id="profileAvatar">
                        <img src="assets/welcome/male.png" alt="Profile">
                    </div>
                    <div class="welcome-text">
                        <p class="greeting">
                            Standard 1
                        </p>
                        <p class="username user-name"
                            id="usernameDisplay">Student!
                            👋</p>
                    </div>
                </div>
                <div class="notification-badge logout-button">
                    <img src="assets/icons/logout.png" width="20"
                        height="20" alt="Notification" onclick="logout()">
                </div>
            </div>

            <div class="content">

                <div class="subjects-grid" id="subjectsGrid">
                    <!-- Hard-coded subject cards -->

                </div>
            </div>
        </div>
        <script src="jquery.js"></script>
        <script src="auth.js"></script>
        <script src="content.js"></script>
        <script>
            $(document).ready(function() {
                getAllSubjects();

                function getAllSubjects() {
                    var subjects = getSubjects();
                    var subjectWrapper = ""
                    $.map(subjects, function(subject) {
                        subjectWrapper += '<a href="units.html?subject_id=' + subject.id + '" class="subject-card-wrapper" style="animation-delay: 500ms">'
                        subjectWrapper += '<div class="subject-card">'
                        subjectWrapper += '<div class="subject-content" style="position: relative;">'
                        subjectWrapper += '<img src="' + getSubjectImage(subject.name) + '" alt="Chichewa" class="subject-icon">'
                        subjectWrapper += '<div class="subject-header">'
                        subjectWrapper += '<h3 class="subject-name">' + subject.name + '</h3>'
                        subjectWrapper += '</div>'
                        subjectWrapper += '</div>'
                        subjectWrapper += '</div>'
                        subjectWrapper += '</a>'
                        
                        
                    })
                    $('#subjectsGrid').html(subjectWrapper)
                }
                
            });
        </script>
    </body>
</html>
