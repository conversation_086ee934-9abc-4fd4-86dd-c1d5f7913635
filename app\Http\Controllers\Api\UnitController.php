<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Unit;
use App\Models\Topic;
use App\Models\CompletedTopic;
use App\Models\Subject;
use Carbon\Carbon;

class UnitController extends Controller
{
    /**
     * Get units for a specific subject
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function getUnits(Request $request, $subject_id, $student_id)
    {
        // Validate request


        // Get all units for the subject ordered by creation date
        $units = Unit::where('subject_id', $subject_id)
            ->orderBy('created_at', 'asc')
            ->with(['topics' => function ($query) {
                $query->orderBy('created_at', 'asc');
            }])
            ->get();

        // Get all completed topics for this student
        $completedTopics = CompletedTopic::where('user_id', $student_id)
            ->pluck('topic_id')
            ->toArray();

        // Process each unit to add completion status and waiting time
        $processedUnits = [];
        $previousUnitComplete = true;
        $previousUnitCompletionTime = null;

        foreach ($units as $unit) {
            // Check if all topics in this unit are completed
            $allTopicsCompleted = true;
            $completedTopicsCount = 0;
            $totalTopicsCount = $unit->topics->count();

            foreach ($unit->topics as $topic) {
                if (in_array($topic->id, $completedTopics)) {
                    $completedTopicsCount++;
                    $topic->is_completed = true;
                } else {
                    $allTopicsCompleted = false;
                    $topic->is_completed = false;
                }
            }

            // Check if this unit can be started
            $canStart = $previousUnitComplete;

            // If previous unit is complete but 48 hours haven't passed, set waiting time
            $waitingTime = null;
            /* if (!$canStart && $previousUnitCompletionTime) {
                $waitingTime = Carbon::parse($previousUnitCompletionTime)->addHours(48);
                if (Carbon::now()->isBefore($waitingTime)) {
                    $canStart = false;
                } else {
                    $canStart = true;
                }
            } */

            // Add unit data with completion status
            $unitData = [
                'id' => $unit->id,
                'name' => $unit->name,
                'description' => $unit->description,
                'is_complete' => $allTopicsCompleted,
                'can_start' => true, /* $canStart */
                'waiting_time' => null, /* $waitingTime ? $waitingTime->toDateTimeString() : null, */
                'topics' => $unit->topics,
                'completion_percentage' => $totalTopicsCount > 0 ? round(($completedTopicsCount / $totalTopicsCount) * 100) : 0,
                'subject_id' => $unit->subject_id,
            ];

            $processedUnits[] = $unitData;

            // Update for next iteration
            $previousUnitComplete = $allTopicsCompleted;
            if ($allTopicsCompleted) {
                // Find the most recent completion time for this unit
                $lastCompletion = CompletedTopic::where('user_id', $student_id)
                    ->whereIn('topic_id', $unit->topics->pluck('id'))
                    ->orderBy('created_at', 'desc')
                    ->first();

                if ($lastCompletion) {
                    $previousUnitCompletionTime = $lastCompletion->created_at;
                }
            }
        }

        // Get subject details
        $subject = Subject::find($subject_id);

        return response()->json([
            'success' => true,
            'units' => $processedUnits,
            'subject' => [
                'id' => $subject->id,
                'name' => $subject->name,
                'description' => $subject->description,
            ],
        ]);
    }
}