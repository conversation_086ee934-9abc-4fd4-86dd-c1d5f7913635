import { ContentBlock } from '@/types';
import { render, screen } from '@testing-library/react';
import { ContentBlockViewer } from '../ContentBlockViewer';

describe('ContentBlockViewer', () => {
    // Test for heading block
    it('renders a heading block correctly', () => {
        const headingBlock: ContentBlock = {
            id: 1,
            lesson_id: 1,
            type: 'heading',
            content: 'Test Heading',
            media_path: null,
            audio_path: null,
            order: 1,
            parent_id: null,
            attributes: { level: '2' },
            created_at: '2023-01-01',
            updated_at: '2023-01-01',
        };

        render(<ContentBlockViewer block={headingBlock} />);

        const headingElement = screen.getByText('Test Heading');
        expect(headingElement).toBeInTheDocument();
        expect(headingElement.tagName).toBe('H2');
    });

    // Test for paragraph block
    it('renders a paragraph block correctly', () => {
        const paragraphBlock: ContentBlock = {
            id: 2,
            lesson_id: 1,
            type: 'paragraph',
            content: 'This is a test paragraph.',
            media_path: null,
            audio_path: null,
            order: 2,
            parent_id: null,
            attributes: null,
            created_at: '2023-01-01',
            updated_at: '2023-01-01',
        };

        render(<ContentBlockViewer block={paragraphBlock} />);

        const paragraphElement = screen.getByText('This is a test paragraph.');
        expect(paragraphElement).toBeInTheDocument();
        expect(paragraphElement.tagName).toBe('P');
    });

    // Test for image block
    it('renders an image block correctly', () => {
        const imageBlock: ContentBlock = {
            id: 3,
            lesson_id: 1,
            type: 'image',
            content: 'Image caption',
            media_path: 'images/test.jpg',
            audio_path: null,
            order: 3,
            parent_id: null,
            attributes: null,
            created_at: '2023-01-01',
            updated_at: '2023-01-01',
        };

        render(<ContentBlockViewer block={imageBlock} />);

        const imageCaption = screen.getByText('Image caption');
        expect(imageCaption).toBeInTheDocument();

        const imageElement = screen.getByAltText('Image caption');
        expect(imageElement).toBeInTheDocument();
        expect(imageElement.tagName).toBe('IMG');
        expect(imageElement).toHaveAttribute('src', '/storage/images/test.jpg');
    });

    // Test for list block
    it('renders a list block with children correctly', () => {
        const listBlock: ContentBlock = {
            id: 4,
            lesson_id: 1,
            type: 'list',
            content: null,
            media_path: null,
            audio_path: null,
            order: 4,
            parent_id: null,
            attributes: null,
            created_at: '2023-01-01',
            updated_at: '2023-01-01',
            children: [
                {
                    id: 5,
                    lesson_id: 1,
                    type: 'listItem',
                    content: 'Item 1',
                    media_path: null,
                    audio_path: null,
                    order: 1,
                    parent_id: 4,
                    attributes: null,
                    created_at: '2023-01-01',
                    updated_at: '2023-01-01',
                },
                {
                    id: 6,
                    lesson_id: 1,
                    type: 'listItem',
                    content: 'Item 2',
                    media_path: null,
                    audio_path: null,
                    order: 2,
                    parent_id: 4,
                    attributes: null,
                    created_at: '2023-01-01',
                    updated_at: '2023-01-01',
                },
            ],
        };

        render(<ContentBlockViewer block={listBlock} />);

        const item1 = screen.getByText('Item 1');
        const item2 = screen.getByText('Item 2');

        expect(item1).toBeInTheDocument();
        expect(item2).toBeInTheDocument();

        // Check that they're within an unordered list
        expect(item1.closest('ul')).toBeInTheDocument();
        expect(item2.closest('ul')).toBeInTheDocument();
    });

    // Test for audio narration
    it('renders audio narration when available', () => {
        const blockWithNarration: ContentBlock = {
            id: 7,
            lesson_id: 1,
            type: 'paragraph',
            content: 'Paragraph with narration',
            media_path: null,
            audio_path: 'audio/narration.mp3',
            order: 5,
            parent_id: null,
            attributes: null,
            created_at: '2023-01-01',
            updated_at: '2023-01-01',
        };

        render(<ContentBlockViewer block={blockWithNarration} />);

        const paragraphElement = screen.getByText('Paragraph with narration');
        expect(paragraphElement).toBeInTheDocument();

        const narrationLabel = screen.getByText('Narration:');
        expect(narrationLabel).toBeInTheDocument();

        const audioElement = screen.getByRole('audio');
        expect(audioElement).toBeInTheDocument();
        expect(audioElement).toHaveAttribute('src', '/storage/audio/narration.mp3');
    });
});
