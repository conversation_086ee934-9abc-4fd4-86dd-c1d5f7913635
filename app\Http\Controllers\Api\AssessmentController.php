<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Assessment;
use App\Models\AssessmentQuestion;
use App\Models\AssessmentQuestionDetail;
use App\Models\AssessmentResult;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class AssessmentController extends Controller
{
    /**
     * Get a list of available assessments for the student
     * 
     * @return \Illuminate\Http\JsonResponse
     */
    public function getAssessments($user_id, $subject_id)
    {

        $user = User::find($user_id);
        // Get assessments for student's class
        $assessments = Assessment::where('subject_id', $subject_id)
            ->with(['subject', 'unit', 'topic'])
            ->get()
            ->map(function ($assessment) use ($user) {
                // Check if student has already taken this assessment
                $result = AssessmentResult::where('assessment_id', $assessment->id)
                    ->where('student_id', $user->id)
                    ->first();

                $progress = $result ? $result->progress : 'not_started';
                $score = $result ? $result->score : null;

                return [
                    'id' => $assessment->id,
                    'title' => $assessment->title,
                    'description' => $assessment->description,
                    'subject' => $assessment->subject->name,
                    'unit' => $assessment->unit ? $assessment->unit->name : null,
                    'topic' => $assessment->topic ? $assessment->topic->name : null,
                    'progress' => $progress,
                    'score' => $score,
                    'status' => $result ? $result->status : null
                ];
            });

        return response()->json([
            'success' => true,
            'assessments' => $assessments
        ]);
    }

    /**
     * Get assessment details (without answers)
     * 
     * @param int $id Assessment ID
     * @return \Illuminate\Http\JsonResponse
     */
    public function getAssessment($id, $user_id)
    {
        try {
            $user = User::find($user_id);

            $assessment = Assessment::with(['subject', 'unit', 'topic'])
                ->findOrFail($id);

            // Check if assessment belongs to student's class
            if ($assessment->class_id != $user->class_id) {
                return response()->json([
                    'success' => false,
                    'message' => 'You do not have access to this assessment'
                ], 403);
            }

            // Check if student has already completed this assessment
            $existingResult = AssessmentResult::where('assessment_id', $id)
                ->where('student_id', $user->id)
                ->where('progress', 'completed')
                ->first();

            if ($existingResult) {
                return response()->json([
                    'success' => false,
                    'message' => 'You have already completed this assessment',
                    'result' => [
                        'score' => $existingResult->score,
                        'passed' => $existingResult->status === 'passed',
                        'completed_at' => $existingResult->updated_at
                    ]
                ]);
            }

            // Get questions without correct answers
            $questions = AssessmentQuestion::where('assessment_id', $id)
                ->get()
                ->map(function ($question) {
                    return [
                        'id' => $question->id,
                        'question' => $question->question,
                        'question_type' => $question->question_type,
                        'audio_url' => $question->audio_url,
                        'options' => [
                            'A' => $question->option_a,
                            'B' => $question->option_b,
                            'C' => $question->option_c,
                            'D' => $question->option_d,
                        ]
                    ];
                });

            $assessmentData = [
                'id' => $assessment->id,
                'title' => $assessment->title,
                'description' => $assessment->description,
                'subject' => $assessment->subject->name,
                'unit' => $assessment->unit ? $assessment->unit->name : null,
                'topic' => $assessment->topic ? $assessment->topic->name : null,
                'total_questions' => $questions->count(),
                'questions' => $questions
            ];

            // Check for or create an in-progress assessment result
            $result = AssessmentResult::firstOrCreate(
                [
                    'assessment_id' => $id,
                    'student_id' => $user->id,
                    'progress' => 'started'
                ],
                [
                    'score' => 0,
                    'failed_questions' => 0,
                    'correct_questions' => 0
                ]
            );

            return response()->json([
                'success' => true,
                'assessment' => $assessmentData,
                'result_id' => $result->id
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve assessment: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Submit answers for an assessment
     * 
     * @param Request $request
     * @param int $id Assessment ID
     * @return \Illuminate\Http\JsonResponse
     */
    public function submitAnswers(Request $request, $id)
    {
        try {
            $user = User::find($request->user_id);

            // Validate request
            $validator = Validator::make($request->all(), [
                'result_id' => 'required|exists:assessment_results,id',
                'answers' => 'required|array',
                'answers.*.question_id' => 'required|exists:assessment_questions,id',
                'answers.*.answer' => 'required|string|max:1'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $assessment = Assessment::findOrFail($id);

            // Check if assessment belongs to student's class
            if ($assessment->class_id != $user->class_id) {
                return response()->json([
                    'success' => false,
                    'message' => 'You do not have access to this assessment'
                ], 403);
            }

            $resultId = $request->input('result_id');
            $result = AssessmentResult::where('id', $resultId)
                ->where('student_id', $user->id)
                ->where('assessment_id', $id)
                ->first();

            if (!$result) {
                return response()->json([
                    'success' => false,
                    'message' => 'Assessment result not found'
                ], 404);
            }

            if ($result->progress === 'completed') {
                return response()->json([
                    'success' => false,
                    'message' => 'This assessment has already been completed'
                ], 400);
            }

            // Process submitted answers
            $answers = $request->input('answers');
            $totalQuestions = AssessmentQuestion::where('assessment_id', $id)->count();
            $submittedQuestions = count($answers);

            if ($submittedQuestions < $totalQuestions) {
                return response()->json([
                    'success' => false,
                    'message' => 'All questions must be answered',
                    'answered' => $submittedQuestions,
                    'total' => $totalQuestions
                ], 400);
            }

            $correctCount = 0;
            $failedCount = 0;

            DB::beginTransaction();

            try {
                // Remove any existing question details if the student is resubmitting
                AssessmentQuestionDetail::where('assessment_result_id', $resultId)->delete();

                foreach ($answers as $answer) {
                    $questionId = $answer['question_id'];
                    $studentAnswer = strtoupper($answer['answer']);

                    $question = AssessmentQuestion::findOrFail($questionId);
                    $isCorrect = strtoupper($question->correct_answer) === $studentAnswer;

                    // Record question details
                    AssessmentQuestionDetail::create([
                        'assessment_result_id' => $resultId,
                        'assessment_question_id' => $questionId,
                        'student_answer' => $studentAnswer,
                        'is_correct' => $isCorrect
                    ]);

                    if ($isCorrect) {
                        $correctCount++;
                    } else {
                        $failedCount++;
                    }
                }

                // Calculate score (percentage)
                $score = ($correctCount / $totalQuestions) * 100;

                // Update result
                $status = $score >= 50 ? 'passed' : 'failed';
                $result->score = $score;
                $result->correct_questions = $correctCount;
                $result->failed_questions = $failedCount;
                $result->status = $status;
                $result->progress = 'completed';
                $result->save();

                DB::commit();

                return response()->json([
                    'success' => true,
                    'message' => 'Assessment completed successfully',
                    'result' => [
                        'score' => $score,
                        'correct_questions' => $correctCount,
                        'failed_questions' => $failedCount,
                        'status' => $status,
                        'passed' => $status === 'passed',
                        'progress' => 'completed'
                    ]
                ]);
            } catch (\Exception $e) {
                DB::rollBack();
                throw $e;
            }
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to submit assessment: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get assessment result details
     * 
     * @param int $id Assessment ID
     * @return \Illuminate\Http\JsonResponse
     */
    public function getResult($id, $user_id)
    {
        try {
            $user = User::find($user_id);

            $result = AssessmentResult::where('assessment_id', $id)
                ->where('student_id', $user->id)
                ->where('progress', '!=', 'started')
                ->with(['assessment.subject', 'assessment.topic', 'questionDetails.question'])
                ->first();

            if (!$result) {
                return response()->json([
                    'success' => false,
                    'message' => 'Assessment result not found'
                ], 404);
            }

            $assessment = $result->assessment;

            // Format questions with student answers
            $questions = $result->questionDetails->map(function ($detail) {
                $question = $detail->question;

                return [
                    'id' => $question->id,
                    'question' => $question->question,
                    'question_type' => $question->question_type,
                    'options' => [
                        'A' => $question->option_a,
                        'B' => $question->option_b,
                        'C' => $question->option_c,
                        'D' => $question->option_d,
                    ],
                    'correct_answer' => $question->correct_answer,
                    'audio_url' => $question->audio_url,
                    'student_answer' => $detail->student_answer,
                    'is_correct' => $detail->is_correct
                ];
            });

            $resultData = [
                'id' => $result->id,
                'assessment' => [
                    'id' => $assessment->id,
                    'title' => $assessment->title,
                    'description' => $assessment->description,
                    'subject' => $assessment->subject->name,
                    'topic' => $assessment->topic ? $assessment->topic->name : null,
                ],
                'score' => $result->score,
                'correct_questions' => $result->correct_questions,
                'failed_questions' => $result->failed_questions,
                'status' => $result->progress,
                'passed' => $result->status === 'passed',
                'completed_at' => $result->updated_at->format('Y-m-d H:i:s'),
                'questions' => $questions
            ];

            return response()->json([
                'success' => true,
                'result' => $resultData
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve assessment result: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get student's assessment statistics
     * 
     * @return \Illuminate\Http\JsonResponse
     */
    public function getStudentStats($user_id)
    {
        try {
            $user = User::find($user_id);

            // Get all assessment results for the student
            $results = AssessmentResult::where('student_id', $user->id)->get();

            // Calculate statistics
            $totalAssessments = Assessment::where('class_id', $user->class_id)->count();
            $attemptedCount = $results->count();
            $completedCount = $results->where('status', '!=', 'started')->count();
            $passedCount = $results->where('status', 'passed')->count();
            $failedCount = $results->where('status', 'failed')->count();
            $inProgressCount = $results->where('progress', 'started')->count();

            // Calculate average score from completed assessments
            $scores = $results->where('progress', 'completed')->pluck('score');
            $avgScore = $scores->isEmpty() ? 0 : $scores->avg();

            // Calculate completion rate
            $completionRate = $totalAssessments > 0 ? ($completedCount / $totalAssessments) * 100 : 0;

            // Calculate pass rate
            $passRate = $completedCount > 0 ? ($passedCount / $completedCount) * 100 : 0;

            // Get recent assessments
            $recentAssessments = AssessmentResult::where('student_id', $user->id)
                ->where('progress', '!=', 'started')
                ->with('assessment')
                ->orderBy('updated_at', 'desc')
                ->take(5)
                ->get()
                ->map(function ($result) {
                    return [
                        'id' => $result->assessment->id,
                        'title' => $result->assessment->title,
                        'subject' => $result->assessment->subject->name,
                        'score' => $result->score,
                        'status' => $result->status,
                        'progress' => $result->progress,
                        'completed_at' => $result->updated_at->format('Y-m-d H:i:s')
                    ];
                });

            // Get subjects and their average scores
            $subjectPerformance = [];
            if ($completedCount > 0) {
                $subjectPerformance = AssessmentResult::where('student_id', $user->id)
                    ->where('progress', 'completed')
                    ->with('assessment.subject')
                    ->get()
                    ->groupBy(function ($result) {
                        return $result->assessment->subject->name;
                    })
                    ->map(function ($results, $subject) {
                        return [
                            'subject' => $subject,
                            'avgScore' => round($results->avg('score'), 0),
                            'assessmentCount' => $results->count()
                        ];
                    })
                    ->values()
                    ->toArray();
            }

            return response()->json([
                'success' => true,
                'stats' => [
                    'totalAssignedAssessments' => $totalAssessments,
                    'attemptedAssessments' => $attemptedCount,
                    'completedAssessments' => $completedCount,
                    'passedAssessments' => $passedCount,
                    'failedAssessments' => $failedCount,
                    'inProgressAssessments' => $inProgressCount,
                    'averageScore' => round($avgScore, 0),
                    'completionRate' => round($completionRate, 0),
                    'passRate' => round($passRate, 0),
                    'recentAssessments' => $recentAssessments,
                    'subjectPerformance' => $subjectPerformance
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve assessment statistics: ' . $e->getMessage()
            ], 500);
        }
    }
}
