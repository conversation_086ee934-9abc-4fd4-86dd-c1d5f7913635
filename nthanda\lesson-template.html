<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Fun Learning Adventure!</title>
        <link
            href="https://fonts.googleapis.com/css2?family=Comic+Neue:wght@400;700&display=swap"
            rel="stylesheet">
        <style>
            :root {
                --primary-color: #FF6B6B;
                --secondary-color: #4ECDC4;
                --accent-color: #FFE66D;
                --background-color: #F7F7F7;
                --text-color: #2C3E50;
            }

            * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
            }

            body {
                font-family: 'Comic Neue', cursive;
                background-color: var(--background-color);
                color: var(--text-color);
                line-height: 1.6;
            }

            .lesson-container {
                max-width: 1200px;
                margin: 0 auto;
                padding: 20px;
            }

            .lesson-header {
                background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
                border-radius: 20px;
                padding: 30px;
                margin-bottom: 30px;
                color: white;
                text-align: center;
                box-shadow: 0 4px 15px rgba(0,0,0,0.1);
                animation: bounce 2s infinite;
            }

            .lesson-title {
                font-size: 2.5em;
                margin-bottom: 10px;
                text-shadow: 2px 2px 4px rgba(0,0,0,0.2);
            }

            .lesson-subtitle {
                font-size: 1.2em;
                opacity: 0.9;
            }

            .content-block {
                background: white;
                border-radius: 15px;
                padding: 20px;
                margin-bottom: 20px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.05);
                transition: transform 0.3s ease;
            }

            .content-block:hover {
                transform: translateY(-5px);
            }

            .heading {
                color: var(--primary-color);
                font-size: 1.8em;
                margin-bottom: 15px;
                border-bottom: 3px solid var(--accent-color);
                padding-bottom: 10px;
            }

            .paragraph {
                font-size: 1.1em;
                margin-bottom: 15px;
            }

            .image-container {
                text-align: center;
                margin: 20px 0;
            }

            .lesson-image {
                max-width: 100%;
                border-radius: 10px;
                box-shadow: 0 4px 10px rgba(0,0,0,0.1);
            }

            .video-container {
                position: relative;
                padding-bottom: 56.25%;
                height: 0;
                overflow: hidden;
                margin: 20px 0;
                border-radius: 10px;
            }

            .video-container iframe {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
            }

            .audio-player {
                width: 100%;
                margin: 20px 0;
                border-radius: 10px;
                background: var(--accent-color);
                padding: 10px;
            }

            .list-container {
                background: #fff;
                padding: 20px;
                border-radius: 10px;
                margin: 20px 0;
            }

            .list-item {
                margin: 10px 0;
                padding-left: 25px;
                position: relative;
            }

            .list-item::before {
                content: "🌟";
                position: absolute;
                left: 0;
            }

            .narration-button {
                background: var(--secondary-color);
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 25px;
                cursor: pointer;
                font-family: 'Comic Neue', cursive;
                font-size: 1em;
                display: flex;
                align-items: center;
                gap: 10px;
                margin: 10px 0;
                transition: transform 0.2s ease;
            }

            .narration-button:hover {
                transform: scale(1.05);
            }

            .narration-button i {
                font-size: 1.2em;
            }

            .progress-bar {
                position: fixed;
                top: 0;
                left: 0;
                height: 5px;
                background: var(--primary-color);
                transition: width 0.3s ease;
            }

            @keyframes bounce {
                0%, 100% { transform: translateY(0); }
                50% { transform: translateY(-10px); }
            }

            .floating-elements {
                position: fixed;
                width: 100%;
                height: 100%;
                pointer-events: none;
                z-index: 1000;
            }

            .floating-element {
                position: absolute;
                font-size: 2em;
                animation: float 6s infinite;
            }

            @keyframes float {
                0% { transform: translateY(0) rotate(0deg); }
                50% { transform: translateY(-20px) rotate(10deg); }
                100% { transform: translateY(0) rotate(0deg); }
            }

            .interactive-element {
                cursor: pointer;
                transition: transform 0.3s ease;
            }

            .interactive-element:hover {
                transform: scale(1.1);
            }

            .achievement-badge {
                position: fixed;
                bottom: 20px;
                right: 20px;
                background: var(--accent-color);
                padding: 15px;
                border-radius: 50%;
                box-shadow: 0 4px 15px rgba(0,0,0,0.2);
                animation: popIn 0.5s ease;
            }

            @keyframes popIn {
                0% { transform: scale(0); }
                50% { transform: scale(1.2); }
                100% { transform: scale(1); }
            }
        </style>
    </head>
    <body>
        <div class="progress-bar" id="progressBar"></div>
        <div class="floating-elements" id="floatingElements"></div>

        <div class="lesson-container">
            <div class="lesson-header">
                <h1 class="lesson-title">Welcome to Our Learning Adventure!
                    🚀</h1>
                <p class="lesson-subtitle">Get ready for an exciting journey of
                    discovery!</p>
            </div>

            <!-- Content blocks will be dynamically inserted here -->
            <div id="contentBlocks"></div>

            <div class="achievement-badge" id="achievementBadge"
                style="display: none;">
                🏆 Great job!
            </div>
        </div>

        <script>
            // Sample content blocks for demonstration
            const sampleContent = [
                {
                    type: 'heading',
                    content: 'Let\'s Start Our Adventure! 🌟',
                    level: 'h1'
                },
                {
                    type: 'paragraph',
                    content: 'Welcome to our fun learning journey! Today, we\'re going to explore amazing things together. Are you ready?',
                    hasNarration: true
                },
                {
                    type: 'image',
                    content: 'A colorful illustration of a learning adventure',
                    imageUrl: 'https://placekitten.com/800/400'
                },
                {
                    type: 'list',
                    content: 'Things we\'ll learn today:',
                    items: [
                        'Exciting new concepts',
                        'Fun facts and discoveries',
                        'Interactive activities',
                        'Creative thinking exercises'
                    ],
                    hasNarration: true
                }
            ];

            // Function to create content blocks
            function createContentBlock(block) {
                const blockElement = document.createElement('div');
                blockElement.className = 'content-block';

                switch (block.type) {
                    case 'heading':
                        blockElement.innerHTML = `
                            <h${block.level} class="heading">${block.content}</h${block.level}>
                        `;
                        break;
                    case 'paragraph':
                        blockElement.innerHTML = `
                            <p class="paragraph">${block.content}</p>
                            ${block.hasNarration ? `
                                <button class="narration-button">
                                    <i>🔊</i> Listen to this section
                                </button>
                            ` : ''}
                        `;
                        break;
                    case 'image':
                        blockElement.innerHTML = `
                            <div class="image-container">
                                <img src="${block.imageUrl}" alt="${block.content}" class="lesson-image">
                            </div>
                        `;
                        break;
                    case 'list':
                        blockElement.innerHTML = `
                            <h3 class="heading">${block.content}</h3>
                            <ul class="list-container">
                                ${block.items.map(item => `
                                    <li class="list-item">${item}</li>
                                `).join('')}
                            </ul>
                            ${block.hasNarration ? `
                                <button class="narration-button">
                                    <i>🔊</i> Listen to this list
                                </button>
                            ` : ''}
                        `;
                        break;
                }

                return blockElement;
            }

            // Initialize the lesson
            function initializeLesson() {
                const contentBlocksContainer = document.getElementById('contentBlocks');
                sampleContent.forEach(block => {
                    contentBlocksContainer.appendChild(createContentBlock(block));
                });

                // Add floating elements
                const floatingElements = document.getElementById('floatingElements');
                const emojis = ['🌟', '🎈', '🎨', '📚', '🎯', '🎪', '🎭', '🎪'];
                for (let i = 0; i < 10; i++) {
                    const element = document.createElement('div');
                    element.className = 'floating-element';
                    element.textContent = emojis[Math.floor(Math.random() * emojis.length)];
                    element.style.left = `${Math.random() * 100}%`;
                    element.style.top = `${Math.random() * 100}%`;
                    element.style.animationDelay = `${Math.random() * 5}s`;
                    floatingElements.appendChild(element);
                }

                // Progress bar
                window.addEventListener('scroll', () => {
                    const winScroll = document.body.scrollTop || document.documentElement.scrollTop;
                    const height = document.documentElement.scrollHeight - document.documentElement.clientHeight;
                    const scrolled = (winScroll / height) * 100;
                    document.getElementById('progressBar').style.width = scrolled + '%';
                });

                // Achievement badge
                let scrollCount = 0;
                window.addEventListener('scroll', () => {
                    scrollCount++;
                    if (scrollCount > 5) {
                        const badge = document.getElementById('achievementBadge');
                        badge.style.display = 'block';
                        setTimeout(() => {
                            badge.style.display = 'none';
                        }, 3000);
                    }
                });
            }

            // Start the lesson when the page loads
            window.addEventListener('load', initializeLesson);
        </script>
    </body>
</html>