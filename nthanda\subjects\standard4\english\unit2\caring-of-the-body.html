
<html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Lesson - Caring of the body</title>
        <link rel="stylesheet" href="../../../../styles.css">

        <style>
            :root {
                --color-blue: #1c407c;
                --color-yellow: #ffd93d;
                --color-dark-yellow: #e6c235;
                --color-white: #ffffff;
                --color-red: #ff5252;
                --color-green: #4caf50;
                --color-dark-green: #388e3c;
                --color-orange: #FF9800;
                --color-purple: #9C27B0;
                --color-gray: #F5F5F5;
                --color-text-gray: #666;
            }
            
            body {
                background-color: var(--color-gray);
                margin: 0;
                padding: 0;
                font-family: "Fredoka", sans-serif;
                color: var(--color-blue);
            }
            
            .container {
                max-width: 800px;
                margin: 0 auto;
                padding: 0;
                position: relative;
            }
            
            .header {
                padding: 16px;
                background-color: var(--color-yellow);
                border-bottom-left-radius: 24px;
                border-bottom-right-radius: 24px;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
                margin-bottom: 12px;
            }
            
            .header-top {
                display: flex;
                align-items: center;
                margin-bottom: 16px;
            }
            
            .back-button {
                margin-right: 16px;
                background-color: var(--color-blue);
                width: 40px;
                height: 40px;
                border-radius: 20px;
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
            }
            
            .back-button img {
                width: 24px;
                height: 24px;
            }
            
            .progress-container {
                flex: 1;
            }
            
            .progress-text {
                font-weight: 500;
                font-size: 14px;
                color: #666;
                margin-bottom: 4px;
            }
            
            .progress-bar-container {
                height: 8px;
                background-color: rgba(0, 0, 0, 0.1);
                border-radius: 4px;
                overflow: hidden;
            }
            
            .progress-bar {
                height: 100%;
                background-color: var(--color-green);
                border-radius: 4px;
                width: 0%;
                transition: width 1s ease-in-out;
            }
            
            .title-container {
                display: flex;
                align-items: center;
                padding: 0 8px;
            }
            
            .lesson-icon {
                margin-right: 12px;
                color: var(--color-orange);
                font-size: 32px;
            }
            
            .title {
                color: var(--color-blue);
                font-weight: 700;
                font-size: 20px;
                margin: 0;
            }
            
            .description {
                color: #666;
                font-weight: 400;
                font-size: 16px;
                margin-top: 4px;
            }
            
            .content {
                padding: 16px;
            }
            
            .content-item {
                margin-bottom: 20px;
            }
            
            .card {
                padding: 16px;
                border-radius: 16px;
                background-color: white;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            }
            
            .text-card {
                padding: 16px;
                border-radius: 16px;
                background-color: white;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            }
            
            .lesson-text {
                font-size: 14px;
                line-height: 20px;
                color: #333;
                font-weight: 400;
            }
            
            .audio-card {
                padding: 16px;
                border-radius: 16px;
                background-color: white;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
                display: flex;
                align-items: center;
                justify-content: center;
            }
            
            .audio-button {
                width: 48px;
                height: 48px;
                border-radius: 24px;
                background-color: var(--color-blue);
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
            }
            
            .audio-button img {
                width: 32px;
                height: 32px;
                filter: brightness(0) invert(1);
            }
            
            .audio-text {
                margin-left: 12px;
                font-size: 16px;
                font-weight: 500;
                color: var(--color-blue);
            }
            
            .images-card {
                padding: 16px;
                border-radius: 16px;
                background-color: white;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
                overflow-x: auto;
            }
            
            .images-container {
                display: flex;
                gap: 16px;
            }
            
            .lesson-image {
                width: 70%;
                height: auto;
                aspect-ratio: 4/3;
                border-radius: 12px;
                object-fit: cover;
                flex-shrink: 0;
            }
            
            .questions-card {
                padding: 16px;
                border-radius: 16px;
                background-color: white;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            }
            
            .question-header {
                display: flex;
                align-items: center;
                justify-content: space-between;
                margin-bottom: 16px;
            }
            
            .question-title {
                font-size: 20px;
                font-weight: 600;
                color: var(--color-blue);
            }
            
            .question-audio {
                width: 24px;
                height: 24px;
                border-radius: 12px;
                background-color: var(--color-blue);
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
            }
            
            .question-audio img {
                width: 16px;
                height: 16px;
                filter: brightness(0) invert(1);
            }
            
            .question-item {
                margin-bottom: 16px;
            }
            
            .question-row {
                display: flex;
                align-items: center;
                margin-bottom: 8px;
            }
            
            .number-badge {
                width: 32px;
                height: 32px;
                border-radius: 16px;
                background-color: var(--color-orange);
                display: flex;
                justify-content: center;
                align-items: center;
                margin-right: 12px;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            }
            
            .number-text {
                color: white;
                font-size: 16px;
                font-weight: 600;
            }
            
            .question-text {
                flex: 1;
                font-size: 14px;
                font-weight: 500;
                color: #666;
            }
            
            .exercise-card {
                padding: 16px;
                border-radius: 16px;
                background-color: white;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            }
            
            .exercise-title {
                font-size: 20px;
                font-weight: 600;
                color: var(--color-blue);
                margin-bottom: 8px;
            }
            
            .exercise-description {
                font-size: 16px;
                font-weight: 400;
                color: #666;
                margin-bottom: 16px;
            }
            
            .example-container {
                background-color: #F5F5F5;
                padding: 16px;
                border-radius: 12px;
                margin-bottom: 16px;
            }
            
            .example-label {
                font-size: 16px;
                font-weight: 600;
                color: var(--color-orange);
                margin-bottom: 8px;
            }
            
            .example-question {
                font-size: 16px;
                font-weight: 400;
                color: #333;
                margin-bottom: 8px;
            }
            
            .example-answer {
                font-size: 16px;
                font-weight: 500;
                color: var(--color-green);
            }
            
            .exercise-item {
                margin-bottom: 16px;
            }
            
            .exercise-question {
                flex: 1;
                font-size: 16px;
                font-weight: 500;
                color: #333;
            }
            
            .complete-button {
                margin: 20px 0;
                border-radius: 30px;
                padding: 12px 0;
                background-color: var(--color-green);
                color: white;
                font-weight: 600;
                font-size: 18px;
                text-align: center;
                cursor: pointer;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
                border: none;
                width: 100%;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <div class="header-top">
                    <div class="back-button" onclick="goBack()">
                        <img src="../../../../assets/icons/arrowleft.png"
                            alt="Back">
                    </div>
                    <div class="progress-container">
                        <p class="progress-text">Progress</p>
                        <div class="progress-bar-container">
                            <div class="progress-bar" id="progressBar"></div>
                        </div>
                    </div>
                </div>
                <div class="title-container">
                    <div class="lesson-icon">📖</div>
                    <div>
                        <h1 class="title">Caring of the body</h1>
                        <p class="description">Ways of taking caring of the
                            body</p>
                    </div>
                </div>
            </div>

            <div class="content">
                <!-- Audio Section -->
                <div class="content-item">
                    <div class="audio-card">
                        <div class="audio-button" onclick="playAudio('audio1')">
                            <img src="../../../../assets/icons/audio.png"
                                alt="Play">
                        </div>
                        <p class="audio-text">Listen to the lesson</p>
                    </div>
                </div>

                <!-- Images Section -->
                <div class="content-item">
                    <div class="images-card">
                        <div class="images-container">
                            <img src="../../../../assets/english/english.jpg"
                                alt="English Lesson 1" class="lesson-image">
                            <img src="../../../../assets/english/english2.jpg"
                                alt="English Lesson 2" class="lesson-image">
                        </div>
                    </div>
                </div>

                <!-- Text Sections -->
                <div class="content-item">
                    <div class="text-card">
                        <p class="lesson-text">The body is important. We need to
                            take care of it. There are many ways of taking care
                            of the body. Some of these ways are brushing teeth,
                            combing hair, bathing and washing hands.</p>
                    </div>
                </div>

                <div class="content-item">
                    <div class="text-card">
                        <p class="lesson-text">We should brush our teeth daily.
                            We should brush them with a tooth brush and
                            toothpaste. We can make our own tooth brush from
                            bamboo or Muula tree. If we do not have toothpaste,
                            we can use some ash.</p>
                    </div>
                </div>

                <div class="content-item">
                    <div class="text-card">
                        <p class="lesson-text">Another way of taking care of the
                            body is taking a bath with soap every day. This
                            helps to remove dirt, reduce bad smell and kill
                            germs on the body.</p>
                    </div>
                </div>

                <div class="content-item">
                    <div class="text-card">
                        <p class="lesson-text">We should also take care of the
                            body by keeping our hair clean. We should wash and
                            comb the hair every day. This helps to remove dirt
                            and prevent lice.</p>
                    </div>
                </div>

                <div class="content-item">
                    <div class="text-card">
                        <p class="lesson-text">We should also take care of our
                            bodies by washing our hands before we eat anything
                            and every time we visit the toilet. We should keep
                            our finger nails short. In this way, we avoid
                            catching germs which spread diseases. We should
                            advise one another to take care of our bodies.</p>
                    </div>
                </div>

                <!-- Questions Section -->
                <div class="content-item">
                    <div class="questions-card">
                        <div class="question-header">
                            <h3 class="question-title">Answering comprehension
                                questions</h3>
                            <div class="question-audio"
                                onclick="playAudio('audio2')">
                                <img src="../../../../assets/icons/audio.png"
                                    alt="Play Audio">
                            </div>
                        </div>

                        <div class="question-item">
                            <div class="question-row">
                                <div class="number-badge">
                                    <span class="number-text">1</span>
                                </div>
                                <p class="question-text">Mention one way of
                                    taking care of the body?</p>
                            </div>
                        </div>

                        <div class="question-item">
                            <div class="question-row">
                                <div class="number-badge">
                                    <span class="number-text">2</span>
                                </div>
                                <p class="question-text">What can we use to make
                                    a tooth brush?</p>
                            </div>
                        </div>

                        <div class="question-item">
                            <div class="question-row">
                                <div class="number-badge">
                                    <span class="number-text">3</span>
                                </div>
                                <p class="question-text">Instead of toothpaste,
                                    one can use _____ to brush the teeth.</p>
                            </div>
                        </div>

                        <div class="question-item">
                            <div class="question-row">
                                <div class="number-badge">
                                    <span class="number-text">4</span>
                                </div>
                                <p class="question-text">Why should we wash our
                                    hair every day?</p>
                            </div>
                        </div>

                        <div class="question-item">
                            <div class="question-row">
                                <div class="number-badge">
                                    <span class="number-text">5</span>
                                </div>
                                <p class="question-text">When should we wash our
                                    hands?</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Exercise A Section -->
                <div class="content-item">
                    <div class="exercise-card">
                        <h3 class="exercise-title">Exercise A: Making
                            sentences</h3>
                        <p class="exercise-description">Make sentences using the
                            following words</p>

                        <div class="example-container">
                            <p class="example-label">Example:</p>
                            <p class="example-question">prevent</p>
                            <p class="example-answer">Answer: James combs his
                                hair to prevent germs.</p>
                        </div>

                        <div class="exercise-item">
                            <div class="question-row">
                                <div class="number-badge"
                                    style="background-color: var(--color-green);">
                                    <span class="number-text">1</span>
                                </div>
                                <p class="exercise-question">bath</p>
                            </div>
                        </div>

                        <div class="exercise-item">
                            <div class="question-row">
                                <div class="number-badge"
                                    style="background-color: var(--color-green);">
                                    <span class="number-text">2</span>
                                </div>
                                <p class="exercise-question">eat</p>
                            </div>
                        </div>

                        <div class="exercise-item">
                            <div class="question-row">
                                <div class="number-badge"
                                    style="background-color: var(--color-green);">
                                    <span class="number-text">3</span>
                                </div>
                                <p class="exercise-question">care</p>
                            </div>
                        </div>

                        <div class="exercise-item">
                            <div class="question-row">
                                <div class="number-badge"
                                    style="background-color: var(--color-green);">
                                    <span class="number-text">4</span>
                                </div>
                                <p class="exercise-question">dirty</p>
                            </div>
                        </div>

                        <div class="exercise-item">
                            <div class="question-row">
                                <div class="number-badge"
                                    style="background-color: var(--color-green);">
                                    <span class="number-text">5</span>
                                </div>
                                <p class="exercise-question">wash</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Exercise B Section -->
                <div class="content-item">
                    <div class="exercise-card">
                        <h3 class="exercise-title">Exercise B: Completing
                            sentences</h3>
                        <p class="exercise-description">Complete the following
                            sentences with the correct form of verbs in brackets
                            to show that an action is continuing</p>

                        <div class="example-container">
                            <p class="example-label">Example:</p>
                            <p class="example-question">My father ______ water.
                                (drink)</p>
                            <p class="example-answer">Answer: My father is
                                drinking water.</p>
                        </div>

                        <div class="exercise-item">
                            <div class="question-row">
                                <div class="number-badge"
                                    style="background-color: var(--color-green);">
                                    <span class="number-text">1</span>
                                </div>
                                <p class="exercise-question">Mr Phiri ____to
                                    town. (go)</p>
                            </div>
                        </div>

                        <div class="exercise-item">
                            <div class="question-row">
                                <div class="number-badge"
                                    style="background-color: var(--color-green);">
                                    <span class="number-text">2</span>
                                </div>
                                <p class="exercise-question">Kondwani and Ellen
                                    ____a Chichewa book. (read)</p>
                            </div>
                        </div>

                        <div class="exercise-item">
                            <div class="question-row">
                                <div class="number-badge"
                                    style="background-color: var(--color-green);">
                                    <span class="number-text">3</span>
                                </div>
                                <p class="exercise-question">Thandizo ____
                                    English. (teach)</p>
                            </div>
                        </div>

                        <div class="exercise-item">
                            <div class="question-row">
                                <div class="number-badge"
                                    style="background-color: var(--color-green);">
                                    <span class="number-text">4</span>
                                </div>
                                <p class="exercise-question">I am ____a football
                                    match. (watch)</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Complete Button -->
                <button class="complete-button" onclick="completeLesson()">
                    I Did It! 🎉
                </button>
            </div>
        </div>

        <script>
            // Initialize progress bar
            setTimeout(function() {
                document.getElementById('progressBar').style.width = '0%';
            }, 300);
            
            function goBack() {
                window.history.back();
            }
            
            function playAudio(audioId) {
                // In a real implementation, this would play the audio
                alert('Playing audio: ' + audioId);
            }
            
            function completeLesson() {
                // Update progress bar
                document.getElementById('progressBar').style.width = '100%';
                
                // Wait 2 seconds and go back
                setTimeout(function() {
                    goBack();
                }, 2000);
            }
        </script>
    </body>
</html>