<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Cache\RateLimiter;
use Illuminate\Http\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

class StudentLoginRateLimiter
{
    /**
     * The rate limiter instance.
     *
     * @var \Illuminate\Cache\RateLimiter
     */
    protected $limiter;

    /**
     * Create a new rate limiter middleware instance.
     *
     * @param  \Illuminate\Cache\RateLimiter  $limiter
     * @return void
     */
    public function __construct(RateLimiter $limiter)
    {
        $this->limiter = $limiter;
    }

    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return \Symfony\Component\HttpFoundation\Response
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Define the maximum number of attempts
        $maxAttempts = 5;

        // Get client IP address or other unique identifier
        $key = $request->ip();

        // Check if the client has exceeded the rate limit
        if ($this->limiter->tooManyAttempts($key, $maxAttempts)) {
            // Calculate the number of seconds until the limit resets
            $seconds = $this->limiter->availableIn($key);

            // Return rate limit exceeded response
            return new JsonResponse([
                'success' => false,
                'message' => 'Too many login attempts. Please try again in ' . $seconds . ' seconds.',
            ], 429);
        }

        // Increment the rate limiter counter
        $this->limiter->hit($key, 60); // Reset after 60 seconds

        // Continue with the request
        $response = $next($request);

        // If the login was successful, clear the rate limit
        if ($response instanceof JsonResponse) {
            $data = json_decode($response->getContent(), true);
            if (isset($data['success']) && $data['success'] === true) {
                $this->limiter->clear($key);
            }
        }

        return $response;
    }
}
