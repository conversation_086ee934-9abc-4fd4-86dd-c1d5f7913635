function getUrlParams(name) {
  const urlParams = new URLSearchParams(window.location.search);
  return urlParams.get(name);
}
function getSubjects() {
  const subjects = JSON.parse(localStorage.getItem("subjects"));
  return subjects;
}

function getUnits(subjectId) {
  const units = JSON.parse(localStorage.getItem("units"));
  return units.filter((unit) => unit.subject_id == subjectId);
}

function getTopics(unitId) {
  const topics = JSON.parse(localStorage.getItem("topics"));
  return topics.filter((topic) => topic.unit_id == unitId);
}

function getTopicName(topicId) {
  const topics = JSON.parse(localStorage.getItem("topics"));
  return topics.find((topic) => topic.id == topicId).name;
}

function getSubjectName(subjectId) {
  const subjects = getSubjects();
  return subjects.find((subject) => subject.id == subjectId).name;
}

function getUnitName(unitId) {
  const units = JSON.parse(localStorage.getItem("units"));
  return units.find((unit) => unit.id == unitId).name;
}

function getSubjectImage(name) {
  if (name == "Masamu" || name == "Mathematics") {
    return "assets/masamu/numbers.jpg";
  } else if (name == "English") {
    return "assets/english/chichewa.jpg";
  } else if (name == "Chichewa") {
    return "assets/english/alphabet.jpg";
  } else if (name == "Sayansi" || name == "Science") {
    return "assets/english/english.jpg";
  }
}

function emptyContent() {
  var contentWrapper = "";
  contentWrapper += '<div class="content-empty">';
  contentWrapper +=
    '<h3 class="content-empty-title">Content is not available yet</h3>';
  contentWrapper += "</div>";
  return contentWrapper;
}
