// Import testing utilities
import '@testing-library/jest-dom';

// Extend window interface to include our custom properties
declare global {
    interface Window {
        route: (name: string, params?: Record<string, unknown>, absolute?: boolean) => string;
        global: {
            storageUrl: (path: string) => string;
            [key: string]: unknown;
        };
    }
}

// Mock Inertia route function
window.route = (name: string, params: Record<string, unknown> = {}, _absolute = true) => {
    return `/mock-route/${name}/${JSON.stringify(params)}`;
};

// Mock storage paths
window.global = {
    ...window.global,
    storageUrl: (path: string) => `/storage/${path}`,
};

// For audio/video mocks
Object.defineProperty(window.HTMLMediaElement.prototype, 'play', {
    configurable: true,
    get() {
        return () => Promise.resolve();
    },
});

Object.defineProperty(window.HTMLMediaElement.prototype, 'pause', {
    configurable: true,
    get() {
        return () => {};
    },
});

Object.defineProperty(window.HTMLMediaElement.prototype, 'muted', {
    configurable: true,
    get() {
        return false;
    },
    set() {},
});

// Global fetch mock - using jest.fn()
global.fetch = jest.fn(() =>
    Promise.resolve({
        ok: true,
        json: () => Promise.resolve({}),
        text: () => Promise.resolve(''),
        blob: () => Promise.resolve(new Blob()),
    } as Response),
);
