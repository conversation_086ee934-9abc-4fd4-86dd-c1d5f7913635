import { LucideIcon } from 'lucide-react';
import type { Config } from 'ziggy-js';

export interface Auth {
    user: User;
}

export interface BreadcrumbItem {
    title: string;
    href: string;
}

export interface NavGroup {
    title: string;
    items: NavItem[];
}

export interface NavItem {
    title: string;
    href: string;
    icon?: LucideIcon | null;
    isActive?: boolean;
    route?: string;
}

export interface SharedData {
    name: string;
    quote: { message: string; author: string };
    auth: Auth;
    ziggy: Config & { location: string };
    [key: string]: unknown;
}

export interface User {
    id: number;
    name: string;
    role: string;
    email: string;
    avatar?: string;
    email_verified_at: string | null;
    created_at: string;
    updated_at: string;
    [key: string]: unknown; // This allows for additional properties...
}

export interface SchoolClass {
    id: number;
    name: string;
}

export interface Subject {
    id: number;
    name: string;
    class_id: number;
    created_at: string;
    updated_at: string;
    assessments_count?: number;
}

// Lesson Content Management System Types

export interface Unit {
    id: number;
    name: string;
    description: string | null;
    subject_id: number;
    order: number;
    created_at: string;
    updated_at: string;
    topics?: Topic[];
}

export interface Topic {
    id: number;
    name: string;
    description: string | null;
    unit_id: number;
    order: number;
    created_at: string;
    updated_at: string;
    lessons?: Lesson[];
}

export interface Lesson {
    id: number;
    title: string;
    description: string | null;
    topic_id: number;
    order: number;
    is_published: boolean;
    created_at: string;
    updated_at: string;
    contentBlocks?: ContentBlock[];
}

export interface ContentBlock {
    id: number;
    lesson_id: number;
    type: ContentBlockType;
    content: string | null;
    media_path: string | null;
    audio_path: string | null;
    order: number;
    parent_id: number | null;
    attributes: Record<string, unknown> | null;
    created_at: string;
    updated_at: string;
    children?: ContentBlock[];
}

export type ContentBlockType = 'heading' | 'paragraph' | 'image' | 'video' | 'audio' | 'list' | 'listItem' | 'zip' | 'wysiwyg';
