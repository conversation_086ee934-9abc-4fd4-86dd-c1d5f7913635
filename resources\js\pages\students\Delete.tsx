import { useForm } from '@inertiajs/react';

import { <PERSON><PERSON>, DialogContent, DialogDescription, Di<PERSON>Footer, DialogHeader, DialogTitle } from '@/components/ui/dialog';

interface StudentUser {
    id: number;
    name: string;
    pin: number;
}

interface Props {
    student: StudentUser;
    open: boolean;
    onOpenChange: (open: boolean) => void;
}

function DeleteStudent({ student, open, onOpenChange }: Props) {
    const { delete: destroy, processing } = useForm();

    const handleDelete = () => {
        destroy(`/students/${student.id}`, {
            preserveScroll: true,
            onSuccess: () => {
                onOpenChange(false);
            },
        });
    };

    return (
        <Dialog open={open} onOpenChange={onOpenChange}>
            <DialogContent className="sm:max-w-md">
                <DialogHeader>
                    <DialogTitle>Delete Student</DialogTitle>
                    <DialogDescription>Are you sure you want to delete {student.name}? This action cannot be undone.</DialogDescription>
                </DialogHeader>
                <DialogFooter>
                    <button
                        type="button"
                        onClick={() => onOpenChange(false)}
                        className="rounded-md px-4 py-2 text-sm font-medium text-gray-600 hover:bg-gray-100"
                        disabled={processing}
                    >
                        Cancel
                    </button>
                    <button
                        type="button"
                        onClick={handleDelete}
                        className="cursor-pointer rounded-md bg-red-600 px-4 py-2 text-sm font-medium text-white hover:bg-red-700 disabled:opacity-50"
                        disabled={processing}
                    >
                        {processing ? 'Deleting...' : 'Delete Student'}
                    </button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    );
}

export default DeleteStudent;
