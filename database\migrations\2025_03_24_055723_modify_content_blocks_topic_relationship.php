<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('content_blocks', function (Blueprint $table) {
            // Drop foreign key constraint for lesson_id
            $table->dropForeign(['lesson_id']);

            // Drop lesson_id column
            $table->dropColumn('lesson_id');

            // Add topic_id column
            $table->foreignId('topic_id')->after('id')->constrained('topics')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('content_blocks', function (Blueprint $table) {
            // Drop foreign key constraint for topic_id
            $table->dropForeign(['topic_id']);

            // Drop topic_id column
            $table->dropColumn('topic_id');

            // Add lesson_id column back
            $table->foreignId('lesson_id')->after('id')->constrained('lessons')->onDelete('cascade');
        });
    }
};
