<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('lessons', function (Blueprint $table) {
            // Drop columns that should be in content_blocks instead
            $table->dropColumn(['type', 'content', 'media_path', 'audio_path', 'attributes']);

            // Add missing columns for lessons
            $table->string('title')->after('topic_id');
            $table->text('description')->nullable()->after('title');
            $table->boolean('is_published')->default(false)->after('order');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('lessons', function (Blueprint $table) {
            // Remove added columns
            $table->dropColumn(['title', 'description', 'is_published']);

            // Add back the original columns
            $table->string('type')->after('topic_id');
            $table->text('content')->nullable()->after('type');
            $table->json('media_path')->nullable()->after('content');
            $table->string('audio_path')->nullable()->after('media_path');
            $table->json('attributes')->nullable()->after('parent_id');
        });
    }
};
