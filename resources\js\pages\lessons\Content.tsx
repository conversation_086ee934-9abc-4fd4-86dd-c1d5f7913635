import BlockSelector from '@/components/ContentBlocks/BlockSelector';
import ContentBlockEditor from '@/components/ContentBlocks/ContentBlockEditor';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import AppLayout from '@/layouts/app-layout';
import { BreadcrumbItem, ContentBlock, Lesson, Topic } from '@/types';
import { DndContext, DragEndEvent, PointerSensor, closestCenter, useSensor, useSensors } from '@dnd-kit/core';
import { SortableContext, arrayMove, useSortable, verticalListSortingStrategy } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { Head, router, usePage } from '@inertiajs/react';
import axios from 'axios';
import { ChevronLeft, Move, Plus, Save } from 'lucide-react';
import { useState } from 'react';

// Sortable content block component
function SortableContentBlock({
    block,
    onUpdate,
    onDelete,
    onMoveUp,
    onMoveDown,
    showMoveUp,
    showMoveDown,
    isLast,
}: {
    block: ContentBlock;
    onUpdate: (updatedBlock: ContentBlock) => void;
    onDelete: (blockId: number) => void;
    onMoveUp: (blockId: number) => void;
    onMoveDown: (blockId: number) => void;
    showMoveUp: boolean;
    showMoveDown: boolean;
    isLast: boolean;
}) {
    const { attributes, listeners, setNodeRef, transform, transition } = useSortable({ id: block.id });

    const style = {
        transform: CSS.Transform.toString(transform),
        transition,
    };

    return (
        <div ref={setNodeRef} style={style} className="mb-4">
            <div {...attributes} {...listeners} className="mb-2 flex cursor-grab items-center justify-between rounded-md bg-gray-100 p-2">
                <div className="flex items-center gap-2">
                    <Move className="h-5 w-5 text-gray-500" />
                    <span className="text-sm font-medium">
                        Block #{block.order}: {block.type}
                    </span>
                </div>
                <div className="rounded-md bg-white px-2 py-1 text-xs text-gray-500">Drag to reorder</div>
            </div>
            <ContentBlockEditor
                block={block}
                onUpdate={onUpdate}
                onDelete={onDelete}
                onMoveUp={onMoveUp}
                onMoveDown={onMoveDown}
                showMoveUp={showMoveUp}
                showMoveDown={showMoveDown}
                isEditing={true}
            />
        </div>
    );
}

interface Props {
    lesson: Lesson;
    contentBlocks: ContentBlock[];
}

export default function Content({ lesson, contentBlocks }: Props) {
    const pageProps = usePage().props as any;
    const topic = pageProps.topic as Topic;
    const classId = pageProps.class?.id;
    const subjectId = pageProps.subject?.id;
    const unitId = pageProps.unit?.id;

    const [blocks, setBlocks] = useState<ContentBlock[]>(contentBlocks || []);
    const [isAddingBlock, setIsAddingBlock] = useState(false);
    const [isSaving, setIsSaving] = useState(false);

    // Set up sensors for drag and drop
    const sensors = useSensors(
        useSensor(PointerSensor, {
            activationConstraint: {
                distance: 8,
            },
        }),
    );

    const breadcrumbs: BreadcrumbItem[] = [
        {
            title: 'Topics',
            href: route('topics.index', { class_id: classId, subject_id: subjectId }),
        },
        {
            title: topic.name,
            href: route('lessons.index', { topic_id: topic.id }),
        },
        {
            title: lesson.title,
            href: '#',
        },
    ];

    const handleDragEnd = (event: DragEndEvent) => {
        const { active, over } = event;

        if (over && active.id !== over.id) {
            setBlocks((items) => {
                const oldIndex = items.findIndex((item) => item.id === active.id);
                const newIndex = items.findIndex((item) => item.id === over.id);

                const reordered = arrayMove(items, oldIndex, newIndex);

                // Update order values
                return reordered.map((item, index) => ({
                    ...item,
                    order: index + 1,
                }));
            });
        }
    };

    const handleAddBlock = async (type: string) => {
        try {
            const response = await axios.post(route('content-blocks.store'), {
                lesson_id: lesson.id,
                type,
                order: blocks.length + 1,
            });

            setBlocks([...blocks, response.data.content_block]);
            setIsAddingBlock(false);
        } catch (error) {
            console.error('Error adding content block:', error);
        }
    };

    const handleUpdateBlock = (updatedBlock: ContentBlock) => {
        setBlocks(blocks.map((block) => (block.id === updatedBlock.id ? updatedBlock : block)));
    };

    const handleDeleteBlock = async (blockId: number) => {
        try {
            await axios.delete(route('content-blocks.destroy', { content_block: blockId }));

            const updatedBlocks = blocks
                .filter((block) => block.id !== blockId)
                .map((block, index) => ({
                    ...block,
                    order: index + 1,
                }));

            setBlocks(updatedBlocks);
        } catch (error) {
            console.error('Error deleting content block:', error);
        }
    };

    const handleMoveUp = (blockId: number) => {
        setBlocks((blocks) => {
            const index = blocks.findIndex((block) => block.id === blockId);
            if (index <= 0) return blocks;

            const newBlocks = [...blocks];
            [newBlocks[index - 1], newBlocks[index]] = [newBlocks[index], newBlocks[index - 1]];

            return newBlocks.map((block, i) => ({
                ...block,
                order: i + 1,
            }));
        });
    };

    const handleMoveDown = (blockId: number) => {
        setBlocks((blocks) => {
            const index = blocks.findIndex((block) => block.id === blockId);
            if (index >= blocks.length - 1) return blocks;

            const newBlocks = [...blocks];
            [newBlocks[index], newBlocks[index + 1]] = [newBlocks[index + 1], newBlocks[index]];

            return newBlocks.map((block, i) => ({
                ...block,
                order: i + 1,
            }));
        });
    };

    const saveAllChanges = async () => {
        setIsSaving(true);

        try {
            await axios.post(route('content-blocks.reorder'), {
                blocks: blocks.map((block) => ({
                    id: block.id,
                    order: block.order,
                    lesson_id: lesson.id,
                })),
            });

            // Redirect back to lesson index
            router.visit(route('lessons.index', { topic_id: topic.id }));
        } catch (error) {
            console.error('Error saving content blocks:', error);
            setIsSaving(false);
        }
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title={`Edit Content - ${lesson.title}`} />
            <div className="container mx-auto px-4 py-8 sm:px-6 lg:px-8">
                <div className="mb-6 flex items-center justify-between">
                    <div className="flex items-center gap-4">
                        <Button variant="outline" size="icon" onClick={() => router.visit(route('lessons.index', { topic_id: topic.id }))}>
                            <ChevronLeft className="h-5 w-5" />
                        </Button>
                        <div>
                            <h1 className="text-2xl font-bold text-gray-900">Edit Content: {lesson.title}</h1>
                            <p className="text-sm text-gray-500">Add and arrange content blocks for this lesson</p>
                        </div>
                    </div>
                    <Button onClick={saveAllChanges} disabled={isSaving}>
                        <Save className="mr-2 h-4 w-4" />
                        {isSaving ? 'Saving...' : 'Save Changes'}
                    </Button>
                </div>

                <Separator className="my-6" />

                {/* Content blocks with drag and drop */}
                <div className="grid grid-cols-1 gap-8 lg:grid-cols-12">
                    <div className="lg:col-span-9">
                        {blocks.length > 0 ? (
                            <DndContext sensors={sensors} collisionDetection={closestCenter} onDragEnd={handleDragEnd}>
                                <SortableContext items={blocks.map((block) => block.id)} strategy={verticalListSortingStrategy}>
                                    <div>
                                        {blocks.map((block, index) => (
                                            <SortableContentBlock
                                                key={block.id}
                                                block={block}
                                                onUpdate={handleUpdateBlock}
                                                onDelete={handleDeleteBlock}
                                                onMoveUp={handleMoveUp}
                                                onMoveDown={handleMoveDown}
                                                showMoveUp={index > 0}
                                                showMoveDown={index < blocks.length - 1}
                                                isLast={index === blocks.length - 1}
                                            />
                                        ))}
                                    </div>
                                </SortableContext>
                            </DndContext>
                        ) : (
                            <Card>
                                <CardContent className="flex flex-col items-center justify-center py-12">
                                    <div className="mb-4 rounded-full bg-blue-100 p-3">
                                        <Plus className="h-6 w-6 text-blue-600" />
                                    </div>
                                    <h3 className="mb-1 text-lg font-medium">No content blocks yet</h3>
                                    <p className="mb-4 text-center text-gray-500">
                                        Start building your lesson by adding content blocks from the panel on the right.
                                    </p>
                                </CardContent>
                            </Card>
                        )}

                        {/* Block type selector */}
                        <div className="mt-8">
                            <Button
                                onClick={() => setIsAddingBlock(!isAddingBlock)}
                                variant={isAddingBlock ? 'secondary' : 'default'}
                                className="w-full justify-center"
                            >
                                <Plus className="mr-2 h-4 w-4" />
                                {isAddingBlock ? 'Cancel' : 'Add Content Block'}
                            </Button>

                            {isAddingBlock && (
                                <div className="mt-4">
                                    <BlockSelector onSelect={handleAddBlock} parentType={null} />
                                </div>
                            )}
                        </div>
                    </div>

                    {/* Preview panel */}
                    <div className="lg:col-span-3">
                        <div className="sticky top-4">
                            <div className="rounded-lg border border-gray-200 bg-white p-4 shadow-sm">
                                <h3 className="mb-3 text-lg font-medium">Lesson Preview</h3>
                                <Separator className="my-3" />
                                <div className="prose prose-sm max-w-none">
                                    {blocks.length > 0 ? (
                                        blocks.map((block) => {
                                            // Simple preview rendering based on block type
                                            switch (block.type) {
                                                case 'heading':
                                                    const level = block.attributes?.level || 1;
                                                    const HeadingTag = `h${level}` as keyof JSX.IntrinsicElements;
                                                    return (
                                                        <HeadingTag key={block.id} className="mt-4">
                                                            {block.content}
                                                        </HeadingTag>
                                                    );

                                                case 'paragraph':
                                                    return (
                                                        <p key={block.id} className="mt-2 mb-4">
                                                            {block.content}
                                                        </p>
                                                    );

                                                case 'image':
                                                    return (
                                                        <div key={block.id} className="my-4">
                                                            {block.media_path && (
                                                                <img
                                                                    src={`/storage/${block.media_path}`}
                                                                    alt={block.content || 'Image'}
                                                                    className="h-auto max-w-full rounded-md"
                                                                />
                                                            )}
                                                        </div>
                                                    );

                                                case 'list':
                                                    return (
                                                        <ul key={block.id} className="my-4 list-disc pl-5">
                                                            {block.children?.map((item) => <li key={item.id}>{item.content}</li>)}
                                                        </ul>
                                                    );

                                                default:
                                                    return null;
                                            }
                                        })
                                    ) : (
                                        <p className="text-gray-500 italic">Add content blocks to see a preview here.</p>
                                    )}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </AppLayout>
    );
}
