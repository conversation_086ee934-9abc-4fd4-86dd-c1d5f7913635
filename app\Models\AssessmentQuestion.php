<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class AssessmentQuestion extends Model
{
    //
    protected $fillable = [
        'assessment_id',
        'question',
        'question_type',
        'option_a',
        'option_b',
        'option_c',
        'option_d',
        'correct_answer',
        'audio_url',
    ];

    public function assessment()
    {
        return $this->belongsTo(Assessment::class);
    }
}
