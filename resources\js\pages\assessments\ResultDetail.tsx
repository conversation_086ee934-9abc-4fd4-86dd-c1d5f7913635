import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import AppLayout from '@/layouts/app-layout';
import { BreadcrumbItem } from '@/types';
import { Head, Link } from '@inertiajs/react';
import { ArrowLeft, CheckCircle, HelpCircle, Printer, XCircle } from 'lucide-react';
import { useRef } from 'react';
import { useReactToPrint } from 'react-to-print';

interface Assessment {
    id: number;
    title: string;
    description: string;
    class_id: number;
    subject_id: number;
    unit_id: number;
    topic_id: number;
    questions_count: number;
}

interface Student {
    id: number;
    name: string;
    email: string;
    avatar?: string;
}

interface QuestionDetail {
    id: number;
    question: string;
    question_type: 'text' | 'image';
    option_a: string;
    option_b: string;
    option_c: string;
    option_d: string;
    correct_answer: 'A' | 'B' | 'C' | 'D';
    student_answer: 'A' | 'B' | 'C' | 'D';
    is_correct: boolean;
}

interface AssessmentResult {
    id: number;
    assessment_id: number;
    student_id: number;
    score: number;
    failed_questions: number;
    correct_questions: number;
    status: 'passed' | 'failed';
    created_at: string;
    student: Student;
    questions: QuestionDetail[];
}

interface Props {
    assessment: Assessment;
    result: AssessmentResult;
}

function ResultDetail({ assessment, result }: Props) {
    const printRef = useRef<HTMLDivElement>(null);

    const handlePrint = useReactToPrint({
        content: () => printRef.current,
    });

    // Generate certificate data
    const generatePDF = () => {
        // This would ideally generate and download a PDF certificate
        // For now, we'll just use the print functionality
        handlePrint();
    };

    const breadcrumbs: BreadcrumbItem[] = [
        {
            title: 'Assessments',
            href: '/assessments',
        },
        {
            title: assessment.title,
            href: route('assessments.index', { class_id: assessment.class_id, subject_id: assessment.subject_id }),
        },
        {
            title: 'Results',
            href: `/assessments/${assessment.id}/results`,
        },
        {
            title: result.student.name,
            href: `/assessments/${assessment.id}/results/${result.id}`,
        },
    ];

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title={`${assessment.title} - ${result.student.name}'s Results`} />
            <div className="container mx-auto p-6">
                <div className="mb-8 flex items-center justify-between">
                    <div>
                        <div className="flex items-center gap-2">
                            <Link href={`/assessments/${assessment.id}/results`}>
                                <Button variant="ghost" size="icon" className="rounded-full">
                                    <ArrowLeft className="h-5 w-5" />
                                </Button>
                            </Link>
                            <h1 className="text-2xl font-bold text-gray-900">{result.student.name}'s Results</h1>
                        </div>
                        <p className="text-sm text-gray-500">{assessment.title}</p>
                    </div>
                    <div className="flex gap-2">
                        <Button variant="outline" onClick={handlePrint}>
                            <Printer className="mr-2 h-4 w-4" /> Print Results
                        </Button>
                    </div>
                </div>

                <div ref={printRef}>
                    {/* Results Summary */}
                    <div className="mb-8 rounded-lg border bg-white p-6 shadow">
                        <div className="grid gap-6 md:grid-cols-3">
                            <div className="text-center">
                                <h3 className="text-sm font-medium text-gray-500">Total Score</h3>
                                <div
                                    className={`mt-2 text-3xl font-bold ${
                                        result.score >= 80 ? 'text-green-600' : result.score >= 60 ? 'text-yellow-600' : 'text-red-600'
                                    }`}
                                >
                                    {result.score}%
                                </div>
                                <Progress
                                    value={result.score}
                                    className="mt-2"
                                    indicatorClassName={result.score >= 80 ? 'bg-green-600' : result.score >= 60 ? 'bg-yellow-600' : 'bg-red-600'}
                                />
                            </div>

                            <div className="text-center">
                                <h3 className="text-sm font-medium text-gray-500">Correct Answers</h3>
                                <div className="mt-2 text-3xl font-bold text-green-600">
                                    {result.correct_questions} <span className="text-base text-gray-500">/ {assessment.questions_count}</span>
                                </div>
                                <div className="mt-2 flex justify-center">
                                    <CheckCircle className="h-6 w-6 text-green-600" />
                                </div>
                            </div>

                            <div className="text-center">
                                <h3 className="text-sm font-medium text-gray-500">Status</h3>
                                <div className="mt-2 text-3xl font-bold">
                                    <span className={result.status === 'passed' ? 'text-green-600' : 'text-red-600'}>
                                        {result.status.charAt(0).toUpperCase() + result.status.slice(1)}
                                    </span>
                                </div>
                                <div className="mt-2 flex justify-center">
                                    {result.status === 'passed' ? (
                                        <CheckCircle className="h-6 w-6 text-green-600" />
                                    ) : (
                                        <XCircle className="h-6 w-6 text-red-600" />
                                    )}
                                </div>
                            </div>
                        </div>

                        <Separator className="my-6" />

                        <div className="flex flex-wrap gap-4">
                            <div>
                                <p className="text-sm text-gray-500">Student Name:</p>
                                <p className="font-medium">{result.student.name}</p>
                            </div>
                            <div>
                                <p className="text-sm text-gray-500">Email:</p>
                                <p className="font-medium">{result.student.email}</p>
                            </div>
                            <div>
                                <p className="text-sm text-gray-500">Assessment:</p>
                                <p className="font-medium">{assessment.title}</p>
                            </div>
                            <div>
                                <p className="text-sm text-gray-500">Date:</p>
                                <p className="font-medium">{new Date(result.created_at).toLocaleDateString()}</p>
                            </div>
                        </div>
                    </div>

                    {/* Questions and Answers */}
                    <h2 className="mb-4 text-xl font-bold">Questions & Answers</h2>
                    <div className="space-y-6">
                        {result.questions.map((question, index) => (
                            <Card key={question.id} className="overflow-hidden">
                                <CardHeader className={`pb-3 ${question.is_correct ? 'bg-green-50' : 'bg-red-50'}`}>
                                    <div className="flex items-start gap-3">
                                        <div
                                            className={`flex h-8 w-8 flex-shrink-0 items-center justify-center rounded-full ${
                                                question.is_correct ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                                            }`}
                                        >
                                            {index + 1}
                                        </div>
                                        <div className="flex-1">
                                            <CardTitle className="text-base">{question.question}</CardTitle>
                                            <div className="mt-1 flex items-center gap-2">
                                                {question.is_correct ? (
                                                    <>
                                                        <CheckCircle className="h-4 w-4 text-green-600" />
                                                        <span className="text-sm text-green-600">Correct</span>
                                                    </>
                                                ) : (
                                                    <>
                                                        <XCircle className="h-4 w-4 text-red-600" />
                                                        <span className="text-sm text-red-600">Incorrect</span>
                                                    </>
                                                )}
                                            </div>
                                        </div>
                                    </div>
                                </CardHeader>
                                <CardContent className="p-5">
                                    {question.question_type === 'text' ? (
                                        <div className="grid grid-cols-1 gap-3 sm:grid-cols-2">
                                            {['A', 'B', 'C', 'D'].map((option) => {
                                                const optionKey = `option_${option.toLowerCase()}` as keyof typeof question;
                                                const isCorrectOption = question.correct_answer === option;
                                                const isSelectedOption = question.student_answer === option;

                                                let optionClassName = 'flex items-center rounded-md border p-3 ';

                                                if (isSelectedOption && isCorrectOption) {
                                                    optionClassName += 'border-green-500 bg-green-50';
                                                } else if (isSelectedOption && !isCorrectOption) {
                                                    optionClassName += 'border-red-500 bg-red-50';
                                                } else if (isCorrectOption) {
                                                    optionClassName += 'border-green-500 bg-green-50';
                                                } else {
                                                    optionClassName += 'border-gray-200';
                                                }

                                                return (
                                                    <div key={option} className={optionClassName}>
                                                        <div
                                                            className={`mr-3 flex h-6 w-6 items-center justify-center rounded-full ${
                                                                isCorrectOption
                                                                    ? 'bg-green-500 text-white'
                                                                    : isSelectedOption
                                                                      ? 'bg-red-500 text-white'
                                                                      : 'bg-gray-200 text-gray-700'
                                                            }`}
                                                        >
                                                            {option}
                                                        </div>
                                                        <span className="flex-1">{question[optionKey] as string}</span>
                                                        {isCorrectOption && <CheckCircle className="ml-auto h-4 w-4 text-green-500" />}
                                                        {isSelectedOption && !isCorrectOption && <XCircle className="ml-auto h-4 w-4 text-red-500" />}
                                                    </div>
                                                );
                                            })}
                                        </div>
                                    ) : (
                                        <div className="grid grid-cols-1 gap-3 sm:grid-cols-2">
                                            {['A', 'B', 'C', 'D'].map((option) => {
                                                const optionKey = `option_${option.toLowerCase()}` as keyof typeof question;
                                                const isCorrectOption = question.correct_answer === option;
                                                const isSelectedOption = question.student_answer === option;

                                                let optionClassName = 'flex flex-col overflow-hidden rounded-md border ';

                                                if (isSelectedOption && isCorrectOption) {
                                                    optionClassName += 'border-green-500';
                                                } else if (isSelectedOption && !isCorrectOption) {
                                                    optionClassName += 'border-red-500';
                                                } else if (isCorrectOption) {
                                                    optionClassName += 'border-green-500';
                                                } else {
                                                    optionClassName += 'border-gray-200';
                                                }

                                                return (
                                                    <div key={option} className={optionClassName}>
                                                        <div
                                                            className={`flex items-center justify-between px-3 py-2 ${
                                                                isCorrectOption
                                                                    ? 'bg-green-500 text-white'
                                                                    : isSelectedOption
                                                                      ? 'bg-red-500 text-white'
                                                                      : 'bg-gray-100'
                                                            }`}
                                                        >
                                                            <span>Option {option}</span>
                                                            {isCorrectOption && <CheckCircle className="h-4 w-4" />}
                                                            {isSelectedOption && !isCorrectOption && <XCircle className="h-4 w-4" />}
                                                        </div>
                                                        <div className="p-2">
                                                            <img
                                                                src={question[optionKey] as string}
                                                                alt={`Option ${option}`}
                                                                className="aspect-video w-full rounded object-cover"
                                                            />
                                                        </div>
                                                    </div>
                                                );
                                            })}
                                        </div>
                                    )}

                                    {!question.is_correct && (
                                        <div className="mt-4 rounded-md bg-blue-50 p-3 text-sm">
                                            <div className="mb-1 flex items-center">
                                                <HelpCircle className="mr-2 h-4 w-4 text-blue-600" />
                                                <span className="font-medium text-blue-900">Explanation</span>
                                            </div>
                                            <p className="text-blue-800">
                                                The correct answer is Option {question.correct_answer}.
                                                {question.question_type === 'text' && (
                                                    <> "{question[`option_${question.correct_answer.toLowerCase()}`] as string}"</>
                                                )}
                                            </p>
                                        </div>
                                    )}
                                </CardContent>
                            </Card>
                        ))}
                    </div>
                </div>
            </div>
        </AppLayout>
    );
}

export default ResultDetail;
