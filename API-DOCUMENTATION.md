# Nthanda API Documentation

## Student Authentication API

This documentation outlines the API endpoints for student authentication in the Nthanda educational application.

### Overview

The Nthanda application uses a PIN-based authentication system for students, secured with Laravel Sanctum for API token authentication.

### Endpoints

#### Login

Authenticates a student using their PIN and returns student information with an API token.

- **URL:** `/api/student/login`
- **Method:** `POST`
- **Request Body:**
    ```json
    {
        "pin": "1234" // 4-digit PIN
    }
    ```
- **Success Response:**
    - **Code:** 200
    - **Content:**
        ```json
        {
            "success": true,
            "message": "Login successful",
            "data": {
                "id": 1,
                "name": "Student Name",
                "email": "<EMAIL>",
                "role": "student",
                "class_id": 1,
                "pin": "1234",
                "created_at": "2023-07-15T10:20:30.000000Z",
                "updated_at": "2023-07-15T10:20:30.000000Z",
                "progress": 65,
                "class": {
                    "id": 1,
                    "name": "Grade 2",
                    "created_at": "2023-07-15T10:20:30.000000Z",
                    "updated_at": "2023-07-15T10:20:30.000000Z"
                }
            },
            "token": "1|abcdefghijklmnopqrstuvwxyz123456789"
        }
        ```
- **Error Responses:**
    - **Code:** 401 UNAUTHORIZED
        ```json
        {
            "success": false,
            "message": "Invalid PIN"
        }
        ```
    - **Code:** 429 TOO MANY REQUESTS
        ```json
        {
            "success": false,
            "message": "Too many login attempts. Please try again later."
        }
        ```

#### Logout

Logs out the current student by revoking their API token.

- **URL:** `/api/student/logout`
- **Method:** `POST`
- **Headers:** `Authorization: Bearer {token}`
- **Success Response:**
    - **Code:** 200
    - **Content:**
        ```json
        {
            "success": true,
            "message": "Logged out successfully"
        }
        ```
- **Error Response:**
    - **Code:** 401 UNAUTHORIZED
        ```json
        {
            "message": "Unauthenticated"
        }
        ```

#### Get Current Student

Retrieves the information of the currently authenticated student.

- **URL:** `/api/student/me`
- **Method:** `GET`
- **Headers:** `Authorization: Bearer {token}`
- **Success Response:**
    - **Code:** 200
    - **Content:**
        ```json
        {
            "success": true,
            "data": {
                "id": 1,
                "name": "Student Name",
                "email": "<EMAIL>",
                "role": "student",
                "class_id": 1,
                "pin": "1234",
                "created_at": "2023-07-15T10:20:30.000000Z",
                "updated_at": "2023-07-15T10:20:30.000000Z",
                "progress": 65,
                "class": {
                    "id": 1,
                    "name": "Grade 2",
                    "created_at": "2023-07-15T10:20:30.000000Z",
                    "updated_at": "2023-07-15T10:20:30.000000Z"
                }
            }
        }
        ```
- **Error Response:**
    - **Code:** 401 UNAUTHORIZED
        ```json
        {
            "message": "Unauthenticated"
        }
        ```
    - **Code:** 403 FORBIDDEN
        ```json
        {
            "success": false,
            "message": "Access denied. User is not a student."
        }
        ```

### Security Considerations

1. **Token Expiration**: By default, Sanctum tokens do not expire. Configure token expiration in `config/sanctum.php` as needed.
2. **Rate Limiting**: Apply rate limiting to the login endpoint to prevent brute force attacks.
3. **CORS Configuration**: Ensure CORS is properly configured for front-end requests in `config/cors.php`.

### Implementation Notes

- The student authentication system uses Laravel Sanctum for API token authentication.
- PIN-based authentication is appropriate for a child-friendly educational application.
- The database should include a `pin` field on the `users` table for storing student PINs.
- Consider adding additional security measures like rate limiting or PIN expiration for production environments.

## Implementation Details

### Components Created

1. **API Routes**:

    - Defined in `routes/api.php`
    - Added protected routes with Sanctum authentication
    - Applied rate limiting middleware to login endpoint

2. **Controller**:

    - Created `StudentAuthController` in `app/Http/Controllers/Api/`
    - Implemented methods for login, logout, and retrieving student information
    - Added proper validation, error handling, and responses

3. **Middleware**:

    - Created `StudentLoginRateLimiter` middleware in `app/Http/Middleware/`
    - Limits login attempts to 5 per minute from the same IP address
    - Automatically clears rate limiting after successful login

4. **Model Updates**:

    - Updated `User` model to support Sanctum authentication
    - Added relationship between `User` and `SchoolClass` models
    - Created association from `SchoolClass` to `User` for retrieving students

5. **Frontend Integration**:
    - Frontend files (`login.html` and `auth.js`) already configured to work with API endpoints
    - Implemented proper token storage and authentication header management
    - Added fallback for when API is not available (for development)

### Testing Instructions

1. Start the Laravel development server:

    ```
    php artisan serve
    ```

2. Use the login.html page to test authentication with a valid PIN

    - Navigate to http://localhost:8000/nthanda/login.html
    - Enter PIN "1234" for testing (or a valid PIN from the database)

3. For API testing with curl:

    ```
    curl -X POST "http://localhost:8000/api/student/login" -H "Content-Type: application/json" -d '{"pin":"1234"}'
    ```

4. After login, test the protected endpoint:
    ```
    curl -X GET "http://localhost:8000/api/student/me" -H "Authorization: Bearer YOUR_TOKEN_HERE"
    ```
