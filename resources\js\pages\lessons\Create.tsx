import { Button } from '@/components/ui/button';
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogFooter, <PERSON>alogHeader, DialogTitle } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Topic } from '@/types';
import { router, usePage } from '@inertiajs/react';
import { Plus } from 'lucide-react';
import { useState } from 'react';

interface CreateProps {
    className?: string;
}

export default function Create({ className }: CreateProps) {
    const [open, setOpen] = useState(false);
    const [processing, setProcessing] = useState(false);
    const pageProps = usePage().props as any;
    const topic = pageProps.topic as Topic;

    const [title, setTitle] = useState('');
    const [description, setDescription] = useState('');

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        setProcessing(true);

        router.post(
            route('lessons.store'),
            {
                title,
                description,
                topic_id: topic.id,
            },
            {
                onSuccess: () => {
                    setOpen(false);
                    setTitle('');
                    setDescription('');
                    setProcessing(false);
                },
                onError: () => {
                    setProcessing(false);
                },
            },
        );
    };

    return (
        <>
            <Button onClick={() => setOpen(true)} className={className}>
                <Plus className="mr-2 h-4 w-4" /> Create Lesson
            </Button>

            <Dialog open={open} onOpenChange={setOpen}>
                <DialogContent className="sm:max-w-md">
                    <DialogHeader>
                        <DialogTitle>Create New Lesson</DialogTitle>
                        <DialogDescription>Add a new lesson to {topic.name}</DialogDescription>
                    </DialogHeader>

                    <form onSubmit={handleSubmit} className="space-y-4 pt-4">
                        <div className="grid gap-2">
                            <Label htmlFor="title">Lesson Title</Label>
                            <Input
                                id="title"
                                type="text"
                                value={title}
                                onChange={(e) => setTitle(e.target.value)}
                                placeholder="Enter lesson title"
                                required
                                autoFocus
                            />
                        </div>

                        <div className="grid gap-2">
                            <Label htmlFor="description">Description (Optional)</Label>
                            <Textarea
                                id="description"
                                value={description}
                                onChange={(e) => setDescription(e.target.value)}
                                placeholder="Enter lesson description"
                                rows={3}
                            />
                        </div>

                        <DialogFooter className="mt-6">
                            <Button type="button" variant="outline" onClick={() => setOpen(false)}>
                                Cancel
                            </Button>
                            <Button type="submit" disabled={processing || !title.trim()}>
                                {processing ? 'Creating...' : 'Create Lesson'}
                            </Button>
                        </DialogFooter>
                    </form>
                </DialogContent>
            </Dialog>
        </>
    );
}
