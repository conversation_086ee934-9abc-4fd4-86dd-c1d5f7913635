import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, Di<PERSON>Header, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { ContentBlockType } from '@/types';
import { Plus } from 'lucide-react';
import { useState } from 'react';
import { BLOCK_TYPES, getBlockTypeForParent } from './BlockTypes';

interface BlockSelectorProps {
    lessonId: number;
    parentId?: number | null;
    parentType?: ContentBlockType | null;
    onBlockSelected: (type: ContentBlockType) => void;
    label?: string;
    className?: string;
}

export default function BlockSelector({
    lessonId,
    parentId = null,
    parentType = null,
    onBlockSelected,
    label = 'Add Content Block',
    className,
}: BlockSelectorProps) {
    const [open, setOpen] = useState(false);
    const allowedTypes = getBlockTypeForParent(parentType);

    const handleBlockSelect = (type: ContentBlockType) => {
        onBlockSelected(type);
        setOpen(false);
    };

    return (
        <Dialog open={open} onOpenChange={setOpen}>
            <DialogTrigger asChild>
                <Button variant="outline" className={className}>
                    <Plus className="mr-2 h-4 w-4" /> {label}
                </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-md">
                <DialogHeader>
                    <DialogTitle>Add Content Block</DialogTitle>
                </DialogHeader>
                <div className="grid grid-cols-1 gap-3 py-4 sm:grid-cols-2">
                    {allowedTypes.map((type) => {
                        const blockType = BLOCK_TYPES[type];
                        return (
                            <Button
                                key={type}
                                variant="outline"
                                className="flex h-24 flex-col items-center justify-center gap-2 text-center"
                                onClick={() => handleBlockSelect(type)}
                            >
                                <div>{blockType.icon}</div>
                                <div className="flex flex-col">
                                    <span className="text-sm font-medium">{blockType.label}</span>
                                    <span className="text-xs text-gray-500">{blockType.description}</span>
                                </div>
                            </Button>
                        );
                    })}
                </div>
            </DialogContent>
        </Dialog>
    );
}
