<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('assessment_question_details', function (Blueprint $table) {
            $table->id();
            $table->foreignId('assessment_result_id')->constrained('assessment_results')->onDelete('cascade');
            $table->foreignId('assessment_question_id')->constrained('assessment_questions')->onDelete('cascade');
            $table->string('student_answer');
            $table->boolean('is_correct');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('assessment_question_details');
    }
};
