<?php

namespace App\Http\Middleware;

use App\Models\User;
use Carbon\Carbon;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class RecordLastLoginTime
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (Auth::check()) {
            /** @var User $user */
            $user = Auth::user();

            // Only update once per day to avoid unnecessary database writes
            if (!$user->last_login_at || Carbon::parse($user->last_login_at)->isYesterday() || Carbon::parse($user->last_login_at)->lessThan(Carbon::today())) {
                $user->last_login_at = Carbon::now();
                $user->save();
            }
        }

        return $next($request);
    }
}
