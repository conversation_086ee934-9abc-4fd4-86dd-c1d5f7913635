:root {
  --color-blue: #1c407c;
  --color-yellow: #ffd93d;
  --color-dark-yellow: #e6c235;
  --color-white: #ffffff;
  --color-red: #ff5252;
  --color-green: #4caf50;
  --color-dark-green: #388e3c;
}

@font-face {
  font-family: "Fredoka";
  src: url("assets/Fredoka/Fredoka-VariableFont_wdth\,wght.ttf");
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: "Fredoka";
  src: url("assets/Fredoka/static/Fredoka-Bold.ttf");
  font-weight: 700;
  font-style: normal;
}

@font-face {
  font-family: "Fredoka";
  src: url("assets/Fredoka/static/Fredoka-Regular.ttf");
  font-weight: 400;
  font-style: normal;
}

@font-face {
  font-family: "Fredoka";
  src: url("assets/Fredoka/static/Fredoka-SemiBold.ttf");
  font-weight: 600;
  font-style: normal;
}

body {
  font-family: "Fredoka", sans-serif;
  background-color: var(--color-yellow);
  margin: 0;
  padding: 0;
  color: var(--color-white);
  overflow-x: hidden;
}

.container {
  max-width: 600px;
  margin: 0 auto;
  padding: 16px;
}

/* Home page styles */
.user-info {
  background-color: var(--color-white);
  border-radius: 16px;
  padding: 20px;
  margin-top: 20px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  color: var(--color-blue);
}

.user-info h2 {
  color: var(--color-blue);
  font-weight: 700;
  margin-top: 0;
  margin-bottom: 16px;
  border-bottom: 2px solid var(--color-yellow);
  padding-bottom: 8px;
}

.user-info p {
  margin: 8px 0;
  font-size: 16px;
}

.user-info a {
  color: var(--color-blue);
  text-decoration: none;
  font-weight: 600;
}

.user-info a:hover {
  text-decoration: underline;
  color: var(--color-green);
}

/* Welcome page styles */
.welcome-container {
  position: relative;
  min-height: 100vh;
  width: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  box-sizing: border-box;
}

.corner-image {
  position: absolute;
  width: 20%;
  max-width: 100px;
}

.top-left-image {
  top: -12px;
  left: -12px;
}

.top-right-image {
  top: -12px;
  right: -12px;
}

.welcome-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  z-index: 1;
}

.logo {
  width: 100%;
  max-width: 100px;
  margin-bottom: 20px;
}

.main-image {
  width: 50%;
  max-width: 200px;
  margin-bottom: 20px;
  animation: bounce 2s infinite ease-in-out;
}

@keyframes bounce {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.welcome-text {
  font-size: 36px;
  font-weight: 700;
  color: var(--color-blue);
  text-align: center;
  margin-bottom: 10px;
  animation: fadeIn 1s ease-in-out;
}

.sub-text {
  font-size: 28px;
  color: var(--color-blue);
  font-weight: 500;
  text-align: center;
  margin-bottom: 20px;
  animation: fadeIn 1s ease-in-out;
}

.tiger-image {
  width: 50%;
  max-width: 200px;
  margin-bottom: 0px;
}

.welcome-button {
  border-radius: 30px;
  padding: 12px 40px;
  background-color: var(--color-green);
  color: var(--color-white);
  font-weight: 700;
  font-size: 20px;
  cursor: pointer;
  width: 100%;
  max-width: 200px;
  text-align: center;
  letter-spacing: 1px;
  border: 4px solid var(--color-dark-green);
  border-left-width: 2px;
  border-right-width: 2px;
  border-top-width: 0;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s;
  text-decoration: none;
  display: block;
}

.welcome-button:hover {
  transform: translateY(-2px);
}

.welcome-button:active {
  transform: translateY(1px);
}

/* Original styles */
.title {
  color: var(--color-white);
  font-weight: 700;
  text-align: left;
  margin-bottom: 8px;
  font-size: 24px;
}

.subtitle {
  color: var(--color-white);
  font-weight: 400;
  text-align: center;
  margin-bottom: 24px;
  font-size: 14px;
}

.section-title {
  color: var(--color-white);
  font-weight: 600;
  font-size: 16px;
  margin-bottom: 16px;
  margin-top: 8px;
}

.input {
  margin-bottom: 16px;
  background-color: white;
  padding: 12px;
  border-radius: 4px;
  border: 2px solid var(--color-blue);
  width: 100%;
  box-sizing: border-box;
}

.input:focus {
  border-color: var(--color-blue);
  outline: none;
}

.error-text {
  color: var(--color-red);
  font-size: 14px;
  margin-bottom: 16px;
  text-align: center;
}

.button {
  margin-top: 24px;
  border-radius: 10px;
  padding: 12px;
  background-color: var(--color-yellow);
  border-width: 4px;
  border-top-width: 0;
  border-color: var(--color-dark-yellow);
  font-weight: 700;
  font-size: 16px;
  cursor: pointer;
  width: 100%;
  text-align: center;
  color: var(--color-blue);
}

.gender-container {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
  gap: 16px;
}

.gender-button {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: var(--color-white);
  border-radius: 16px;
  padding: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.25);
  position: relative;
  cursor: pointer;
}

.gender-image {
  width: 100px;
  height: 100px;
  border-radius: 16px;
  margin-bottom: 12px;
  object-fit: cover;
}

.gender-text {
  font-weight: 600;
  font-size: 16px;
  color: var(--color-blue);
  text-align: center;
}

.selected-gender {
  border: 2px solid var(--color-yellow);
  background-color: var(--color-white);
}

.class-grid {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  gap: 16px;
}

.class-card {
  width: calc(50% - 16px);
  padding: 16px;
  background-color: var(--color-white);
  border-radius: 16px;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.25);
  position: relative;
  cursor: pointer;
}

.selected-class {
  border: 2px solid var(--color-yellow);
  background-color: var(--color-white);
}

.class-icon-container {
  width: 56px;
  height: 56px;
  border-radius: 28px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.class-name {
  font-weight: 600;
  font-size: 16px;
  color: var(--color-blue);
  margin-top: 12px;
  text-align: center;
}

.subjects-count {
  font-weight: 400;
  font-size: 12px;
  color: #666;
  margin-top: 4px;
}

.check-circle {
  position: absolute;
  top: 8px;
  right: 8px;
  background-color: var(--color-yellow);
  border-radius: 20px;
  width: 40px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.25);
}

@media (max-width: 480px) {
  .class-card {
    width: 100%;
  }
}

.content-empty {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
  background-color: var(--color-white);
  border-radius: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.25);
}

.content-empty-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--color-blue);
}
