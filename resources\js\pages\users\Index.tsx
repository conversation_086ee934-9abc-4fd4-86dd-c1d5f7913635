import { Separator } from '@/components/ui/separator';
import UserCard from '@/components/UserCard';
import AppLayout from '@/layouts/app-layout';
import TabLayout from '@/layouts/TabLayout';
import { BreadcrumbItem, NavItem, User } from '@/types';
import { Head, usePage } from '@inertiajs/react';
import { useState } from 'react';
import Create from './Create';
import Delete from './Delete';
import EditUser from './Edit';
interface Props {
    users: {
        data: User[];
        current_page: number;
        last_page: number;
        per_page: number;
        total: number;
        from: number;
        to: number;
    };
}

function Index({ users }: Props) {
    const [editingUser, setEditingUser] = useState<User | null>(null);
    const [deletingUser, setDeletingUser] = useState<User | null>(null);
    const { role } = usePage().props;

    const breadcrumbs: BreadcrumbItem[] = [
        {
            title: 'Users',
            href: '/users',
        },
    ];

    const sidebarNavItems: NavItem[] = [
        {
            title: 'Administrators',
            href: route('users.index', { role: 'admin' }),
            icon: null,
            isActive: role === 'admin',
        },
        {
            title: 'Teachers',
            href: route('users.index', { role: 'teacher' }),
            icon: null,
            isActive: role === 'teacher',
        },
    ];

    // Handlers for user actions
    const handleEditUser = (user: User) => {
        setEditingUser(user);
    };

    const handleDeleteUser = (user: User) => {
        setDeletingUser(user);
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Users" />

            <TabLayout sidebarNavItems={sidebarNavItems} title="Users" description="Manage your administrators and teachers">
                <div className="w-full">
                    <div className="mx-auto w-full px-4 sm:px-6 lg:px-8">
                        <div className="mb-6 flex items-center justify-between">
                            <h2 className="text-2xl font-semibold">{role === 'admin' ? 'Administrators' : 'Teachers'}</h2>
                            <Create />
                        </div>
                        <Separator className="my-6" />

                        {/* User Cards Grid */}
                        <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
                            {users.data.map((user) => (
                                <UserCard key={user.id} user={user} onEdit={handleEditUser} onDelete={handleDeleteUser} />
                            ))}
                        </div>

                        {/* Empty state when no users */}
                        {users.data.length === 0 && (
                            <div className="flex flex-col items-center justify-center rounded-lg border-2 border-dashed border-gray-300 p-12 text-center">
                                <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth={2}
                                        d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"
                                    />
                                </svg>
                                <h3 className="mt-2 text-sm font-medium text-gray-900">No {role === 'admin' ? 'administrators' : 'teachers'}</h3>
                                <p className="mt-1 text-sm text-gray-500">
                                    Get started by creating a new {role === 'admin' ? 'administrator' : 'teacher'}.
                                </p>
                            </div>
                        )}

                        {/* Pagination */}
                        {users.last_page > 1 && (
                            <div className="mt-6 flex items-center justify-between">
                                <div className="text-sm text-gray-700">
                                    Showing {users.from} to {users.to} of {users.total} results
                                </div>
                                <div className="flex space-x-2">
                                    {Array.from({ length: users.last_page }, (_, i) => i + 1).map((page) => (
                                        <a
                                            key={page}
                                            href={`/users?page=${page}`}
                                            className={`rounded px-3 py-1 ${
                                                page === users.current_page ? 'bg-blue-950 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                                            }`}
                                        >
                                            {page}
                                        </a>
                                    ))}
                                </div>
                            </div>
                        )}
                    </div>
                </div>

                {/* Edit Dialog */}
                {editingUser && <EditUser user={editingUser} open={!!editingUser} onOpenChange={(open: boolean) => !open && setEditingUser(null)} />}

                {/* Delete Dialog */}
                {deletingUser && (
                    <Delete user={deletingUser} open={!!deletingUser} onOpenChange={(open: boolean) => !open && setDeletingUser(null)} />
                )}
            </TabLayout>
        </AppLayout>
    );
}

export default Index;
