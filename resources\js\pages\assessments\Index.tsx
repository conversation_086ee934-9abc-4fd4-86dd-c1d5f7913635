import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import { Separator } from '@/components/ui/separator';
import AppLayout from '@/layouts/app-layout';
import TabLayout from '@/layouts/TabLayout';
import { BreadcrumbItem, NavItem, SchoolClass, Subject } from '@/types';
import { Head, Link, usePage } from '@inertiajs/react';
import { BarChart2, BookOpen, Edit, FileQuestion, MoreVertical, Search, Trash, Users } from 'lucide-react';
import { useEffect, useState } from 'react';
import Create from './Create';
import DeleteAssessment from './Delete';
import EditAssessment from './Edit';

interface Assessment {
    id: number;
    title: string;
    description: string;
    questions_count: number;
    subject_id: number;
    class_id: number;
    unit_id: number;
    topic_id: number;
    results_count: number;
}

interface Props {
    assessments: {
        data: Assessment[];
        current_page: number;
        last_page: number;
        per_page: number;
        total: number;
        from: number;
        to: number;
    };
}

function Index({ assessments }: Props) {
    const schoolClass = usePage().props.class as SchoolClass;
    const class_id = schoolClass.id;
    const className = schoolClass.name;
    const subjects = usePage().props.subjects as Subject[];
    const subject = usePage().props.subject as Subject;
    const [searchTerm, setSearchTerm] = useState('');
    const [filteredAssessments, setFilteredAssessments] = useState<Assessment[]>(assessments.data);
    const [editingAssessment, setEditingAssessment] = useState<Assessment | null>(null);
    const [deletingAssessment, setDeletingAssessment] = useState<Assessment | null>(null);

    useEffect(() => {
        if (searchTerm.trim() === '') {
            setFilteredAssessments(assessments.data);
        } else {
            const filtered = assessments.data.filter((assessment) => assessment.title.toLowerCase().includes(searchTerm.toLowerCase()));
            setFilteredAssessments(filtered);
        }
    }, [searchTerm, assessments.data]);

    const breadcrumbs: BreadcrumbItem[] = [
        {
            title: 'Assessments',
            href: '/assessments',
        },
    ];

    const sidebarNavItems: NavItem[] = subjects.map((subjectItem: Subject) => ({
        title: subjectItem.name,
        href: route('assessments.index', { class_id: class_id, subject_id: subjectItem.id }),
        route: route('assessments.index', { class_id: class_id, subject_id: subjectItem.id }),
        isActive: subjectItem.id == subject.id,
        count: subjectItem.assessments_count,
    }));

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Assessments" />
            <TabLayout title={className} description={`manage your assessments for ${subject.name}`} sidebarNavItems={sidebarNavItems}>
                <div className="w-full">
                    <div className="mx-auto w-full px-4 sm:px-6 lg:px-8">
                        <div className="mb-6 flex items-center justify-between">
                            <h2 className="text-2xl font-semibold">{subject.name}</h2>
                            <Create />
                        </div>
                        <Separator className="my-6" />

                        {/* Search and filter section */}
                        <div className="mb-6">
                            <div className="relative">
                                <Search className="absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 text-gray-500" />
                                <Input
                                    placeholder="Search assessments by title..."
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                    className="pl-10"
                                />
                            </div>
                        </div>

                        {/* Assessment Cards Grid */}
                        {assessments.data.length > 0 && (
                            <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
                                {filteredAssessments.map((assessment) => (
                                    <Card key={assessment.id} className="overflow-hidden transition-all hover:shadow-md">
                                        <CardHeader className="flex flex-row items-center justify-between bg-blue-950 p-4 text-white">
                                            <div className="line-clamp-1 text-lg font-semibold">{assessment.title}</div>
                                            <div className="flex items-center gap-2">
                                                <DropdownMenu>
                                                    <DropdownMenuTrigger className="cursor-pointer rounded-md p-1 text-gray-400 hover:bg-gray-100">
                                                        <MoreVertical className="h-5 w-5" />
                                                    </DropdownMenuTrigger>
                                                    <DropdownMenuContent align="end">
                                                        <DropdownMenuItem className="cursor-pointer" onClick={() => setEditingAssessment(assessment)}>
                                                            <Edit className="mr-2 h-4 w-4" />
                                                            Edit
                                                        </DropdownMenuItem>
                                                        <DropdownMenuItem
                                                            variant="destructive"
                                                            className="cursor-pointer"
                                                            onClick={() => setDeletingAssessment(assessment)}
                                                        >
                                                            <Trash className="mr-2 h-4 w-4" />
                                                            Delete
                                                        </DropdownMenuItem>
                                                    </DropdownMenuContent>
                                                </DropdownMenu>
                                            </div>
                                        </CardHeader>
                                        <CardContent className="p-4">
                                            <p className="mb-4 text-sm text-gray-600">{assessment.description || 'No description provided'}</p>

                                            <div className="mb-4 grid grid-cols-2 gap-4">
                                                <div className="flex items-center rounded-md bg-blue-50 p-3">
                                                    <FileQuestion className="mr-2 h-5 w-5 text-blue-600" />
                                                    <div>
                                                        <p className="text-xs text-gray-500">Questions</p>
                                                        <p className="font-medium">{assessment.questions_count || 0}</p>
                                                    </div>
                                                </div>
                                                <div className="flex items-center rounded-md bg-green-50 p-3">
                                                    <Users className="mr-2 h-5 w-5 text-green-600" />
                                                    <div>
                                                        <p className="text-xs text-gray-500">Students</p>
                                                        <p className="font-medium">{assessment.results_count || 0}</p>
                                                    </div>
                                                </div>
                                            </div>

                                            <div className="grid grid-cols-1 gap-2">
                                                <Link href={`/assessments/${assessment.id}/questions`} className="w-full">
                                                    <Button variant="outline" size="default" className="w-full">
                                                        <FileQuestion className="mr-1 h-4 w-4" />
                                                        {assessment.questions_count ? 'Manage Questions' : 'Add Questions'}
                                                    </Button>
                                                </Link>
                                                <Link href={`/assessments/${assessment.id}/results`} className="w-full">
                                                    <Button variant="outline" size="default" className="w-full">
                                                        <BarChart2 className="mr-1 h-4 w-4" />
                                                        View Results
                                                    </Button>
                                                </Link>
                                            </div>
                                        </CardContent>
                                    </Card>
                                ))}
                            </div>
                        )}

                        {filteredAssessments.length === 0 && searchTerm !== '' && (
                            <div className="rounded-lg border border-gray-200 p-6 text-center text-gray-500">
                                No assessments found matching "{searchTerm}"
                            </div>
                        )}

                        {/* Empty state when no assessments */}
                        {assessments.data.length === 0 && (
                            <div className="flex flex-col items-center justify-center rounded-lg border-2 border-dashed border-gray-300 p-12 text-center">
                                <BookOpen className="mx-auto h-12 w-12 text-gray-400" />
                                <h3 className="mt-2 text-sm font-medium text-gray-900">No assessments</h3>
                                <p className="mt-1 text-sm text-gray-500">Get started by adding a new assessment to this subject.</p>
                                <div className="mt-6">
                                    <Create />
                                </div>
                            </div>
                        )}

                        {/* Pagination */}
                        {assessments && assessments.last_page > 1 && searchTerm === '' && (
                            <div className="mt-6 flex items-center justify-between">
                                <div className="text-sm text-gray-700">
                                    Showing {assessments.from} to {assessments.to} of {assessments.total} results
                                </div>
                                <div className="flex space-x-2">
                                    {Array.from({ length: assessments.last_page }, (_, i) => i + 1).map((page) => (
                                        <a
                                            key={page}
                                            href={`/assessments?class_id=${class_id}&subject_id=${subject.id}&page=${page}`}
                                            className={`rounded px-3 py-1 ${
                                                page === assessments.current_page
                                                    ? 'bg-blue-950 text-white'
                                                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                                            }`}
                                        >
                                            {page}
                                        </a>
                                    ))}
                                </div>
                            </div>
                        )}
                    </div>
                </div>
                {editingAssessment && (
                    <EditAssessment
                        assessment={editingAssessment}
                        open={!!editingAssessment}
                        onOpenChange={(open: boolean) => !open && setEditingAssessment(null)}
                    />
                )}

                {/* Delete Dialog */}
                {deletingAssessment && (
                    <DeleteAssessment
                        assessment={deletingAssessment}
                        open={!!deletingAssessment}
                        onOpenChange={(open: boolean) => !open && setDeletingAssessment(null)}
                    />
                )}
            </TabLayout>
        </AppLayout>
    );
}

export default Index;
