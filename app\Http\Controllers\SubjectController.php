<?php

namespace App\Http\Controllers;

use App\Http\Resources\SubjectResource;
use App\Models\SchoolClass;
use App\Models\Subject;
use Illuminate\Http\Request;
use Inertia\Inertia;

class SubjectController extends BaseController
{
    /**
     * Display a listing of the subjects.
     */
    public function index(Request $request)
    {
        $this->class_id = 1;
        if ($request->has('class_id')) {
            $this->class_id = $request->class_id;
        }

        $classes = SchoolClass::all();
        $class = SchoolClass::find($this->class_id);
        $subjects = SubjectResource::collection(Subject::where('class_id', $this->class_id)->paginate(10));
        $this->data = [
            'subjects' => $subjects,
            'class_id' => $this->class_id,
            'className' => $class->name,
            'classes' => $classes,
        ];

        return Inertia::render('subjects/Index', $this->data);
    }

    /**
     * Store a newly created subject in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'class_id' => 'required|exists:school_classes,id',
        ]);

        $subject = Subject::create([
            'name' => $request->name,
            'class_id' => $request->class_id,
        ]);

        return redirect()->back()->with('success', 'Subject created successfully');
    }

    /**
     * Update the specified subject in storage.
     */
    public function update(Request $request, $id)
    {
        $subject = Subject::findOrFail($id);

        $request->validate([
            'name' => 'required|string|max:255',
        ]);

        $subject->update([
            'name' => $request->name,
        ]);

        return redirect()->back()->with('success', 'Subject updated successfully');
    }

    /**
     * Remove the specified subject from storage.
     */
    public function destroy($id)
    {
        $subject = Subject::findOrFail($id);
        $subject->delete();

        return redirect()->back()->with('success', 'Subject deleted successfully');
    }
}