# Questions Component Fix for Image Upload Issues

This README explains the fixes made to address image upload issues in the Questions component.

## The Problem

The original `Questions.tsx` component had issues with handling image uploads properly in the form submission process. Specifically:

1. The `instanceof File` check for image files wasn't working as expected
2. The form wasn't being submitted with the correct `multipart/form-data` encoding
3. There was an unused variable warning for the `post` variable

## The Solution

We've created a fixed version of the component in `QuestionsFixed.tsx` with the following improvements:

1. **Proper Form Data Handling**: The `onSubmit` function now correctly appends data to the FormData object, explicitly handling both text and image question types
2. **Fixed Image Option Processing**: We properly handle both new file uploads and existing image URLs
3. **Removed Unused Variable Warning**: We no longer destructure the unused `post` variable from `useForm`
4. **Better Error Handling**: Improved error messages and logging for debugging
5. **Cleaner Implementation**: Simplified code with better organization and comments

## How to Implement the Fix

You have two options to implement the fix:

### Option 1: Replace the entire file (recommended)

1. Rename your existing file as a backup:

```bash
mv resources/js/pages/assessments/Questions.tsx resources/js/pages/assessments/Questions.tsx.bak
```

2. Copy the fixed version to the correct location:

```bash
cp resources/js/pages/assessments/QuestionsFixed.tsx resources/js/pages/assessments/Questions.tsx
```

### Option 2: Update just the onSubmit function

If you prefer to only update the problematic function, replace your existing `onSubmit` function in `Questions.tsx` with this fixed version:

```typescript
const onSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Validate image options if using image type
    if (!validateImageOptions()) {
        return;
    }

    try {
        // Create a FormData object for multipart/form-data submission
        const formData = new FormData();

        // Add basic question data
        formData.append('question', data.question);
        formData.append('question_type', data.question_type);
        formData.append('correct_answer', data.correct_answer);

        // Handle options based on question type
        if (data.question_type === 'text') {
            // For text questions, simply append the text options
            formData.append('option_a', data.option_a || '');
            formData.append('option_b', data.option_b || '');
            formData.append('option_c', data.option_c || '');
            formData.append('option_d', data.option_d || '');
        } else {
            // For image questions, handle file uploads or existing URLs
            ['a', 'b', 'c', 'd'].forEach((opt) => {
                const optionKey = `option_${opt}`;
                const file = imageFiles[optionKey];

                if (file) {
                    // If we have a new file, append it to the form data
                    formData.append(optionKey, file);
                } else if (imagePreviews[optionKey]) {
                    // If we're keeping an existing image URL (from the database)
                    // The server expects a string value when not uploading a new file
                    formData.append(optionKey, imagePreviews[optionKey]);
                }
            });
        }

        // Add method override for PATCH requests when updating
        if (currentQuestion) {
            formData.append('_method', 'PATCH');
        }

        // Determine the submission URL based on whether we're creating or updating
        const url = currentQuestion ? `/assessments/${assessment.id}/questions/${currentQuestion.id}` : `/assessments/${assessment.id}/questions`;

        // Use Inertia router with forceFormData option to ensure proper handling
        router.post(url, formData, {
            forceFormData: true,
            onSuccess: () => {
                if (currentQuestion) {
                    setIsEditingQuestion(false);
                } else {
                    setIsAddingQuestion(false);
                }
                resetForm();
                setCurrentQuestion(null);
            },
            onError: (errors) => {
                console.error('Submission errors:', errors);
                alert('There was an error saving your question. Please check the form and try again.');
            },
        });
    } catch (error) {
        console.error('Error preparing form submission:', error);
        alert('There was an unexpected error. Please try again.');
    }
};
```

Also, be sure to update your `useForm` hook to avoid the unused variable warning:

```typescript
// We're not destructuring 'post' here to avoid the unused variable warning
const { data, setData, processing, errors, reset } = useForm<QuestionForm>({
    question: '',
    question_type: 'text',
    option_a: '',
    option_b: '',
    option_c: '',
    option_d: '',
    correct_answer: 'A',
});
```

## Explanation of the Fix

The primary issue was how form data was being handled for image uploads. The fixed version:

1. Creates a FormData object for proper multipart/form-data encoding
2. Correctly appends text fields for regular text questions
3. For image questions, it:
    - Appends actual File objects for new uploads
    - Appends the image URL strings for existing images that haven't changed
4. Uses the router.post method with forceFormData to ensure proper handling
5. Provides better error handling and user feedback

This approach ensures that both text and image questions are handled correctly, and that existing images are preserved when editing a question unless explicitly changed.
