<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Update all existing assessment results to have attempt_number = 1
        // This ensures backward compatibility
        DB::table('assessment_results')
            ->whereNull('attempt_number')
            ->orWhere('attempt_number', 0)
            ->update(['attempt_number' => 1]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // No need to reverse this as it's just setting default values
        // The previous migration handles the column removal
    }
};
