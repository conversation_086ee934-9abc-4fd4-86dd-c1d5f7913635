import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Lesson } from '@/types';
import { router } from '@inertiajs/react';
import { useState } from 'react';

interface DeleteLessonProps {
    lesson: Lesson;
    open: boolean;
    onOpenChange: (open: boolean) => void;
}

export default function DeleteLesson({ lesson, open, onOpenChange }: DeleteLessonProps) {
    const [processing, setProcessing] = useState(false);

    const handleDelete = () => {
        setProcessing(true);

        router.delete(route('lessons.destroy', { lesson: lesson.id }), {
            onSuccess: () => {
                onOpenChange(false);
                setProcessing(false);
            },
            onError: () => {
                setProcessing(false);
            },
        });
    };

    return (
        <Dialog open={open} onOpenChange={onOpenChange}>
            <DialogContent className="sm:max-w-md">
                <DialogHeader>
                    <DialogTitle>Delete Lesson</DialogTitle>
                    <DialogDescription>Are you sure you want to delete this lesson? This action cannot be undone.</DialogDescription>
                </DialogHeader>

                <div className="mt-4 rounded-lg bg-red-50 p-4 text-sm text-red-500">
                    <p className="font-semibold">Warning:</p>
                    <p>Deleting this lesson will permanently remove all associated lesson content.</p>
                </div>

                <DialogFooter className="mt-6">
                    <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
                        Cancel
                    </Button>
                    <Button type="button" variant="destructive" onClick={handleDelete} disabled={processing}>
                        {processing ? 'Deleting...' : 'Delete Lesson'}
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    );
}
