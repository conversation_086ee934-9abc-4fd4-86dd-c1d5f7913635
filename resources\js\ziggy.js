const Ziggy = {"url":"http:\/\/localhost","port":null,"defaults":{},"routes":{"home":{"uri":"\/","methods":["GET","HEAD"]},"dashboard":{"uri":"dashboard","methods":["GET","HEAD"]},"users.index":{"uri":"users","methods":["GET","HEAD"]},"users.create":{"uri":"users\/create","methods":["GET","HEAD"]},"users.store":{"uri":"users","methods":["POST"]},"users.show":{"uri":"users\/{user}","methods":["GET","HEAD"],"parameters":["user"]},"users.edit":{"uri":"users\/{user}\/edit","methods":["GET","HEAD"],"parameters":["user"]},"users.update":{"uri":"users\/{user}","methods":["PUT","PATCH"],"parameters":["user"]},"users.destroy":{"uri":"users\/{user}","methods":["DELETE"],"parameters":["user"]},"teachers.index":{"uri":"teachers","methods":["GET","HEAD"]},"teachers.create":{"uri":"teachers\/create","methods":["GET","HEAD"]},"teachers.store":{"uri":"teachers","methods":["POST"]},"teachers.show":{"uri":"teachers\/{teacher}","methods":["GET","HEAD"],"parameters":["teacher"]},"teachers.edit":{"uri":"teachers\/{teacher}\/edit","methods":["GET","HEAD"],"parameters":["teacher"]},"teachers.update":{"uri":"teachers\/{teacher}","methods":["PUT","PATCH"],"parameters":["teacher"]},"teachers.destroy":{"uri":"teachers\/{teacher}","methods":["DELETE"],"parameters":["teacher"]},"students.index":{"uri":"students","methods":["GET","HEAD"]},"students.store":{"uri":"students","methods":["POST"]},"students.update":{"uri":"students\/{id}","methods":["PUT"],"parameters":["id"]},"students.destroy":{"uri":"students\/{id}","methods":["DELETE"],"parameters":["id"]},"subjects.index":{"uri":"subjects","methods":["GET","HEAD"]},"subjects.store":{"uri":"subjects","methods":["POST"]},"subjects.update":{"uri":"subjects\/{id}","methods":["PUT"],"parameters":["id"]},"subjects.destroy":{"uri":"subjects\/{id}","methods":["DELETE"],"parameters":["id"]},"lesson-content.units":{"uri":"lesson-content\/units","methods":["GET","HEAD"]},"lesson-content.topics":{"uri":"lesson-content\/topics","methods":["GET","HEAD"]},"lesson-content.lessons":{"uri":"lesson-content\/lessons","methods":["GET","HEAD"]},"units.index":{"uri":"subjects\/{subject}\/units","methods":["GET","HEAD"],"parameters":["subject"]},"units.dashboard":{"uri":"units\/{unit}\/dashboard","methods":["GET","HEAD"],"parameters":["unit"]},"units.store":{"uri":"units","methods":["POST"]},"units.update":{"uri":"units\/{unit}","methods":["PUT"],"parameters":["unit"]},"units.destroy":{"uri":"units\/{unit}","methods":["DELETE"],"parameters":["unit"]},"units.reorder":{"uri":"units\/reorder","methods":["POST"]},"topics.convert":{"uri":"topics\/{topic}\/convert-to-lesson","methods":["POST"],"parameters":["topic"]},"topics.index":{"uri":"units\/{unit}\/topics","methods":["GET","HEAD"],"parameters":["unit"]},"topics.store":{"uri":"topics","methods":["POST"]},"topics.update":{"uri":"topics\/{topic}","methods":["PUT"],"parameters":["topic"]},"topics.destroy":{"uri":"topics\/{topic}","methods":["DELETE"],"parameters":["topic"]},"topics.reorder":{"uri":"topics\/reorder","methods":["POST"]},"topics.lessons.index":{"uri":"topics\/{topic}\/lessons","methods":["GET","HEAD"],"parameters":["topic"]},"units.lessons.index":{"uri":"units\/{unit}\/lessons","methods":["GET","HEAD"],"parameters":["unit"]},"units.all-lessons":{"uri":"units\/{unit}\/all-lessons","methods":["GET","HEAD"],"parameters":["unit"]},"topics.lessons.create":{"uri":"topics\/{topic}\/lessons\/create","methods":["GET","HEAD"],"parameters":["topic"]},"units.lessons.create":{"uri":"units\/{unit}\/lessons\/create","methods":["GET","HEAD"],"parameters":["unit"]},"lessons.store":{"uri":"lessons","methods":["POST"]},"lessons.show":{"uri":"lessons\/{lesson}","methods":["GET","HEAD"],"parameters":["lesson"]},"lessons.edit":{"uri":"lessons\/{lesson}\/edit","methods":["GET","HEAD"],"parameters":["lesson"]},"lessons.update":{"uri":"lessons\/{lesson}","methods":["PUT"],"parameters":["lesson"]},"lessons.destroy":{"uri":"lessons\/{lesson}","methods":["DELETE"],"parameters":["lesson"]},"lessons.reorder":{"uri":"lessons\/reorder","methods":["POST"]},"lessons.move":{"uri":"lessons\/{lesson}\/move","methods":["POST"],"parameters":["lesson"]},"content-blocks.store":{"uri":"content-blocks","methods":["POST"]},"content-blocks.update":{"uri":"content-blocks\/{id}","methods":["PUT"],"parameters":["id"]},"content-blocks.destroy":{"uri":"content-blocks\/{id}","methods":["DELETE"],"parameters":["id"]},"content-blocks.reorder":{"uri":"content-blocks\/reorder","methods":["POST"]},"profile.edit":{"uri":"settings\/profile","methods":["GET","HEAD"]},"profile.update":{"uri":"settings\/profile","methods":["PATCH"]},"profile.destroy":{"uri":"settings\/profile","methods":["DELETE"]},"password.edit":{"uri":"settings\/password","methods":["GET","HEAD"]},"password.update":{"uri":"settings\/password","methods":["PUT"]},"appearance":{"uri":"settings\/appearance","methods":["GET","HEAD"]},"register":{"uri":"register","methods":["GET","HEAD"]},"login":{"uri":"login","methods":["GET","HEAD"]},"password.request":{"uri":"forgot-password","methods":["GET","HEAD"]},"password.email":{"uri":"forgot-password","methods":["POST"]},"password.reset":{"uri":"reset-password\/{token}","methods":["GET","HEAD"],"parameters":["token"]},"password.store":{"uri":"reset-password","methods":["POST"]},"verification.notice":{"uri":"verify-email","methods":["GET","HEAD"]},"verification.verify":{"uri":"verify-email\/{id}\/{hash}","methods":["GET","HEAD"],"parameters":["id","hash"]},"verification.send":{"uri":"email\/verification-notification","methods":["POST"]},"password.confirm":{"uri":"confirm-password","methods":["GET","HEAD"]},"logout":{"uri":"logout","methods":["POST"]},"storage.local":{"uri":"storage\/{path}","methods":["GET","HEAD"],"wheres":{"path":".*"},"parameters":["path"]}}};
if (typeof window !== 'undefined' && typeof window.Ziggy !== 'undefined') {
  Object.assign(Ziggy.routes, window.Ziggy.routes);
}
export { Ziggy };
