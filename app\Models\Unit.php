<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Unit extends Model
{
    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'description',
        'subject_id',
    ];

    /**
     * Get the subject that owns the unit.
     */
    public function subject(): BelongsTo
    {
        return $this->belongsTo(Subject::class);
    }

    /**
     * Get the topics for the unit.
     */
    public function topics(): HasMany
    {
        return $this->hasMany(Topic::class);
    }

    /**
     * Get all lessons associated with this unit, either directly or through topics.
     * This provides a flattened view of all lessons.
     */
    public function lessons()
    {
        return Lesson::where(function ($query) {
            $query->where('unit_id', $this->id)
                ->orWhereIn('topic_id', $this->topics->pluck('id'));
        });
    }

    /**
     * Get the directly associated lessons (without a topic).
     */
    public function directLessons(): Has<PERSON><PERSON>
    {
        return $this->hasMany(Lesson::class);
    }
}