<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('assessment_results', function (Blueprint $table) {
            // Add attempt_number field
            $table->integer('attempt_number')->default(1)->after('student_id');
            
            // Add time_taken field to track how long each attempt took
            $table->integer('time_taken')->nullable()->after('progress')->comment('Time taken in seconds');
            
            // Add index for better performance when querying attempts
            $table->index(['assessment_id', 'student_id', 'attempt_number']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('assessment_results', function (Blueprint $table) {
            $table->dropIndex(['assessment_id', 'student_id', 'attempt_number']);
            $table->dropColumn(['attempt_number', 'time_taken']);
        });
    }
};
