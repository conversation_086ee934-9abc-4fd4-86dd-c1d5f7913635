import { useForm } from '@inertiajs/react';

import { <PERSON><PERSON>, DialogContent, DialogDescription, Di<PERSON>Footer, DialogHeader, DialogTitle } from '@/components/ui/dialog';

interface Subject {
    id: number;
    name: string;
}

interface Props {
    subject: Subject;
    open: boolean;
    onOpenChange: (open: boolean) => void;
}

function DeleteSubject({ subject, open, onOpenChange }: Props) {
    const { delete: destroy, processing } = useForm();

    const handleDelete = () => {
        destroy(`/subjects/${subject.id}`, {
            preserveScroll: true,
            onSuccess: () => {
                onOpenChange(false);
            },
        });
    };

    return (
        <Dialog open={open} onOpenChange={onOpenChange}>
            <DialogContent className="sm:max-w-md">
                <DialogHeader>
                    <DialogTitle>Delete Subject</DialogTitle>
                    <DialogDescription>Are you sure you want to delete the subject "{subject.name}"? This action cannot be undone.</DialogDescription>
                </DialogHeader>
                <DialogFooter>
                    <button
                        type="button"
                        onClick={() => onOpenChange(false)}
                        className="rounded-md px-4 py-2 text-sm font-medium text-gray-600 hover:bg-gray-100"
                        disabled={processing}
                    >
                        Cancel
                    </button>
                    <button
                        type="button"
                        onClick={handleDelete}
                        className="rounded-md bg-red-600 px-4 py-2 text-sm font-medium text-white hover:bg-red-700 disabled:opacity-50"
                        disabled={processing}
                    >
                        {processing ? 'Deleting...' : 'Delete Subject'}
                    </button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    );
}

export default DeleteSubject;
