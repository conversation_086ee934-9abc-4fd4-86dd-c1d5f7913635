<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ContentBlock extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'topic_id',
        'type',
        'content',
        'media_path',
        'audio_path',
        'order',
        'parent_id',
        'attributes',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'attributes' => 'array',
    ];

    /**
     * Get the topic that owns the content block.
     */
    public function topic()
    {
        return $this->belongsTo(Topic::class);
    }

    /**
     * Get the parent content block.
     */
    public function parent()
    {
        return $this->belongsTo(ContentBlock::class, 'parent_id');
    }

    /**
     * Get the child content blocks.
     */
    public function children()
    {
        return $this->hasMany(ContentBlock::class, 'parent_id')->orderBy('order');
    }
}
