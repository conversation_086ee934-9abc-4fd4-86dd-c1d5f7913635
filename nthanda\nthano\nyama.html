<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title><PERSON><PERSON><PERSON> ndi Mkango - Nthano</title>
        <link rel="stylesheet" href="../styles.css">

        <style>
            :root {
                --color-blue: #1c407c;
                --color-yellow: #ffd93d;
                --color-dark-yellow: #e6c235;
                --color-white: #ffffff;
                --color-red: #ff5252;
                --color-green: #4caf50;
                --color-dark-green: #388e3c;
                --color-orange: #FF9800;
                --color-purple: #9C27B0;
                --color-gray: #F5F5F5;
                --color-text-gray: #666;
            }
            
            body {
                background-color: var(--color-gray);
                margin: 0;
                padding: 0;
                font-family: "Fredoka", sans-serif;
                color: var(--color-blue);
            }
            
            .container {
                max-width: 800px;
                margin: 0 auto;
                padding: 0;
                position: relative;
            }
            
            .background-pattern {
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                opacity: 0.05;
                transform: rotate(45deg);
                z-index: -1;
            }
            
            .pattern-row {
                display: flex;
                justify-content: space-around;
                margin: 15px 0;
            }
            
            .pattern-shape {
                width: 30px;
                height: 30px;
                border-radius: 6px;
                margin: 8px;
            }
            
            .header {
                padding: 16px;
                background-color: var(--color-blue);
                border-bottom-left-radius: 24px;
                border-bottom-right-radius: 24px;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
                margin-bottom: 12px;
            }
            
            .header-top {
                display: flex;
                align-items: center;
                margin-bottom: 8px;
            }
            
            .back-button {
                margin-right: 16px;
                background-color: var(--color-yellow) ;
                width: 40px;
                height: 40px;
                border-radius: 20px;
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
            }
            
            .back-button img {
                width: 15px;
                height: 15px;
            }
            
            .title {
                color: var(--color-white);
                font-weight: 700;
                flex: 1;
                font-size: 20px;
                margin: 0;
            }
            
            .subtitle {
                color: var(--color-yellow);
                font-weight: 400;
                font-size: 14px;
                text-align: left;
                margin-left: 40px;
                
            }
            
            .video-container {
                margin-top: 20px;
                align-items: center;
                background-color: white;
                border-radius: 20px;
                margin: 16px;
                padding: 16px;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
            }
            
            .video {
                width: 100%;
                border-radius: 16px;
                background-color: #F0F0F0;
                aspect-ratio: 16/9;
            }
            
            .controls {
                display: flex;
                align-items: center;
                justify-content: center;
                margin-top: 16px;
                background-color: white;
                border-radius: 30px;
                padding: 8px;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
            }
            
            .control-button {
                margin: 8px;
                background-color: transparent;
                border: none;
                cursor: pointer;
            }
            
            .control-button img {
                width: 32px;
                height: 32px;
            }
            
            .play-button {
                background-color: var(--color-yellow);
                border-radius: 20px;
                width: 64px;
                height: 64px;
                display: flex;
                align-items: center;
                justify-content: center;
                margin: 0 16px;
            }
            
            .play-button img {
                width: 30px;
                height: 30px;
            }
            
            .loading-container {
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                background-color: white;
                z-index: 1;
                border-radius: 16px;
            }
            
            .loading-icon {
                margin-bottom: 12px;
                animation: pulse 1.5s infinite alternate;
            }
            
            @keyframes pulse {
                0% {
                    transform: scale(1);
                    opacity: 0.7;
                }
                100% {
                    transform: scale(1.2);
                    opacity: 1;
                }
            }
            
            .loading-text {
                margin-top: 12px;
                color: var(--color-blue);
                font-weight: 500;
                font-size: 16px;
            }
            
            .fun-fact-container {
                display: flex;
                align-items: center;
                background-color: white;
                margin: 16px;
                margin-top: 8px;
                padding: 16px;
                border-radius: 16px;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            }
            
            .fun-fact-icon {
                margin-right: 12px;
                color: var(--color-yellow);
                font-size: 24px;
            }
            
            .fun-fact-text {
                flex: 1;
                margin-left: 12px;
                color: var(--color-blue);
                font-weight: 500;
                font-size: 16px;
            }
            
            .progress-container {
                margin: 16px;
                margin-top: 8px;
                padding: 16px;
                border-radius: 16px;
                background-color: white;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            }
            
            .progress-text {
                text-align: center;
                font-weight: 500;
                font-size: 14px;
                color: var(--color-blue);
                margin-bottom: 8px;
            }
            
            .progress-bar {
                height: 8px;
                background-color: #F0F0F0;
                border-radius: 4px;
                overflow: hidden;
                position: relative;
            }
            
            .progress-fill {
                height: 100%;
                border-radius: 4px;
                width: 0%;
                transition: width 0.3s ease-in-out;
            }
            
            .error-container {
                flex: 1;
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                padding: 20px;
                height: 60vh;
            }
            
            .error-icon {
                font-size: 64px;
                color: var(--color-orange);
                margin-bottom: 16px;
            }
            
            .error-text {
                font-weight: 500;
                font-size: 18px;
                color: var(--color-blue);
                text-align: center;
                margin-top: 16px;
                margin-bottom: 24px;
            }
            
            .back-home-button {
                background-color: var(--color-orange);
                color: white;
                border: none;
                border-radius: 20px;
                padding: 12px 24px;
                font-weight: 600;
                font-size: 16px;
                cursor: pointer;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
            }
        </style>
    </head>
    <body>
        <div class="container">
            <!-- Background Pattern -->
            <div class="background-pattern">
                <div class="pattern-row">
                    <div class="pattern-shape"
                        style="background-color: var(--color-orange);"></div>
                    <div class="pattern-shape"
                        style="background-color: var(--color-green);"></div>
                    <div class="pattern-shape"
                        style="background-color: var(--color-blue);"></div>
                    <div class="pattern-shape"
                        style="background-color: var(--color-purple);"></div>
                </div>
                <div class="pattern-row">
                    <div class="pattern-shape"
                        style="background-color: var(--color-green);"></div>
                    <div class="pattern-shape"
                        style="background-color: var(--color-blue);"></div>
                    <div class="pattern-shape"
                        style="background-color: var(--color-purple);"></div>
                    <div class="pattern-shape"
                        style="background-color: var(--color-orange);"></div>
                </div>
                <div class="pattern-row">
                    <div class="pattern-shape"
                        style="background-color: var(--color-blue);"></div>
                    <div class="pattern-shape"
                        style="background-color: var(--color-purple);"></div>
                    <div class="pattern-shape"
                        style="background-color: var(--color-orange);"></div>
                    <div class="pattern-shape"
                        style="background-color: var(--color-green);"></div>
                </div>
                <div class="pattern-row">
                    <div class="pattern-shape"
                        style="background-color: var(--color-purple);"></div>
                    <div class="pattern-shape"
                        style="background-color: var(--color-orange);"></div>
                    <div class="pattern-shape"
                        style="background-color: var(--color-green);"></div>
                    <div class="pattern-shape"
                        style="background-color: var(--color-blue);"></div>
                </div>
                <div class="pattern-row">
                    <div class="pattern-shape"
                        style="background-color: var(--color-orange);"></div>
                    <div class="pattern-shape"
                        style="background-color: var(--color-green);"></div>
                    <div class="pattern-shape"
                        style="background-color: var(--color-blue);"></div>
                    <div class="pattern-shape"
                        style="background-color: var(--color-purple);"></div>
                </div>
                <div class="pattern-row">
                    <div class="pattern-shape"
                        style="background-color: var(--color-green);"></div>
                    <div class="pattern-shape"
                        style="background-color: var(--color-blue);"></div>
                    <div class="pattern-shape"
                        style="background-color: var(--color-purple);"></div>
                    <div class="pattern-shape"
                        style="background-color: var(--color-orange);"></div>
                </div>
                <div class="pattern-row">
                    <div class="pattern-shape"
                        style="background-color: var(--color-blue);"></div>
                    <div class="pattern-shape"
                        style="background-color: var(--color-purple);"></div>
                    <div class="pattern-shape"
                        style="background-color: var(--color-orange);"></div>
                    <div class="pattern-shape"
                        style="background-color: var(--color-green);"></div>
                </div>
                <div class="pattern-row">
                    <div class="pattern-shape"
                        style="background-color: var(--color-purple);"></div>
                    <div class="pattern-shape"
                        style="background-color: var(--color-orange);"></div>
                    <div class="pattern-shape"
                        style="background-color: var(--color-green);"></div>
                    <div class="pattern-shape"
                        style="background-color: var(--color-blue);"></div>
                </div>
            </div>

            <div class="header">
                <div class="header-top">
                    <div class="back-button" onclick="goBack()">
                        <img src="../assets/icons/arrowleft.png" alt="Back">
                    </div>
                    <h1 class="title">Nyama za m`Nkhalango</h1>
                </div>
                <p class="subtitle">Nthano ya nyama za
                    m`nkhalango!🐰</p>
            </div>

            <div class="video-container" id="videoContainer">
                <div class="loading-container" id="loadingContainer">
                    <img src="../assets/icons/star.png" alt="Loading"
                        class="loading-icon" width="48" height="48">
                    <p class="loading-text">Tikukonzekera nthano yanu... ⭐</p>
                </div>

                <video class="video" id="videoPlayer"
                    poster="../assets/nthano/video-poster.jpg">
                    <source src="../assets/videos/nyama.mp4" type="video/mp4">
                    Your browser does not support the video tag.
                </video>

                <div class="controls">
                    <button class="control-button" onclick="rewindVideo()">
                        <img src="../assets/icons/arrowleft.png" alt="Rewind">
                    </button>
                    <button class="play-button" onclick="togglePlayPause()"
                        id="playButton">
                        <img src="../assets/icons/play.png" alt="Play"
                            id="playIcon">
                    </button>
                    <button class="control-button" onclick="forwardVideo()">
                        <img src="../assets/icons/arrowright.png" alt="Forward">
                    </button>
                </div>
            </div>

            <div class="fun-fact-container">
                <div class="fun-fact-icon">💡</div>
                <p class="fun-fact-text">
                    Nthano zimatiphunzitsa zinthu zabwino! 🌟
                </p>
            </div>

            <div class="progress-container">
                <p class="progress-text" id="progressText">
                    Nthano yayima... ⏸️
                </p>
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"
                        style="background-color: var(--color-orange);"></div>
                </div>
            </div>
        </div>

        <script>
            // Variables to track video state
            let isPlaying = false;
            let videoLoaded = false;
            const videoPlayer = document.getElementById('videoPlayer');
            const playButton = document.getElementById('playButton');
            const playIcon = document.getElementById('playIcon');
            const progressFill = document.getElementById('progressFill');
            const progressText = document.getElementById('progressText');
            const loadingContainer = document.getElementById('loadingContainer');
            
            // Initialize video
            window.addEventListener('load', function() {
                // Simulate loading delay
                setTimeout(function() {
                    loadingContainer.style.display = 'none';
                    videoLoaded = true;
                }, 2000);
                
                // Update progress bar as video plays
                videoPlayer.addEventListener('timeupdate', updateProgress);
                
                // Reset when video ends
                videoPlayer.addEventListener('ended', function() {
                    isPlaying = false;
                    playIcon.src = '../assets/icons/play.png';
                    progressText.textContent = 'Nthano yatha! 🎉';
                });
            });
            
            function togglePlayPause() {
                if (!videoLoaded) return;
                
                if (isPlaying) {
                    videoPlayer.pause();
                    playIcon.src = '../assets/icons/play.png';
                    progressText.textContent = 'Nthano yayima... ⏸️';
                    progressFill.style.backgroundColor = 'var(--color-orange)';
                } else {
                    videoPlayer.play();
                    playIcon.src = '../assets/icons/pause.png';
                    progressText.textContent = 'Tikumvetsera nthano... 🎵';
                    progressFill.style.backgroundColor = 'var(--color-green)';
                }
                
                isPlaying = !isPlaying;
            }
            
            function updateProgress() {
                const percentage = (videoPlayer.currentTime / videoPlayer.duration) * 100;
                progressFill.style.width = percentage + '%';
            }
            
            function rewindVideo() {
                if (!videoLoaded) return;
                videoPlayer.currentTime = Math.max(0, videoPlayer.currentTime - 10);
            }
            
            function forwardVideo() {
                if (!videoLoaded) return;
                videoPlayer.currentTime = Math.min(videoPlayer.duration, videoPlayer.currentTime + 10);
            }
            
            function goBack() {
                window.history.back();
            }
        </script>
    </body>
</html>
