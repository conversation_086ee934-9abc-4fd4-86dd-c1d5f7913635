import { Button } from '@/components/ui/button';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { ContentBlock } from '@/types';
import axios from 'axios';
import { ChevronDown, ChevronUp, GripVertical, Pencil, Play, Trash } from 'lucide-react';
import { useEffect, useState } from 'react';
import { BLOCK_TYPES } from './BlockTypes';

interface ContentBlockEditorProps {
    block: ContentBlock;
    onUpdate: (updatedBlock: ContentBlock) => void;
    onDelete: (blockId: number) => void;
    onMoveUp: (blockId: number) => void;
    onMoveDown: (blockId: number) => void;
    showMoveUp: boolean;
    showMoveDown: boolean;
    isEditing: boolean;
}

export default function ContentBlockEditor({
    block,
    onUpdate,
    onDelete,
    onMoveUp,
    onMoveDown,
    showMoveUp,
    showMoveDown,
    isEditing,
}: ContentBlockEditorProps) {
    const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
    const [editedContent, setEditedContent] = useState(block.content || '');
    const [editedAttributes, setEditedAttributes] = useState(block.attributes || {});
    const [mediaPreview, setMediaPreview] = useState<string | null>(block.media_path ? `/storage/${block.media_path}` : null);
    const [audioPreview, setAudioPreview] = useState<string | null>(block.audio_path ? `/storage/${block.audio_path}` : null);
    const [selectedMediaFile, setSelectedMediaFile] = useState<File | null>(null);
    const [selectedAudioFile, setSelectedAudioFile] = useState<File | null>(null);
    const [isUpdating, setIsUpdating] = useState(false);
    const [removeMedia, setRemoveMedia] = useState(false);
    const [removeAudio, setRemoveAudio] = useState(false);

    const blockType = BLOCK_TYPES[block.type];

    const handleUpdateBlock = async () => {
        setIsUpdating(true);

        const formData = new FormData();
        formData.append('_method', 'PUT');
        formData.append('type', block.type);

        if (blockType.hasContent) {
            formData.append('content', editedContent);
        }

        if (Object.keys(editedAttributes).length > 0) {
            formData.append('attributes', JSON.stringify(editedAttributes));
        }

        if (selectedMediaFile) {
            formData.append('media', selectedMediaFile);
        } else if (removeMedia) {
            formData.append('remove_media', '1');
        }

        if (selectedAudioFile) {
            formData.append('audio', selectedAudioFile);
        } else if (removeAudio) {
            formData.append('remove_audio', '1');
        }

        try {
            const response = await axios.post(`/content-blocks/${block.id}`, formData, {
                headers: {
                    'Content-Type': 'multipart/form-data',
                },
            });

            onUpdate(response.data.content_block);
            setIsEditDialogOpen(false);
        } catch (error) {
            console.error('Error updating content block:', error);
        } finally {
            setIsUpdating(false);
        }
    };

    const handleMediaFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0] || null;
        setSelectedMediaFile(file);
        setRemoveMedia(false);

        if (file) {
            const url = URL.createObjectURL(file);
            setMediaPreview(url);
        }
    };

    const handleAudioFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0] || null;
        setSelectedAudioFile(file);
        setRemoveAudio(false);

        if (file) {
            const url = URL.createObjectURL(file);
            setAudioPreview(url);
        }
    };

    const handleRemoveMedia = () => {
        setSelectedMediaFile(null);
        setMediaPreview(null);
        setRemoveMedia(true);
    };

    const handleRemoveAudio = () => {
        setSelectedAudioFile(null);
        setAudioPreview(null);
        setRemoveAudio(true);
    };

    // Update local state when block prop changes
    useEffect(() => {
        setEditedContent(block.content || '');
        setEditedAttributes(block.attributes || {});
        setMediaPreview(block.media_path ? `/storage/${block.media_path}` : null);
        setAudioPreview(block.audio_path ? `/storage/${block.audio_path}` : null);
        setRemoveMedia(false);
        setRemoveAudio(false);
    }, [block]);

    const handleHeadingLevelChange = (value: string) => {
        setEditedAttributes({ ...editedAttributes, level: parseInt(value) });
    };

    const renderContent = () => {
        switch (block.type) {
            case 'heading':
                const level = block.attributes?.level || 1;
                const HeadingTag = `h${level}` as keyof JSX.IntrinsicElements;
                return <HeadingTag className="mb-2 font-bold">{block.content}</HeadingTag>;

            case 'paragraph':
                return <p className="mb-4 text-gray-800">{block.content}</p>;

            case 'image':
                return (
                    <div className="mb-4">
                        {block.media_path && (
                            <img src={`/storage/${block.media_path}`} alt={block.content || 'Image'} className="max-h-96 rounded-lg" />
                        )}
                        {block.content && <p className="mt-2 text-sm text-gray-600">{block.content}</p>}
                    </div>
                );

            case 'video':
                return (
                    <div className="mb-4">
                        {block.media_path && <video src={`/storage/${block.media_path}`} controls className="max-h-96 w-full rounded-lg" />}
                        {block.content && <p className="mt-2 text-sm text-gray-600">{block.content}</p>}
                    </div>
                );

            case 'audio':
                return (
                    <div className="mb-4">
                        {block.media_path && <audio src={`/storage/${block.media_path}`} controls className="w-full" />}
                        {block.content && <p className="mt-2 text-sm text-gray-600">{block.content}</p>}
                    </div>
                );

            case 'listItem':
                return <li className="mb-1 ml-5">{block.content}</li>;

            default:
                return null;
        }
    };

    return (
        <>
            <div
                className={`group relative rounded-lg border p-4 ${isEditing ? 'border-dashed border-gray-400 hover:border-gray-600' : 'border-transparent'}`}
            >
                {isEditing && (
                    <div className="absolute top-2 right-2 flex space-x-1 opacity-0 transition-opacity group-hover:opacity-100">
                        <Button size="icon" variant="ghost" className="h-8 w-8 rounded-full" onClick={() => setIsEditDialogOpen(true)}>
                            <Pencil className="h-4 w-4" />
                        </Button>
                        <Button size="icon" variant="ghost" className="h-8 w-8 rounded-full" onClick={() => onDelete(block.id)}>
                            <Trash className="h-4 w-4" />
                        </Button>
                        {showMoveUp && (
                            <Button size="icon" variant="ghost" className="h-8 w-8 rounded-full" onClick={() => onMoveUp(block.id)}>
                                <ChevronUp className="h-4 w-4" />
                            </Button>
                        )}
                        {showMoveDown && (
                            <Button size="icon" variant="ghost" className="h-8 w-8 rounded-full" onClick={() => onMoveDown(block.id)}>
                                <ChevronDown className="h-4 w-4" />
                            </Button>
                        )}
                        <Button size="icon" variant="ghost" className="h-8 w-8 cursor-grab rounded-full">
                            <GripVertical className="h-4 w-4" />
                        </Button>
                    </div>
                )}

                {block.type === 'list' ? (
                    <ul className="list-disc pl-5">
                        {block.children?.map((child) => (
                            <li key={child.id} className="mb-1">
                                {child.content}
                            </li>
                        ))}
                    </ul>
                ) : (
                    renderContent()
                )}

                {isEditing && block.audio_path && (
                    <div className="mt-2 flex items-center space-x-1 text-xs text-gray-500">
                        <Play className="h-3 w-3" />
                        <span>Audio narration available</span>
                    </div>
                )}
            </div>

            <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
                <DialogContent className="sm:max-w-lg">
                    <DialogHeader>
                        <DialogTitle>Edit {blockType.label}</DialogTitle>
                    </DialogHeader>
                    <div className="grid gap-4 py-4">
                        {blockType.hasContent && (
                            <div className="grid gap-2">
                                <Label htmlFor="content">Content</Label>
                                <Textarea id="content" value={editedContent} onChange={(e) => setEditedContent(e.target.value)} rows={4} />
                            </div>
                        )}

                        {block.type === 'heading' && (
                            <div className="grid gap-2">
                                <Label htmlFor="headingLevel">Heading Level</Label>
                                <Select value={String(editedAttributes.level || 1)} onValueChange={handleHeadingLevelChange}>
                                    <SelectTrigger id="headingLevel">
                                        <SelectValue placeholder="Select heading level" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="1">Heading 1</SelectItem>
                                        <SelectItem value="2">Heading 2</SelectItem>
                                        <SelectItem value="3">Heading 3</SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>
                        )}

                        {blockType.hasMedia && (
                            <div className="grid gap-2">
                                <Label>Media</Label>
                                {mediaPreview ? (
                                    <div className="rounded border p-2">
                                        <div className="flex justify-between">
                                            <span className="text-sm text-gray-600">Current media file</span>
                                            <Button size="sm" variant="ghost" className="h-8 w-8" onClick={handleRemoveMedia}>
                                                <Trash className="h-4 w-4 text-red-500" />
                                            </Button>
                                        </div>
                                        {block.type === 'image' && <img src={mediaPreview} alt="Preview" className="mt-2 max-h-48 rounded" />}
                                        {block.type === 'video' && <video src={mediaPreview} controls className="mt-2 max-h-48 w-full rounded" />}
                                        {block.type === 'audio' && <audio src={mediaPreview} controls className="mt-2 w-full" />}
                                    </div>
                                ) : (
                                    <div className="flex items-center justify-center rounded border border-dashed p-4">
                                        <Label htmlFor="media-upload" className="cursor-pointer text-center text-sm text-gray-600">
                                            <span>Upload media file</span>
                                            <Input
                                                id="media-upload"
                                                type="file"
                                                className="hidden"
                                                onChange={handleMediaFileChange}
                                                accept={block.type === 'image' ? 'image/*' : block.type === 'video' ? 'video/*' : 'audio/*'}
                                            />
                                        </Label>
                                    </div>
                                )}
                            </div>
                        )}

                        {blockType.hasAudio && (
                            <div className="grid gap-2">
                                <Label>Audio Narration</Label>
                                {audioPreview ? (
                                    <div className="rounded border p-2">
                                        <div className="flex justify-between">
                                            <span className="text-sm text-gray-600">Current audio narration</span>
                                            <Button size="sm" variant="ghost" className="h-8 w-8" onClick={handleRemoveAudio}>
                                                <Trash className="h-4 w-4 text-red-500" />
                                            </Button>
                                        </div>
                                        <audio src={audioPreview} controls className="mt-2 w-full" />
                                    </div>
                                ) : (
                                    <div className="flex items-center justify-center rounded border border-dashed p-4">
                                        <Label htmlFor="audio-upload" className="cursor-pointer text-center text-sm text-gray-600">
                                            <span>Upload audio narration</span>
                                            <Input
                                                id="audio-upload"
                                                type="file"
                                                className="hidden"
                                                onChange={handleAudioFileChange}
                                                accept="audio/*"
                                            />
                                        </Label>
                                    </div>
                                )}
                            </div>
                        )}
                    </div>
                    <div className="flex justify-end space-x-2">
                        <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
                            Cancel
                        </Button>
                        <Button onClick={handleUpdateBlock} disabled={isUpdating}>
                            {isUpdating ? 'Saving...' : 'Save Changes'}
                        </Button>
                    </div>
                </DialogContent>
            </Dialog>
        </>
    );
}
