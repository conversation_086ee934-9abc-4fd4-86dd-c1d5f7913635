#!/bin/bash

# Display banner
echo "=========================================="
echo "   Lesson System - Development Setup"
echo "=========================================="
echo

# Install UI dependencies
echo "Installing UI dependencies..."
bash install-ui-deps.sh

# Install additional development tools
echo
echo "Installing development tools..."
npm install --save-dev @typescript-eslint/parser @typescript-eslint/eslint-plugin eslint-plugin-jsx-a11y

# Configure environment
echo
echo "Creating .env file if not exists..."
if [ ! -f ".env" ]; then
  cp .env.example .env
  echo "Created .env file from .env.example"
else
  echo ".env file already exists"
fi

# Fix common file permissions
echo
echo "Setting file permissions..."
chmod -R 755 storage bootstrap/cache

# Clear any caches
echo
echo "Clearing Laravel caches..."
php artisan config:clear
php artisan cache:clear
php artisan route:clear
php artisan view:clear

# Run database migrations if needed
echo
echo "Would you like to run database migrations? (y/n)"
read -r answer
if [ "$answer" = "y" ] || [ "$answer" = "Y" ]; then
  php artisan migrate
  echo "Database migrations completed"
fi

# Success message
echo
echo "=========================================="
echo "Setup completed successfully!"
echo
echo "To start development:"
echo "1. npm run dev   (for frontend)"
echo "2. php artisan serve   (for backend)"
echo "==========================================" 