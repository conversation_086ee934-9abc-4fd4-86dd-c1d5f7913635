<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Subject;
use App\Models\Topic;
use App\Models\Unit;
use App\Models\User;
use App\Models\CompletedTopic;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\ValidationException;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class StudentAuthController extends Controller
{
    /**
     * Authenticate a student using PIN.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function login(Request $request)
    {
        $request->validate([
            'pin' => 'required|digits:4',
        ]);

        // Find user by PIN (only students have PINs)
        $student = User::where('pin', $request->pin)
            ->where('role', 'student')
            ->first();

        if (!$student) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid PIN. Please try again.'
            ], 201);
        }

        // Get student's subjects
        $subjects = Subject::where('class_id', $student->class_id)->get();

        // Get all units for the student's subjects
        $allUnits = Unit::whereIn('subject_id', $subjects->pluck('id'))
            ->orderBy('created_at', 'asc')
            ->get();

        // Get completed topics for the student
        $completedTopics = CompletedTopic::where('user_id', $student->id)
            ->with('topic')
            ->get();

        // Group completed topics by unit
        $completedTopicsByUnit = $completedTopics->groupBy('unit_id');

        // Process units with completion status and waiting period
        $units = $allUnits->map(function ($unit) use ($completedTopicsByUnit, $student) {
            $unitTopics = Topic::where('unit_id', $unit->id)->get();
            $completedUnitTopics = $completedTopicsByUnit->get($unit->id, collect())->pluck('topic_id');

            // Check if all topics in the unit are completed
            $allTopicsCompleted = $unitTopics->every(function ($topic) use ($completedUnitTopics) {
                return $completedUnitTopics->contains($topic->id);
            });

            // Get the latest completion time for this unit
            $latestCompletion = $completedTopicsByUnit->get($unit->id, collect())
                ->sortByDesc('created_at')
                ->first();

            $isComplete = false;
            $canStart = false;
            $waitingTime = null;

            if ($allTopicsCompleted && $latestCompletion) {
                $isComplete = true;
                $completionTime = Carbon::parse($latestCompletion->created_at);
                $waitingPeriod = Carbon::now()->subHours(48);

                if ($completionTime->isBefore($waitingPeriod)) {
                    $canStart = true;
                } else {
                    $waitingTime = $completionTime->addHours(48)->format('Y-m-d H:i:s');
                }
            } else {
                // Check if this is the first unit or if previous unit is complete
                $canStart = true; // Default to true, will be set to false if conditions aren't met

                // Find the previous unit in the same subject
                $previousUnit = null;

                // Get all units in the same subject ordered by creation date
                $subjectUnits = Unit::where('subject_id', $unit->subject_id)
                    ->orderBy('created_at', 'asc')
                    ->get();

                // Find the current unit's position in the ordered list
                $currentUnitIndex = $subjectUnits->search(function ($item) use ($unit) {
                    return $item->id === $unit->id;
                });

                // If this is not the first unit, check the previous unit
                if ($currentUnitIndex > 0) {
                    $previousUnit = $subjectUnits[$currentUnitIndex - 1];

                    // Check if previous unit is completed
                    $previousUnitTopics = Topic::where('unit_id', $previousUnit->id)->get();
                    $previousUnitCompletedTopics = $completedTopicsByUnit->get($previousUnit->id, collect())->pluck('topic_id');

                    $previousUnitCompleted = $previousUnitTopics->every(function ($topic) use ($previousUnitCompletedTopics) {
                        return $previousUnitCompletedTopics->contains($topic->id);
                    });

                    if ($previousUnitCompleted) {
                        // Check if 48 hours have passed since the previous unit's completion
                        $latestPreviousCompletion = $completedTopicsByUnit->get($previousUnit->id, collect())
                            ->sortByDesc('created_at')
                            ->first();

                        if ($latestPreviousCompletion) {
                            $completionTime = Carbon::parse($latestPreviousCompletion->created_at);
                            $waitingPeriod = Carbon::now()->subHours(48);

                            if ($completionTime->isBefore($waitingPeriod)) {
                                $canStart = true;
                            } else {
                                $canStart = false;
                                $waitingTime = $completionTime->addHours(48)->format('Y-m-d H:i:s');
                            }
                        }
                    } else {
                        $canStart = false;
                    }
                }
            }

            return [
                'id' => $unit->id,
                'name' => $unit->name,
                'description' => $unit->description,
                'subject_id' => $unit->subject_id,
                'created_at' => $unit->created_at,
                'is_complete' => $isComplete,
                'can_start' => $canStart,
                'waiting_time' => $waitingTime,
                'topics' => $unitTopics->map(function ($topic) use ($completedUnitTopics) {
                    return [
                        'id' => $topic->id,
                        'name' => $topic->name,
                        'description' => $topic->description,
                        'is_completed' => $completedUnitTopics->contains($topic->id)
                    ];
                })
            ];
        })->values();

        // Get topics for all units
        $topics = Topic::whereIn('unit_id', $allUnits->pluck('id'))->get();

        // Get student details
        $studentData = [
            'id' => $student->id,
            'name' => $student->name,
            'grade' => $student->class ? $student->class->name : 'Unknown',
            'avatar' => $student->avatar ?? 'assets/avatars/student1.png',
        ];

        return response()->json([
            'success' => true,
            'message' => 'Login successful',
            'student' => $studentData,
            'token' => null,
            'subjects' => $subjects,
            'units' => $units,
            'topics' => $topics,
        ]);
    }

    /**
     * Authenticate a student using QR code token.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function loginWithQR(Request $request)
    {
        $request->validate([
            'qr_token' => 'required|string',
        ]);

        // Find user by QR code token (only students have QR tokens)
        $student = User::where('qr_code_token', $request->qr_token)
            ->where('role', 'student')
            ->first();

        if (!$student) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid QR code. Please try again.'
            ], 201);
        }

        // Get student's subjects
        $subjects = Subject::where('class_id', $student->class_id)->get();

        // Get all units for the student's subjects
        $allUnits = Unit::whereIn('subject_id', $subjects->pluck('id'))
            ->orderBy('created_at', 'asc')
            ->get();

        // Get completed topics for the student
        $completedTopics = CompletedTopic::where('user_id', $student->id)
            ->with('topic')
            ->get();

        // Group completed topics by unit
        $completedTopicsByUnit = $completedTopics->groupBy('unit_id');

        // Process units to determine their status
        $units = $allUnits->map(function ($unit) use ($completedTopicsByUnit, $allUnits) {
            $unitCompletedTopics = $completedTopicsByUnit->get($unit->id, collect());
            $totalTopics = $unit->topics()->count();
            $completedCount = $unitCompletedTopics->count();

            // Determine unit status
            $status = 'locked';
            if ($completedCount > 0) {
                $status = $completedCount >= $totalTopics ? 'completed' : 'in_progress';
            } elseif ($unit->id === $allUnits->first()->id) {
                // First unit is always unlocked
                $status = 'unlocked';
            } else {
                // Check if previous unit is completed
                $previousUnit = $allUnits->where('id', '<', $unit->id)->last();
                if ($previousUnit) {
                    $prevCompletedTopics = $completedTopicsByUnit->get($previousUnit->id, collect());
                    $prevTotalTopics = $previousUnit->topics()->count();
                    if ($prevCompletedTopics->count() >= $prevTotalTopics && $prevTotalTopics > 0) {
                        $status = 'unlocked';
                    }
                }
            }

            return [
                'id' => $unit->id,
                'name' => $unit->name,
                'description' => $unit->description,
                'subject_id' => $unit->subject_id,
                'order' => $unit->order,
                'status' => $status,
                'completed_topics' => $completedCount,
                'total_topics' => $totalTopics,
                'progress' => $totalTopics > 0 ? round(($completedCount / $totalTopics) * 100) : 0,
                'created_at' => $unit->created_at,
                'updated_at' => $unit->updated_at,
            ];
        });

        // Get topics for all units
        $topics = Topic::whereIn('unit_id', $allUnits->pluck('id'))->get();

        // Get student details
        $studentData = [
            'id' => $student->id,
            'name' => $student->name,
            'grade' => $student->class ? $student->class->name : 'Unknown',
            'avatar' => $student->avatar ?? 'assets/avatars/student1.png',
        ];

        return response()->json([
            'success' => true,
            'message' => 'QR Login successful',
            'student' => $studentData,
            'token' => null,
            'subjects' => $subjects,
            'units' => $units,
            'topics' => $topics,
        ]);
    }

    /**
     * Log the student out (revoke token).
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function logout(Request $request)
    {
        // Revoke the token that was used to authenticate the current request
        $request->user()->currentAccessToken()->delete();

        return response()->json([
            'success' => true,
            'message' => 'Successfully logged out',
        ]);
    }

    /**
     * Get the authenticated student's information.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function me(Request $request)
    {
        $user = $request->user();

        if ($user->role !== 'student') {
            return response()->json([
                'success' => false,
                'message' => 'Not a student account'
            ], 403);
        }

        // Get student details
        $studentData = [
            'id' => $user->id,
            'name' => $user->name,
            'grade' => $user->class ? $user->class->name : 'Unknown',
            'avatar' => $user->avatar ?? 'assets/avatars/student1.png',
            'progress' => $user->progress ?? 0,
        ];

        return response()->json([
            'success' => true,
            'data' => $studentData,
        ]);
    }
}
