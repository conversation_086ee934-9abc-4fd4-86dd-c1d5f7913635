# Lesson Content Management System

This project implements a comprehensive lesson content management system with a hierarchical structure:

```
Subject → Units → Topics → Lessons → Content Blocks
```

## Features

- **Hierarchical Content Organization**: Logical arrangement of educational content
- **Content Block System**: Flexible blocks for various content types (headings, paragraphs, images, videos, audio, lists)
- **Drag-and-drop Reordering**: Easy reordering of units, topics, lessons, and content blocks
- **Media Management**: Support for image, video, and audio content
- **Audio Narration**: Add audio narration to any content block
- **Publishing Controls**: Publish/unpublish content as needed

## Backend Components

- **Models**: `Subject`, `Unit`, `Topic`, `Lesson`, `ContentBlock`
- **Controllers**: RESTful controllers for each entity with CRUD operations
- **Migrations**: Database structure with proper relationships

## Frontend Components

### Core Components

- `BlockTypes.tsx`: Defines the available content block types and their properties
- `BlockSelector.tsx`: UI for adding new content blocks to a lesson
- `ContentBlockEditor.tsx`: Editor for modifying content blocks
- `ContentBlockViewer.tsx`: Display component for content blocks

### Page Components

- `units/Index.tsx`: List and manage units for a subject
- `topics/Index.tsx`: List and manage topics for a unit
- `lessons/Index.tsx`: List and manage lessons for a topic
- `lessons/Create.tsx`: Create new lessons
- `lessons/Show.tsx`: View a lesson and its content
- `lessons/Edit.tsx`: Edit a lesson's details and content blocks

## Installation

1. Make sure all backend dependencies are installed:

    ```
    composer install
    ```

2. Install frontend dependencies:

    ```
    npm install
    ```

3. Install UI component libraries:

    ```
    bash install-ui-deps.sh
    ```

4. Run migrations:

    ```
    php artisan migrate
    ```

5. Run the development server:
    ```
    npm run dev
    php artisan serve
    ```

## Usage

1. Create a subject
2. Add units to the subject
3. Create topics within units
4. Add lessons to topics
5. Build lesson content using content blocks

## Content Block Types

- **Heading**: Section headings with customizable levels (H1-H6)
- **Paragraph**: Text content with formatting
- **Image**: Image content with optional caption
- **Video**: Video content with optional caption
- **Audio**: Audio content with optional description
- **List**: Bulleted lists of items
- **List Item**: Individual items within a list
