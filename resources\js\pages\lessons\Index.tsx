import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import { Separator } from '@/components/ui/separator';
import AppLayout from '@/layouts/app-layout';
import { BreadcrumbItem, Lesson, Topic } from '@/types';
import { DndContext, DragEndEvent, PointerSensor, closestCenter, useSensor, useSensors } from '@dnd-kit/core';
import { SortableContext, arrayMove, useSortable, verticalListSortingStrategy } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { Head, router, usePage } from '@inertiajs/react';
import axios from 'axios';
import { ChevronLeft, Edit, MoreHorizontal, Plus, Search, Trash } from 'lucide-react';
import { useEffect, useState } from 'react';

// Sortable lesson item component
function SortableLesson({
    lesson,
    onEdit,
    onDelete,
    onAdd,
}: {
    lesson: Lesson;
    onEdit: (lesson: Lesson) => void;
    onDelete: (lesson: Lesson) => void;
    onAdd: (lesson: Lesson) => void;
}) {
    const { attributes, listeners, setNodeRef, transform, transition } = useSortable({ id: lesson.id });

    const style = {
        transform: CSS.Transform.toString(transform),
        transition,
    };

    return (
        <div ref={setNodeRef} style={style} {...attributes} className="mb-4 overflow-hidden rounded-lg border border-gray-200 bg-white shadow-sm">
            <div className="flex items-center justify-between p-4">
                <div className="flex items-center gap-4">
                    <div className="flex h-10 w-10 flex-shrink-0 items-center justify-center rounded-full bg-blue-100 text-sm font-bold text-blue-600">
                        {lesson.order}
                    </div>
                    <div>
                        <h3 className="text-base font-medium text-gray-900">{lesson.title}</h3>
                        {lesson.description && <p className="text-sm text-gray-500">{lesson.description}</p>}
                    </div>
                </div>
                <div className="flex items-center gap-2">
                    <div {...listeners} className="cursor-grab rounded-md p-2 hover:bg-gray-100">
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="24"
                            height="24"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            className="h-5 w-5 text-gray-400"
                        >
                            <path d="M12 5v14M5 12h14"></path>
                        </svg>
                    </div>
                    <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon">
                                <MoreHorizontal className="h-5 w-5" />
                            </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => onAdd(lesson)}>
                                <Plus className="mr-2 h-4 w-4" />
                                Add content
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => onEdit(lesson)}>
                                <Edit className="mr-2 h-4 w-4" />
                                Edit
                            </DropdownMenuItem>
                            <DropdownMenuItem className="text-red-600" onClick={() => onDelete(lesson)}>
                                <Trash className="mr-2 h-4 w-4" />
                                Delete
                            </DropdownMenuItem>
                        </DropdownMenuContent>
                    </DropdownMenu>
                </div>
            </div>
        </div>
    );
}

interface Props {
    lessons: {
        data: Lesson[];
        current_page: number;
        last_page: number;
        per_page: number;
        total: number;
        from: number;
        to: number;
    };
}

// Create component placeholder
const Create = () => (
    <Button>
        <Plus className="mr-2 h-4 w-4" /> Create Lesson
    </Button>
);

// Delete component placeholder
const DeleteLesson = ({ lesson, open, onOpenChange }: { lesson: Lesson; open: boolean; onOpenChange: (open: boolean) => void }) => (
    <div>Delete Dialog for {lesson.title}</div>
);

// Edit component placeholder
const EditLesson = ({ lesson, open, onOpenChange }: { lesson: Lesson; open: boolean; onOpenChange: (open: boolean) => void }) => (
    <div>Edit Dialog for {lesson.title}</div>
);

function Index({ lessons }: Props) {
    const pageProps = usePage().props as any;
    const topic = pageProps.topic as Topic;
    const classId = pageProps.class?.id;
    const subjectId = pageProps.subject?.id;
    const unitId = pageProps.unit?.id;
    const openCreateDialog = pageProps.openCreateDialog as boolean;

    const [searchTerm, setSearchTerm] = useState('');
    const [filteredLessons, setFilteredLessons] = useState<Lesson[]>(lessons.data);
    const [editingLesson, setEditingLesson] = useState<Lesson | null>(null);
    const [deletingLesson, setDeletingLesson] = useState<Lesson | null>(null);
    const [sortedLessons, setSortedLessons] = useState<Lesson[]>(lessons.data);
    const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(openCreateDialog || false);

    // Set up sensors for drag and drop
    const sensors = useSensors(
        useSensor(PointerSensor, {
            activationConstraint: {
                distance: 8,
            },
        }),
    );

    useEffect(() => {
        if (searchTerm.trim() === '') {
            setFilteredLessons(sortedLessons);
        } else {
            const filtered = sortedLessons.filter(
                (lesson) =>
                    lesson.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                    (lesson.description && lesson.description.toLowerCase().includes(searchTerm.toLowerCase())),
            );
            setFilteredLessons(filtered);
        }
    }, [searchTerm, sortedLessons]);

    useEffect(() => {
        setSortedLessons(lessons.data);
    }, [lessons.data]);

    const handleDragEnd = async (event: DragEndEvent) => {
        const { active, over } = event;

        if (over && active.id !== over.id) {
            setSortedLessons((items) => {
                const oldIndex = items.findIndex((item) => item.id === active.id);
                const newIndex = items.findIndex((item) => item.id === over.id);

                const reordered = arrayMove(items, oldIndex, newIndex);

                // Update order values
                const updatedItems = reordered.map((item, index) => ({
                    ...item,
                    order: index + 1,
                }));

                // Save the new order to the server
                saveOrder(updatedItems);

                return updatedItems;
            });
        }
    };

    const saveOrder = async (items: Lesson[]) => {
        try {
            await axios.post(route('lessons.reorder'), {
                lessons: items.map((item) => ({ id: item.id, order: item.order })),
                topic_id: topic.id,
            });
        } catch (error) {
            console.error('Error saving lesson order:', error);
        }
    };

    const breadcrumbs: BreadcrumbItem[] = [
        {
            title: 'Topics',
            href: route('topics.index', { class_id: classId, subject_id: subjectId }),
        },
        {
            title: topic.name,
            href: '#',
        },
    ];

    const goToContentEditor = (lesson: Lesson) => {
        router.visit(route('lessons.content', { lesson_id: lesson.id }));
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title={`Lessons for ${topic.name}`} />
            <div className="mx-auto w-full max-w-5xl px-4 py-8 sm:px-6 lg:px-8">
                <div className="mb-6 flex items-center justify-between">
                    <div className="flex items-center gap-4">
                        <Button
                            variant="outline"
                            size="icon"
                            onClick={() => router.visit(route('topics.index', { class_id: classId, subject_id: subjectId, unit_id: unitId }))}
                        >
                            <ChevronLeft className="h-5 w-5" />
                        </Button>
                        <div>
                            <h1 className="text-2xl font-bold text-gray-900">Lessons for {topic.name}</h1>
                            <p className="text-sm text-gray-500">Create and manage lesson content for this topic</p>
                        </div>
                    </div>
                    <Button onClick={() => setIsCreateDialogOpen(true)}>
                        <Plus className="mr-2 h-4 w-4" /> Create Lesson
                    </Button>
                </div>

                <Separator className="my-6" />

                {/* Search section */}
                <div className="mb-6">
                    <div className="relative">
                        <Search className="absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 text-gray-500" />
                        <Input
                            placeholder="Search lessons by name..."
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                            className="pl-10"
                        />
                    </div>
                </div>

                {/* Lessons list with drag and drop */}
                {sortedLessons.length > 0 ? (
                    <DndContext sensors={sensors} collisionDetection={closestCenter} onDragEnd={handleDragEnd}>
                        <SortableContext items={filteredLessons.map((lesson) => lesson.id)} strategy={verticalListSortingStrategy}>
                            <div>
                                {filteredLessons.map((lesson) => (
                                    <SortableLesson
                                        key={lesson.id}
                                        lesson={lesson}
                                        onEdit={(lesson) => setEditingLesson(lesson)}
                                        onDelete={(lesson) => setDeletingLesson(lesson)}
                                        onAdd={(lesson) => goToContentEditor(lesson)}
                                    />
                                ))}
                            </div>
                        </SortableContext>
                    </DndContext>
                ) : (
                    <Card>
                        <CardHeader>
                            <CardTitle>No lessons yet</CardTitle>
                            <CardDescription>Start by adding your first lesson to this topic</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="flex justify-center">
                                <Create />
                            </div>
                        </CardContent>
                    </Card>
                )}

                {/* Pagination */}
                {lessons && lessons.last_page > 1 && searchTerm === '' && (
                    <div className="mt-6 flex items-center justify-between">
                        <div className="text-sm text-gray-700">
                            Showing {lessons.from} to {lessons.to} of {lessons.total} results
                        </div>
                        <div className="flex space-x-2">
                            {Array.from({ length: lessons.last_page }, (_, i) => i + 1).map((page) => (
                                <a
                                    key={page}
                                    href={route('lessons.index', { topic_id: topic.id, page })}
                                    className={`rounded px-3 py-1 ${
                                        page === lessons.current_page ? 'bg-blue-950 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                                    }`}
                                >
                                    {page}
                                </a>
                            ))}
                        </div>
                    </div>
                )}
            </div>

            {/* Edit Dialog */}
            {editingLesson && (
                <EditLesson lesson={editingLesson} open={!!editingLesson} onOpenChange={(open: boolean) => !open && setEditingLesson(null)} />
            )}

            {/* Delete Dialog */}
            {deletingLesson && (
                <DeleteLesson lesson={deletingLesson} open={!!deletingLesson} onOpenChange={(open: boolean) => !open && setDeletingLesson(null)} />
            )}

            {/* Create Lesson Dialog */}
            {isCreateDialogOpen && (
                <div className="bg-opacity-50 fixed inset-0 z-50 flex items-center justify-center bg-black">
                    <div className="w-full max-w-md rounded-lg bg-white p-8">
                        <h2 className="mb-4 text-xl font-bold">Create New Lesson</h2>
                        <p className="mb-6 text-gray-500">Add a new lesson to {topic.name}</p>

                        <form
                            onSubmit={(e) => {
                                e.preventDefault();
                                const form = e.currentTarget;
                                const formData = new FormData(form);

                                router.post(
                                    route('lessons.store'),
                                    {
                                        title: formData.get('title') as string,
                                        description: formData.get('description') as string,
                                        topic_id: topic.id,
                                    },
                                    {
                                        onSuccess: () => {
                                            setIsCreateDialogOpen(false);
                                        },
                                    },
                                );
                            }}
                        >
                            <div className="mb-4">
                                <label className="mb-1 block text-sm font-medium text-gray-700">Lesson Title</label>
                                <input
                                    type="text"
                                    name="title"
                                    className="w-full rounded-md border border-gray-300 p-2"
                                    placeholder="Enter lesson title"
                                    required
                                />
                            </div>

                            <div className="mb-6">
                                <label className="mb-1 block text-sm font-medium text-gray-700">Description (Optional)</label>
                                <textarea
                                    name="description"
                                    className="w-full rounded-md border border-gray-300 p-2"
                                    placeholder="Enter lesson description"
                                    rows={3}
                                />
                            </div>

                            <div className="flex justify-end gap-2">
                                <Button type="button" variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                                    Cancel
                                </Button>
                                <Button type="submit">Create Lesson</Button>
                            </div>
                        </form>
                    </div>
                </div>
            )}
        </AppLayout>
    );
}

export default Index;
