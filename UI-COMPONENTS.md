# UI Components Documentation

This document provides an overview of the UI components used in the Lesson Content Management System.

## Core Content Components

### ContentBlockViewer

```typescript
interface ContentBlockViewerProps {
    block: ContentBlock;
}
```

A component for displaying content blocks in a read-only format. It handles different types of content (headings, paragraphs, images, videos, audio, lists) and provides appropriate rendering for each.

**Example Usage:**

```tsx
<ContentBlockViewer block={contentBlock} />
```

### ContentBlockEditor

A component for editing content blocks. It provides interfaces for modifying content, uploading media, adding audio narration, and deleting blocks.

**Example Usage:**

```tsx
<ContentBlockEditor
    block={block}
    onUpdate={handleUpdateBlock}
    onDelete={handleDeleteBlock}
    onMoveUp={handleMoveUp}
    onMoveDown={handleMoveDown}
    showMoveUp={index > 0}
    showMoveDown={index < blocks.length - 1}
    isEditing={true}
/>
```

### BlockSelector

A component for selecting and adding new content blocks to a lesson.

**Example Usage:**

```tsx
<BlockSelector onSelectBlockType={handleAddBlock} parentType={parentBlock?.type} lessonId={lesson.id} parentId={parentBlock?.id} />
```

## Page Components

### Units Index Page

Displays all units for a subject, provides CRUD operations for units. Includes unit cards with name, description, and actions.

### Topics Index Page

Displays all topics for a unit, provides CRUD operations for topics. Includes topic cards with name, description, and actions.

### Lessons Index Page

Displays all lessons for a topic, provides CRUD operations for lessons. Shows lessons in a table format with title, status, and action buttons.

### Lesson Create Page

Form for creating a new lesson with fields for title, description, and publication status.

### Lesson Edit Page

Complex editor for modifying lesson details and managing content blocks. Includes tabs for basic information and content management.

### Lesson Show Page

Displays a lesson with all its content blocks. Suitable for both teacher review and student consumption.

## UI Component Usage Guidelines

1. **Content Block Components**: Use these for rendering and editing lesson content blocks
2. **Page Components**: Use these for complete page layouts
3. **Layout Components**: App layouts handle navigation, breadcrumbs, and page structure

## Component Hierarchy

```
AppLayout
├── Page Components (Units/Topics/Lessons Index, Create, Edit, Show)
│   ├── Content Block Components (BlockSelector, ContentBlockEditor, ContentBlockViewer)
│   │   └── UI Components (Button, Input, Switch, etc.)
```

## UI Libraries Used

1. **Radix UI**: Accessible UI primitives (Dialogs, Dropdowns, Tabs, etc.)
2. **Lucide React**: Icon components
3. **DND Kit**: Drag and drop functionality
4. **Sonner**: Toast notifications
