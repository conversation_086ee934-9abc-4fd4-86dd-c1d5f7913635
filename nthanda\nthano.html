<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title><PERSON><PERSON><PERSON><PERSON></title>
        <link rel="stylesheet" href="styles.css">

        <style>
            :root {
                --color-blue: #1c407c;
                --color-yellow: #ffd93d;
                --color-dark-yellow: #e6c235;
                --color-white: #ffffff;
                --color-red: #ff5252;
                --color-green: #4caf50;
                --color-dark-green: #388e3c;
                --color-orange: #FF9800;
                --color-purple: #9C27B0;
                --color-gray: #F5F5F5;
                --color-text-gray: #666;
            }
            
            body {
                background-color: var(--color-gray);
                margin: 0;
                padding: 0;
                font-family: "Fredoka", sans-serif;
                color: var(--color-blue);
            }
            
            .container {
                max-width: 800px;
                margin: 0 auto;
                padding: 0;
                position: relative;
            }
            
            .background-pattern {
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                opacity: 0.05;
                transform: rotate(45deg);
                z-index: -1;
            }
            
            .pattern-row {
                display: flex;
                justify-content: space-around;
                margin: 15px 0;
            }
            
            .pattern-shape {
                width: 30px;
                height: 30px;
                border-radius: 6px;
                margin: 8px;
            }
            
            .header {
                padding: 12px;
                background-color: white;
                border-bottom-left-radius: 20px;
                border-bottom-right-radius: 20px;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
                margin-bottom: 12px;
            }
            
            .header-top {
                display: flex;
                align-items: center;
                margin-bottom: 4px;
            }
            
            .back-button {
                margin-right: 12px;
                background-color: #F0F0F0;
                width: 40px;
                height: 40px;
                border-radius: 20px;
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
            }
            
            .back-button img {
                width: 24px;
                height: 24px;
            }
            
            .title {
                color: var(--color-blue);
                font-weight: 700;
                flex: 1;
                font-size: 20px;
            }
            
            .subtitle {
                color: #666;
                font-weight: 400;
                font-size: 13px;
                margin-left: 36px;
            }
            
            .scroll-content {
                padding: 12px;
            }
            
            .card-wrapper {
                width: 100%;
                margin-bottom: 12px;
                border-radius: 12px;
                overflow: hidden;
            }
            
            .card {
                border-radius: 12px;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
                background-color: white;
                border-width: 2px;
                border-style: solid;
                overflow: hidden;
            }
            
            .story-image {
                width: 100%;
                height: 160px;
                object-fit: cover;
            }
            
            .card-header {
                display: flex;
                align-items: center;
                margin-bottom: 8px;
                padding: 12px;
            }
            
            .icon-container {
                width: 48px;
                height: 48px;
                border-radius: 24px;
                display: flex;
                justify-content: center;
                align-items: center;
            }
            
            .icon-container img {
                width: 32px;
                height: 32px;
                filter: brightness(0) invert(1);
            }
            
            .header-info {
                flex: 1;
                margin-left: 10px;
            }
            
            .card-title {
                color: var(--color-blue);
                font-weight: 600;
                font-size: 16px;
                margin-bottom: 2px;
            }
            
            .card-description {
                color: #666;
                font-weight: 400;
                font-size: 13px;
            }
            
            .preview-container {
                background-color: #F8F8F8;
                padding: 12px;
                margin: 0 12px;
                border-radius: 10px;
                margin-bottom: 8px;
            }
            
            .preview-text {
                font-weight: 400;
                font-size: 13px;
                color: #444;
                font-style: italic;
                line-height: 18px;
            }
            
            .characters-container {
                padding: 12px;
                padding-top: 4px;
            }
            
            .section-title {
                font-weight: 600;
                font-size: 13px;
                color: #666;
                margin-bottom: 6px;
            }
            
            .characters-list {
                display: flex;
                flex-wrap: wrap;
                gap: 6px;
            }
            
            .character-chip {
                margin-right: 6px;
                margin-bottom: 6px;
                height: 28px;
                padding: 4px 12px;
                border-radius: 14px;
                display: inline-flex;
                align-items: center;
            }
            
            .chip-text {
                font-weight: 500;
                font-size: 11px;
            }
            
            .play-button {
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 8px 0;
                margin-top: 4px;
            }
            
            .play-button img {
                width: 32px;
                height: 32px;
                filter: brightness(0) invert(1);
            }
            
            .play-text {
                margin-left: 6px;
                font-weight: 600;
                font-size: 14px;
                color: white;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <!-- Background Pattern -->
            <div class="background-pattern">
                <div class="pattern-row">
                    <div class="pattern-shape"
                        style="background-color: var(--color-orange);"></div>
                    <div class="pattern-shape"
                        style="background-color: var(--color-green);"></div>
                    <div class="pattern-shape"
                        style="background-color: var(--color-blue);"></div>
                    <div class="pattern-shape"
                        style="background-color: var(--color-purple);"></div>
                </div>
                <div class="pattern-row">
                    <div class="pattern-shape"
                        style="background-color: var(--color-green);"></div>
                    <div class="pattern-shape"
                        style="background-color: var(--color-blue);"></div>
                    <div class="pattern-shape"
                        style="background-color: var(--color-purple);"></div>
                    <div class="pattern-shape"
                        style="background-color: var(--color-orange);"></div>
                </div>
                <div class="pattern-row">
                    <div class="pattern-shape"
                        style="background-color: var(--color-blue);"></div>
                    <div class="pattern-shape"
                        style="background-color: var(--color-purple);"></div>
                    <div class="pattern-shape"
                        style="background-color: var(--color-orange);"></div>
                    <div class="pattern-shape"
                        style="background-color: var(--color-green);"></div>
                </div>
                <div class="pattern-row">
                    <div class="pattern-shape"
                        style="background-color: var(--color-purple);"></div>
                    <div class="pattern-shape"
                        style="background-color: var(--color-orange);"></div>
                    <div class="pattern-shape"
                        style="background-color: var(--color-green);"></div>
                    <div class="pattern-shape"
                        style="background-color: var(--color-blue);"></div>
                </div>
                <div class="pattern-row">
                    <div class="pattern-shape"
                        style="background-color: var(--color-orange);"></div>
                    <div class="pattern-shape"
                        style="background-color: var(--color-green);"></div>
                    <div class="pattern-shape"
                        style="background-color: var(--color-blue);"></div>
                    <div class="pattern-shape"
                        style="background-color: var(--color-purple);"></div>
                </div>
                <div class="pattern-row">
                    <div class="pattern-shape"
                        style="background-color: var(--color-green);"></div>
                    <div class="pattern-shape"
                        style="background-color: var(--color-blue);"></div>
                    <div class="pattern-shape"
                        style="background-color: var(--color-purple);"></div>
                    <div class="pattern-shape"
                        style="background-color: var(--color-orange);"></div>
                </div>
                <div class="pattern-row">
                    <div class="pattern-shape"
                        style="background-color: var(--color-blue);"></div>
                    <div class="pattern-shape"
                        style="background-color: var(--color-purple);"></div>
                    <div class="pattern-shape"
                        style="background-color: var(--color-orange);"></div>
                    <div class="pattern-shape"
                        style="background-color: var(--color-green);"></div>
                </div>
                <div class="pattern-row">
                    <div class="pattern-shape"
                        style="background-color: var(--color-purple);"></div>
                    <div class="pattern-shape"
                        style="background-color: var(--color-orange);"></div>
                    <div class="pattern-shape"
                        style="background-color: var(--color-green);"></div>
                    <div class="pattern-shape"
                        style="background-color: var(--color-blue);"></div>
                </div>
            </div>

            <div class="header">
                <div class="header-top">
                    <div class="back-button" onclick="goBack()">
                        <img src="assets/icons/arrowleft.png" alt="Back">
                    </div>
                    <h1 class="title">Nthano Za Ana! 📚</h1>
                </div>
                <p class="subtitle">Sankhani nthano yomwe mukufuna kuwonera!
                    🎉</p>
            </div>

            <div class="scroll-content">
                <!-- First Story Card -->
                <div class="card-wrapper" onclick="selectNthano(1)">
                    <div class="card" style="border-color: var(--color-green);">
                        <img src="assets/nthano/nyama1.jpg"
                            alt="Nyama za m`Nkhalango" class="story-image">

                        <div class="card-header">
                            <div class="icon-container"
                                style="background-color: var(--color-green);">
                                1
                            </div>
                            <div class="header-info">
                                <h3 class="card-title">Nyama za m`Nkhalango</h3>
                                <p class="card-description">Nthano ya nyama za
                                    m`nkhalango!</p>
                            </div>
                        </div>

                        <div class="preview-container">
                            <p class="preview-text">Kalekale kunali nyama
                                zambiri m'nkhalango. Tsiku lina mkango unafuna
                                kupanga msonkhano ndi nyama zonse...</p>
                        </div>

                        <div class="characters-container">
                            <p class="section-title">Nyama mu Nthanoyi:</p>
                            <div class="characters-list">
                                <div class="character-chip"
                                    style="background-color: rgba(76, 175, 80, 0.12);">
                                    <span class="chip-text"
                                        style="color: var(--color-green);">Mkango</span>
                                </div>
                                <div class="character-chip"
                                    style="background-color: rgba(76, 175, 80, 0.12);">
                                    <span class="chip-text"
                                        style="color: var(--color-green);">Kalulu</span>
                                </div>
                                <div class="character-chip"
                                    style="background-color: rgba(76, 175, 80, 0.12);">
                                    <span class="chip-text"
                                        style="color: var(--color-green);">Njovu</span>
                                </div>
                                <div class="character-chip"
                                    style="background-color: rgba(76, 175, 80, 0.12);">
                                    <span class="chip-text"
                                        style="color: var(--color-green);">Nyati</span>
                                </div>
                            </div>
                        </div>

                        <div class="play-button"
                            style="background-color: var(--color-green);">
                            <img src="assets/icons/play.png" alt="Play">
                            <span class="play-text">Wonera Nthano</span>
                        </div>
                    </div>
                </div>

                <!-- Second Story Card -->
                <div class="card-wrapper" onclick="selectNthano(2)">
                    <div class="card" style="border-color: var(--color-green);">
                        <img src="assets/nthano/nkhandwe.png"
                            alt="Nkhandwe ndi Mbuzi" class="story-image">

                        <div class="card-header">
                            <div class="icon-container"
                                style="background-color: var(--color-green);">
                                2
                            </div>
                            <div class="header-info">
                                <h3 class="card-title">Nkhandwe ndi Mbuzi</h3>
                                <p class="card-description">Nthano ya nkhandwe
                                    ndi mbuzi!</p>
                            </div>
                        </div>

                        <div class="preview-container">
                            <p class="preview-text">Tsiku lina nkhandwe yanjala
                                inakumana ndi mbuzi yomwe inali kudya udzu
                                pafupi ndi mtsinje...</p>
                        </div>

                        <div class="characters-container">
                            <p class="section-title">Nyama mu Nthanoyi:</p>
                            <div class="characters-list">
                                <div class="character-chip"
                                    style="background-color: rgba(76, 175, 80, 0.12);">
                                    <span class="chip-text"
                                        style="color: var(--color-green);">Nkhandwe</span>
                                </div>
                                <div class="character-chip"
                                    style="background-color: rgba(76, 175, 80, 0.12);">
                                    <span class="chip-text"
                                        style="color: var(--color-green);">Mbuzi</span>
                                </div>
                            </div>
                        </div>

                        <div class="play-button"
                            style="background-color: var(--color-green);">
                            <img src="assets/icons/play.png" alt="Play">
                            <span class="play-text">Wonera Nthano</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <script>
            function goBack() {
                window.history.back();
            }
            
            function selectNthano(id) {
                window.location.href = `nthano/nyama.html`;
            }
        </script>
    </body>
</html>