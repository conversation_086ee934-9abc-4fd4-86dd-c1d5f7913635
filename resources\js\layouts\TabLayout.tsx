import Heading from '@/components/heading';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { cn } from '@/lib/utils';
import { type NavItem } from '@/types';
import { Link } from '@inertiajs/react';
import React from 'react';

function TabLayout({
    children,
    sidebarNavItems,
    title,
    description,
}: {
    children: React.ReactNode;
    sidebarNavItems: NavItem[];
    title: string;
    description: string;
}) {
    return (
        <div className="px-4 py-6">
            <Heading title={title} description={description} />

            <div className="flex flex-col space-y-8 lg:flex-row lg:space-y-0 lg:space-x-6">
                <aside className="w-full max-w-xl lg:w-40">
                    <nav className="flex flex-col space-y-1 space-x-0">
                        {sidebarNavItems.map((item) => (
                            <Button
                                key={item.title}
                                size="sm"
                                variant="ghost"
                                asChild
                                className={cn('flex w-full items-center justify-between', {
                                    'bg-blue-950 text-white hover:bg-blue-950/90 hover:text-white': item.isActive,
                                })}
                            >
                                <Link href={item.href} className="flexitems-center justify-between" prefetch>
                                    {item.title}
                                </Link>
                            </Button>
                        ))}
                    </nav>
                </aside>

                <Separator className="my-6 md:hidden" />

                <div className="w-full flex-1">
                    <section className="w-full space-y-12">{children}</section>
                </div>
            </div>
        </div>
    );
}

export default TabLayout;
