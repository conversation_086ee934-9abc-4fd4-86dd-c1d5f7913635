import { <PERSON><PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { Textarea } from '@/components/ui/textarea';
import AppLayout from '@/layouts/app-layout';
import { BreadcrumbItem } from '@/types';
import { Head, Link, router, useForm } from '@inertiajs/react';
import { ArrowLeft, CheckCircle, Edit, File, Image, MoreHorizontal, Plus, Trash, Upload } from 'lucide-react';
import { useEffect, useState } from 'react';
import { useDropzone } from 'react-dropzone';

interface Assessment {
    id: number;
    title: string;
    description: string;
    class_id: number;
    subject_id: number;
    unit_id: number;
    topic_id: number;
}

interface Question {
    id: number;
    assessment_id: number;
    question: string;
    question_type: 'text' | 'image';
    option_a: string;
    option_b: string;
    option_c: string;
    option_d: string;
    correct_answer: 'A' | 'B' | 'C' | 'D';
}

interface QuestionForm {
    question: string;
    question_type: 'text' | 'image';
    option_a: string;
    option_b: string;
    option_c: string;
    option_d: string;
    correct_answer: 'A' | 'B' | 'C' | 'D';
    _method?: string;
    [key: string]: string | undefined;
}

interface Props {
    assessment: Assessment;
    questions: Question[];
}

function Questions({ assessment, questions }: Props) {
    const [isAddingQuestion, setIsAddingQuestion] = useState(false);
    const [isEditingQuestion, setIsEditingQuestion] = useState(false);
    const [currentQuestion, setCurrentQuestion] = useState<Question | null>(null);
    const [isConfirmingDelete, setIsConfirmingDelete] = useState(false);
    const [questionToDelete, setQuestionToDelete] = useState<Question | null>(null);
    const [imageFiles, setImageFiles] = useState<Record<string, File | null>>({
        option_a: null,
        option_b: null,
        option_c: null,
        option_d: null,
    });
    const [imagePreviews, setImagePreviews] = useState<Record<string, string>>({
        option_a: '',
        option_b: '',
        option_c: '',
        option_d: '',
    });

    // We're not destructuring 'post' here to avoid the unused variable warning
    const { data, setData, processing, errors, reset } = useForm<QuestionForm>({
        question: '',
        question_type: 'text',
        option_a: '',
        option_b: '',
        option_c: '',
        option_d: '',
        correct_answer: 'A',
    });

    // Create dropzones for each option
    const optionADropzone = useDropzone({
        accept: { 'image/*': ['.jpeg', '.jpg', '.png', '.gif'] },
        onDrop: (acceptedFiles: File[]) => {
            if (acceptedFiles.length === 0) return;
            const file = acceptedFiles[0];

            // Revoke any existing object URL to prevent memory leaks
            if (imagePreviews.option_a) {
                URL.revokeObjectURL(imagePreviews.option_a);
            }

            const objectUrl = URL.createObjectURL(file);
            setImageFiles((prev) => ({ ...prev, option_a: file }));
            setImagePreviews((prev) => ({ ...prev, option_a: objectUrl }));
        },
    });

    const optionBDropzone = useDropzone({
        accept: { 'image/*': ['.jpeg', '.jpg', '.png', '.gif'] },
        onDrop: (acceptedFiles: File[]) => {
            if (acceptedFiles.length === 0) return;
            const file = acceptedFiles[0];

            // Revoke any existing object URL to prevent memory leaks
            if (imagePreviews.option_b) {
                URL.revokeObjectURL(imagePreviews.option_b);
            }

            const objectUrl = URL.createObjectURL(file);
            setImageFiles((prev) => ({ ...prev, option_b: file }));
            setImagePreviews((prev) => ({ ...prev, option_b: objectUrl }));
        },
    });

    const optionCDropzone = useDropzone({
        accept: { 'image/*': ['.jpeg', '.jpg', '.png', '.gif'] },
        onDrop: (acceptedFiles: File[]) => {
            if (acceptedFiles.length === 0) return;
            const file = acceptedFiles[0];

            // Revoke any existing object URL to prevent memory leaks
            if (imagePreviews.option_c) {
                URL.revokeObjectURL(imagePreviews.option_c);
            }

            const objectUrl = URL.createObjectURL(file);
            setImageFiles((prev) => ({ ...prev, option_c: file }));
            setImagePreviews((prev) => ({ ...prev, option_c: objectUrl }));
        },
    });

    const optionDDropzone = useDropzone({
        accept: { 'image/*': ['.jpeg', '.jpg', '.png', '.gif'] },
        onDrop: (acceptedFiles: File[]) => {
            if (acceptedFiles.length === 0) return;
            const file = acceptedFiles[0];

            // Revoke any existing object URL to prevent memory leaks
            if (imagePreviews.option_d) {
                URL.revokeObjectURL(imagePreviews.option_d);
            }

            const objectUrl = URL.createObjectURL(file);
            setImageFiles((prev) => ({ ...prev, option_d: file }));
            setImagePreviews((prev) => ({ ...prev, option_d: objectUrl }));
        },
    });

    // Cleanup object URLs when component unmounts
    useEffect(() => {
        return () => {
            // Revoke all object URLs when component unmounts
            Object.values(imagePreviews).forEach((url) => {
                if (url && url.startsWith('blob:')) {
                    URL.revokeObjectURL(url);
                }
            });
        };
    }, []);

    // Get the appropriate dropzone for a given option
    const getDropzoneForOption = (option: string) => {
        switch (option.toLowerCase()) {
            case 'a':
                return optionADropzone;
            case 'b':
                return optionBDropzone;
            case 'c':
                return optionCDropzone;
            case 'd':
                return optionDDropzone;
            default:
                return optionADropzone;
        }
    };

    const resetForm = () => {
        // Clean up any existing object URLs first
        Object.values(imagePreviews).forEach((url) => {
            if (url && url.startsWith('blob:')) {
                URL.revokeObjectURL(url);
            }
        });

        reset();
        setImageFiles({
            option_a: null,
            option_b: null,
            option_c: null,
            option_d: null,
        });
        setImagePreviews({
            option_a: '',
            option_b: '',
            option_c: '',
            option_d: '',
        });
    };

    const validateImageOptions = () => {
        if (data.question_type !== 'image') return true;

        // Check that each option has either a preview URL or a file selected
        const options = ['a', 'b', 'c', 'd'];
        const missingOptions = options.filter((opt) => {
            const key = `option_${opt}`;
            return !imageFiles[key] && !imagePreviews[key];
        });

        if (missingOptions.length > 0) {
            const missingLabels = missingOptions.map((opt) => opt.toUpperCase()).join(', ');
            alert(`Please upload images for all options. Missing: Option ${missingLabels}`);
            return false;
        }

        // Make sure the correct answer has an image
        const correctOptionKey = `option_${data.correct_answer.toLowerCase()}`;
        if (!imageFiles[correctOptionKey] && !imagePreviews[correctOptionKey]) {
            alert(`Please upload an image for the correct answer (Option ${data.correct_answer})`);
            return false;
        }

        return true;
    };

    // Fixed onSubmit function that handles form data properly for both text and image questions
    const onSubmit = (e: React.FormEvent) => {
        e.preventDefault();

        // Validate image options if using image type
        if (!validateImageOptions()) {
            return;
        }

        try {
            // Create a FormData object for multipart/form-data submission
            const formData = new FormData();

            // Add basic question data
            formData.append('question', data.question);
            formData.append('question_type', data.question_type);
            formData.append('correct_answer', data.correct_answer);

            // Handle options based on question type
            if (data.question_type === 'text') {
                // For text questions, simply append the text options
                formData.append('option_a', data.option_a || '');
                formData.append('option_b', data.option_b || '');
                formData.append('option_c', data.option_c || '');
                formData.append('option_d', data.option_d || '');
            } else {
                // For image questions, handle file uploads or existing URLs
                ['a', 'b', 'c', 'd'].forEach((opt) => {
                    const optionKey = `option_${opt}`;
                    const file = imageFiles[optionKey];

                    if (file) {
                        // If we have a new file, append it to the form data
                        formData.append(optionKey, file);
                    } else if (imagePreviews[optionKey]) {
                        // If we're keeping an existing image URL (from the database)
                        // The server expects a string value when not uploading a new file
                        formData.append(optionKey, imagePreviews[optionKey]);
                    }
                });
            }

            // Add method override for PATCH requests when updating
            if (currentQuestion) {
                formData.append('_method', 'PATCH');
            }

            // Determine the submission URL based on whether we're creating or updating
            const url = currentQuestion ? `/assessments/${assessment.id}/questions/${currentQuestion.id}` : `/assessments/${assessment.id}/questions`;

            // Use Inertia router with forceFormData option to ensure proper handling
            router.post(url, formData, {
                forceFormData: true,
                onSuccess: () => {
                    if (currentQuestion) {
                        setIsEditingQuestion(false);
                    } else {
                        setIsAddingQuestion(false);
                    }
                    resetForm();
                    setCurrentQuestion(null);
                },
                onError: (errors) => {
                    console.error('Submission errors:', errors);
                    alert('There was an error saving your question. Please check the form and try again.');
                },
            });
        } catch (error) {
            console.error('Error preparing form submission:', error);
            alert('There was an unexpected error. Please try again.');
        }
    };

    const handleEdit = (question: Question) => {
        setCurrentQuestion(question);
        setData({
            question: question.question,
            question_type: question.question_type,
            option_a: question.option_a,
            option_b: question.option_b,
            option_c: question.option_c,
            option_d: question.option_d,
            correct_answer: question.correct_answer,
        });

        if (question.question_type === 'image') {
            // For image-type questions, we load the existing image URLs
            // into the previews, but not as files (files would be null)
            const previewUrls = {
                option_a: question.option_a || '',
                option_b: question.option_b || '',
                option_c: question.option_c || '',
                option_d: question.option_d || '',
            };

            setImagePreviews(previewUrls);

            // Clear any previously set files since we're now editing
            // and using the existing URLs until new files are selected
            setImageFiles({
                option_a: null,
                option_b: null,
                option_c: null,
                option_d: null,
            });
        }

        setIsEditingQuestion(true);
    };

    const handleDelete = () => {
        if (!questionToDelete) return;

        router.delete(`/assessments/${assessment.id}/questions/${questionToDelete.id}`, {
            onSuccess: () => {
                setIsConfirmingDelete(false);
                setQuestionToDelete(null);
            },
        });
    };

    // Add this function to your component for image cleanup
    const cleanupImage = (optionKey: string) => {
        const url = imagePreviews[optionKey];
        if (url && url.startsWith('blob:')) {
            URL.revokeObjectURL(url);
        }
        setImageFiles((prev) => ({
            ...prev,
            [optionKey]: null,
        }));
        setImagePreviews((prev) => ({
            ...prev,
            [optionKey]: '',
        }));
    };

    const breadcrumbs: BreadcrumbItem[] = [
        {
            title: 'Assessments',
            href: '/assessments',
        },
        {
            title: assessment.title,
            href: route('assessments.index', { class_id: assessment.class_id, subject_id: assessment.subject_id }),
        },
        {
            title: 'Questions',
            href: `/assessments/${assessment.id}/questions`,
        },
    ];

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title={`${assessment.title} - Questions`} />
            <div className="container mx-auto p-6">
                <div className="mb-8 flex items-center justify-between">
                    <div>
                        <div className="flex items-center gap-2">
                            <Link href={route('assessments.index', { class_id: assessment.class_id, subject_id: assessment.subject_id })}>
                                <Button variant="ghost" size="icon" className="rounded-full">
                                    <ArrowLeft className="h-5 w-5" />
                                </Button>
                            </Link>
                            <h1 className="text-2xl font-bold text-gray-900">{assessment.title}</h1>
                        </div>
                        <p className="text-sm text-gray-500">{assessment.description}</p>
                    </div>
                    <Button onClick={() => setIsAddingQuestion(true)}>
                        <Plus className="mr-2 h-4 w-4" />
                        Add Question
                    </Button>
                </div>

                {questions.length === 0 ? (
                    <div className="rounded-lg border border-dashed p-8 text-center">
                        <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-gray-100">
                            <File className="h-6 w-6 text-gray-500" />
                        </div>
                        <h3 className="mt-4 text-lg font-medium">No questions yet</h3>
                        <p className="mt-1 text-sm text-gray-500">Get started by adding your first question.</p>
                        <Button className="mt-4" onClick={() => setIsAddingQuestion(true)}>
                            <Plus className="mr-2 h-4 w-4" />
                            Add Question
                        </Button>
                    </div>
                ) : (
                    <div className="space-y-4">
                        {questions.map((question, index) => (
                            <Card key={question.id} className="overflow-hidden">
                                <CardHeader className="pb-2">
                                    <div className="flex items-start justify-between">
                                        <div className="flex items-center gap-2">
                                            <div className="flex h-6 w-6 items-center justify-center rounded-full bg-gray-100 text-xs font-medium">
                                                {index + 1}
                                            </div>
                                            <CardTitle className="text-base">{question.question}</CardTitle>
                                        </div>
                                        <DropdownMenu>
                                            <DropdownMenuTrigger asChild>
                                                <Button variant="ghost" size="icon" className="h-8 w-8">
                                                    <MoreHorizontal className="h-4 w-4" />
                                                </Button>
                                            </DropdownMenuTrigger>
                                            <DropdownMenuContent align="end">
                                                <DropdownMenuItem onClick={() => handleEdit(question)}>
                                                    <Edit className="mr-2 h-4 w-4" />
                                                    Edit
                                                </DropdownMenuItem>
                                                <DropdownMenuItem
                                                    onClick={() => {
                                                        setQuestionToDelete(question);
                                                        setIsConfirmingDelete(true);
                                                    }}
                                                    className="text-red-600"
                                                >
                                                    <Trash className="mr-2 h-4 w-4" />
                                                    Delete
                                                </DropdownMenuItem>
                                            </DropdownMenuContent>
                                        </DropdownMenu>
                                    </div>
                                    <div className="mt-1 flex items-center gap-2">
                                        <div className="flex items-center rounded-full bg-gray-100 px-2 py-0.5 text-xs">
                                            {question.question_type === 'text' ? (
                                                <>
                                                    <File className="mr-1 h-3 w-3" />
                                                    Text
                                                </>
                                            ) : (
                                                <>
                                                    <Image className="mr-1 h-3 w-3" />
                                                    Image
                                                </>
                                            )}
                                        </div>
                                        <div className="flex items-center rounded-full bg-green-100 px-2 py-0.5 text-xs text-green-700">
                                            <CheckCircle className="mr-1 h-3 w-3" />
                                            Correct Answer: {question.correct_answer}
                                        </div>
                                    </div>
                                </CardHeader>
                                <CardContent>
                                    <div className="grid grid-cols-2 gap-4">
                                        {['A', 'B', 'C', 'D'].map((option) => {
                                            const optionKey = `option_${option.toLowerCase()}` as keyof Question;
                                            const optionValue = question[optionKey];
                                            const isCorrect = question.correct_answer === option;

                                            return (
                                                <div
                                                    key={option}
                                                    className={`rounded-lg border p-3 ${
                                                        isCorrect ? 'border-green-200 bg-green-50' : 'border-gray-200'
                                                    }`}
                                                >
                                                    <div className="mb-2 flex items-center gap-2">
                                                        <div
                                                            className={`flex h-5 w-5 items-center justify-center rounded-full text-xs font-medium ${
                                                                isCorrect ? 'bg-green-100 text-green-700' : 'bg-gray-100 text-gray-700'
                                                            }`}
                                                        >
                                                            {option}
                                                        </div>
                                                        {isCorrect && <CheckCircle className="h-4 w-4 text-green-500" />}
                                                    </div>
                                                    {question.question_type === 'text' ? (
                                                        <p className="text-sm">{optionValue}</p>
                                                    ) : (
                                                        optionValue && (
                                                            <img src={optionValue} alt={`Option ${option}`} className="h-20 w-full object-contain" />
                                                        )
                                                    )}
                                                </div>
                                            );
                                        })}
                                    </div>
                                </CardContent>
                            </Card>
                        ))}
                    </div>
                )}

                {/* Add/Edit Question Dialog */}
                <Dialog
                    open={isAddingQuestion || isEditingQuestion}
                    onOpenChange={() => (isAddingQuestion ? setIsAddingQuestion(false) : setIsEditingQuestion(false))}
                >
                    <DialogContent className="sm:max-w-[600px]">
                        <DialogHeader>
                            <DialogTitle>{isEditingQuestion ? 'Edit Question' : 'Add Question'}</DialogTitle>
                            <DialogDescription>
                                {isEditingQuestion
                                    ? 'Update the question details below.'
                                    : 'Add a new question to this assessment. Choose between text or image options.'}
                            </DialogDescription>
                        </DialogHeader>
                        <form onSubmit={onSubmit} className="space-y-6">
                            <div className="space-y-4">
                                <div>
                                    <Label htmlFor="question">Question</Label>
                                    <Textarea
                                        id="question"
                                        value={data.question}
                                        onChange={(e) => setData('question', e.target.value)}
                                        placeholder="Enter your question here"
                                        className="mt-1"
                                    />
                                    {errors.question && <p className="mt-1 text-sm text-red-600">{errors.question}</p>}
                                </div>
                                <div>
                                    <Label>Question Type</Label>
                                    <RadioGroup
                                        className="mt-2 flex space-x-4"
                                        value={data.question_type}
                                        onValueChange={(value) => setData('question_type', value as 'text' | 'image')}
                                    >
                                        <div className="flex items-center space-x-2">
                                            <RadioGroupItem value="text" id="question-type-text" />
                                            <Label htmlFor="question-type-text" className="cursor-pointer font-normal">
                                                Text Options
                                            </Label>
                                        </div>
                                        <div className="flex items-center space-x-2">
                                            <RadioGroupItem value="image" id="question-type-image" />
                                            <Label htmlFor="question-type-image" className="cursor-pointer font-normal">
                                                Image Options
                                            </Label>
                                        </div>
                                    </RadioGroup>
                                    {errors.question_type && <p className="mt-1 text-sm text-red-600">{errors.question_type}</p>}
                                </div>

                                <Separator />

                                <div className="space-y-4">
                                    <h4 className="text-sm font-medium">Answer Options</h4>
                                    {['A', 'B', 'C', 'D'].map((option) => {
                                        const optionKey = `option_${option.toLowerCase()}` as keyof QuestionForm;
                                        return (
                                            <div key={option} className="space-y-2">
                                                <Label htmlFor={optionKey.toString()}>Option {option}</Label>
                                                {data.question_type === 'text' ? (
                                                    <Input
                                                        id={optionKey.toString()}
                                                        value={data[optionKey] || ''}
                                                        onChange={(e) => setData(optionKey, e.target.value)}
                                                        placeholder={`Enter option ${option}`}
                                                    />
                                                ) : (
                                                    <div className="space-y-2">
                                                        <div
                                                            {...getDropzoneForOption(option.toLowerCase()).getRootProps()}
                                                            className="flex cursor-pointer flex-col items-center justify-center rounded-lg border border-dashed border-gray-300 p-6 hover:bg-gray-50"
                                                        >
                                                            <input {...getDropzoneForOption(option.toLowerCase()).getInputProps()} />
                                                            {imagePreviews[`option_${option.toLowerCase()}`] ? (
                                                                <div className="relative w-full">
                                                                    <img
                                                                        src={imagePreviews[`option_${option.toLowerCase()}`]}
                                                                        alt={`Option ${option} preview`}
                                                                        className="mx-auto h-32 object-contain"
                                                                    />
                                                                    <Button
                                                                        type="button"
                                                                        variant="destructive"
                                                                        size="sm"
                                                                        className="absolute top-0 right-0 h-7 w-7 rounded-full p-0"
                                                                        onClick={(e) => {
                                                                            e.stopPropagation();
                                                                            cleanupImage(`option_${option.toLowerCase()}`);
                                                                        }}
                                                                    >
                                                                        <Trash className="h-4 w-4" />
                                                                    </Button>
                                                                </div>
                                                            ) : (
                                                                <>
                                                                    <Upload className="mb-2 h-8 w-8 text-gray-400" />
                                                                    <p className="text-sm text-gray-500">Drag & drop or click to upload</p>
                                                                </>
                                                            )}
                                                        </div>
                                                    </div>
                                                )}
                                                {errors[optionKey] && <p className="text-sm text-red-600">{errors[optionKey]}</p>}
                                            </div>
                                        );
                                    })}
                                </div>

                                <div>
                                    <Label htmlFor="correct_answer">Correct Answer</Label>
                                    <Select
                                        value={data.correct_answer}
                                        onValueChange={(value) => setData('correct_answer', value as 'A' | 'B' | 'C' | 'D')}
                                    >
                                        <SelectTrigger id="correct_answer" className="mt-1">
                                            <SelectValue placeholder="Select the correct answer" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="A">Option A</SelectItem>
                                            <SelectItem value="B">Option B</SelectItem>
                                            <SelectItem value="C">Option C</SelectItem>
                                            <SelectItem value="D">Option D</SelectItem>
                                        </SelectContent>
                                    </Select>
                                    {errors.correct_answer && <p className="mt-1 text-sm text-red-600">{errors.correct_answer}</p>}
                                </div>
                            </div>
                            <DialogFooter>
                                <Button
                                    type="button"
                                    variant="outline"
                                    onClick={() => (isAddingQuestion ? setIsAddingQuestion(false) : setIsEditingQuestion(false))}
                                >
                                    Cancel
                                </Button>
                                <Button type="submit" disabled={processing}>
                                    {isEditingQuestion ? 'Update Question' : 'Add Question'}
                                </Button>
                            </DialogFooter>
                        </form>
                    </DialogContent>
                </Dialog>

                {/* Delete Confirmation Dialog */}
                <Dialog open={isConfirmingDelete} onOpenChange={setIsConfirmingDelete}>
                    <DialogContent className="sm:max-w-[425px]">
                        <DialogHeader>
                            <DialogTitle>Delete Question</DialogTitle>
                            <DialogDescription>Are you sure you want to delete this question? This action cannot be undone.</DialogDescription>
                        </DialogHeader>
                        <DialogFooter>
                            <Button variant="outline" onClick={() => setIsConfirmingDelete(false)}>
                                Cancel
                            </Button>
                            <Button variant="destructive" onClick={handleDelete}>
                                Delete
                            </Button>
                        </DialogFooter>
                    </DialogContent>
                </Dialog>
            </div>
        </AppLayout>
    );
}

export default Questions;
