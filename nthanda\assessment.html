<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Student Assessment</title>
        <link rel="stylesheet" href="styles.css">
        <style>
        :root {
            --color-blue: #1c407c;
            --color-yellow: #ffd93d;
            --color-dark-yellow: #e6c235;
            --color-white: #ffffff;
            --color-red: #ff5252;
            --color-green: #4caf50;
            --color-dark-green: #388e3c;
            --color-gray: #F5F5F5;
            --color-text-gray: #666;
        }

        body {
            background-color: var(--color-yellow);
            margin: 0;
            padding: 0;
            font-family: "Fredoka", sans-serif;
            color: var(--color-blue);
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 0;
        }

        .header {
            padding: 16px;
            background-color: var(--color-yellow);
            padding-bottom: 20px;
        }

        .header-top {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
        }

        .back-button {
            background-color: var(--color-blue);
            margin-right: 8px;
            padding: 8px;
            border-radius: 10px;
            border: 2px solid var(--color-dark-yellow);
            border-top-width: 0;
            cursor: pointer;
            display: flex;
            justify-content: center;
            align-items: center;
            text-decoration: none;
        }

        .back-button::before {
            content: "←";
            color: var(--color-yellow);
            font-size: 20px;
            font-weight: bold;
        }

        .header-title {
            font-weight: 700;
            font-size: 28px;
            color: var(--color-blue);
            margin: 0;
            flex: 1;
            margin-left: 16px;
        }

        .header-subtitle {
            font-weight: 500;
            font-size: 24px;
            color: var(--color-blue);
            margin-left: 40px;
            margin-bottom: 0;
        }

        .content {
            flex: 1;
            border-top-left-radius: 24px;
            border-top-right-radius: 24px;
            background-color: var(--color-white);
            padding: 16px;
            min-height: 80vh;
        }

        .assessment-container {
            max-width: 800px;
            margin: 2rem auto;
            background-color: #fff;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            padding: 2rem;
            border: 3px solid var(--primary-color);
            position: relative;
            overflow: hidden;
        }

        .back-button {
            background-color: var(--primary-color);
            padding: 12px;
            border-radius: 12px;
            border: 2px solid var(--accent-color);
            border-top-width: 0;
            cursor: pointer;
            display: flex;
            justify-content: center;
            align-items: center;
            color: white;
            margin-bottom: 1rem;
            width: 45px;
            height: 45px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
            transition: all 0.3s ease;
            text-decoration: none;
        }

        .back-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 12px rgba(0,0,0,0.2);
        }

        .back-button::before {
            content: "←";
            font-size: 1.5rem;
            font-weight: bold;
        }

        .assessment-container::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 8px;
            background: linear-gradient(90deg, var(--primary-color), var(--accent-color), var(--secondary-color), var(--primary-color));
            background-size: 300% 100%;
            animation: gradient-animation 6s ease infinite;
        }

        @keyframes gradient-animation {
            0% {background-position: 0% 50%}
            50% {background-position: 100% 50%}
            100% {background-position: 0% 50%}
        }

        .assessment-header {
            margin-bottom: 2rem;
            border-bottom: 2px dashed var(--light-accent);
            padding-bottom: 1.5rem;
            position: relative;
        }

        .assessment-header-top {
            display: flex;
            align-items: flex-start;
            margin-bottom: 1rem;
        }

        .assessment-header-content {
            flex: 1;
            margin-left: 1rem;
        }

        .assessment-header h1 {
            margin-top: 0;
            margin-bottom: 1rem;
            font-size: 2.2rem;
            font-weight: 700;
            color: var(--primary-color);
            animation: bounceTitle 1.5s ease infinite;
            transform-origin: left center;
        }

        @keyframes bounceTitle {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-5px); }
        }

        .text-muted {
            color: #6c757d;
        }

        .flex-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
        }

        .badge {
            display: inline-block;
            padding: 0.35rem 0.75rem;
            font-size: 0.85rem;
            font-weight: 700;
            line-height: 1;
            text-align: center;
            white-space: nowrap;
            vertical-align: baseline;
            border-radius: 50px;
            margin-right: 0.5rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            transform: translateY(0);
            transition: all 0.3s ease;
        }

        .badge:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }

        .bg-primary {
            background-color: var(--primary-color);
            color: #fff;
        }

        .bg-secondary {
            background-color: var(--accent-color);
            color: #333;
        }

        .timer-container {
            text-align: center;
            margin-bottom: 1.5rem;
            font-size: 1.2rem;
            background-color: rgba(255,255,255,0.8);
            border-radius: 50px;
            padding: 0.5rem 1rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            border: 2px solid var(--accent-color);
            position: relative;
        }

        .timer-container::before {
            content: "⏱️";
            margin-right: 8px;
        }

        .timer {
            font-weight: bold;
            color: var(--danger-color);
            font-family: monospace;
            font-size: 1.3rem;
        }

        .loading-center {
            text-align: center;
            padding: 3rem 0;
        }

        .spinner {
            display: inline-block;
            width: 3rem;
            height: 3rem;
            border: 0.25rem solid rgba(0, 0, 0, 0.1);
            border-right-color: var(--primary-color);
            border-radius: 50%;
            animation: spinner 0.75s linear infinite;
        }

        @keyframes spinner {
            to {
                transform: rotate(360deg);
            }
        }

        .mt-3 {
            margin-top: 1rem;
        }

        .question-card {
            background-color: var(--color-gray);
            border-radius: 16px;
            padding: 16px;
            margin-bottom: 1.5rem;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            position: relative;
            overflow: hidden;
            animation: fadeIn 0.5s ease-in-out;
        }

        .question-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
        }

        .question-number {
            color: var(--color-blue);
            font-weight: bold;
            margin-bottom: 0.75rem;
            font-size: 1.1rem;
            display: flex;
            align-items: center;
            background-color: var(--color-yellow);
            padding: 0.35rem 0.75rem;
            border-radius: 50px;
        }

        .question-audio-icon {
            margin-left: 10px;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background-color: var(--color-blue);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .question-audio-icon svg {
            width: 14px;
            height: 14px;
            stroke: var(--color-yellow);
        }

        .question-audio-icon:hover {
            transform: scale(1.1);
            background-color: var(--color-dark-yellow);
        }

        .question-audio-icon.playing {
            background-color: var(--color-dark-yellow);
            animation: pulse 1.5s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        .question-text {
            font-size: 1.2rem;
            margin-bottom: 1.5rem;
            line-height: 1.5;
            color: var(--color-blue);
            padding: 10px;
            border-left: 4px solid var(--color-blue);
            background-color: rgba(28, 64, 124, 0.05);
        }

        .options-container {
            margin-bottom: 1rem;
        }

        .option-item {
            margin-bottom: 1rem;
        }

        .option-label {
            display: block;
            padding: 1rem 1.2rem;
            border: 2px solid #dee2e6;
            border-radius: var(--border-radius);
            margin-bottom: 0.75rem;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .option-label:hover {
            background-color: rgba(74, 137, 220, 0.05);
            border-color: var(--primary-color);
            transform: translateX(5px);
        }

        .option-input {
            display: none;
        }

        .option-input:checked + .option-label {
            background-color: rgba(74, 137, 220, 0.2);
            border-color: var(--primary-color);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .option-input:checked + .option-label::before {
            content: "✓";
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 1.2rem;
            color: var(--secondary-color);
            font-weight: bold;
        }

        .btn {
            display: inline-block;
            font-weight: 600;
            line-height: 1.5;
            text-align: center;
            text-decoration: none;
            vertical-align: middle;
            cursor: pointer;
            user-select: none;
            background-color: transparent;
            border: 2px solid transparent;
            padding: 0.6rem 1.2rem;
            font-size: 1rem;
            border-radius: 10px;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background-color: var(--color-blue);
            color: var(--color-white);
            border: 2px solid var(--color-dark-yellow);
            border-top-width: 0;
        }

        .btn-primary:hover {
            background-color: var(--color-blue);
            transform: translateY(-3px);
            box-shadow: 0 6px 10px rgba(0, 0, 0, 0.2);
        }

        .btn-secondary {
            background-color: var(--color-yellow);
            color: var(--color-blue);
            border: 2px solid var(--color-dark-yellow);
            border-top-width: 0;
        }

        .btn-secondary:hover {
            background-color: var(--color-dark-yellow);
            transform: translateY(-3px);
            box-shadow: 0 6px 10px rgba(0, 0, 0, 0.2);
        }

        .btn-submit {
            background-color: var(--color-yellow);
            color: var(--color-blue);
            font-weight: 700;
            padding: 12px 24px;
            font-size: 16px;
            border: 2px solid var(--color-dark-yellow);
            border-top-width: 0;
            display: inline-block;
            text-align: center;
            border-radius: 10px;
            cursor: pointer;
        }

        .btn-submit:hover {
            background-color: var(--color-dark-yellow);
            transform: translateY(-3px);
            box-shadow: 0 6px 10px rgba(0, 0, 0, 0.2);
        }

        .text-center {
            text-align: center;
        }

        .text-left {
            text-align: left;
        }

        .mt-4 {
            margin-top: 1.5rem;
        }

        .hide {
            display: none !important;
        }

        .result-container {
            text-align: center;
            padding: 2rem;
            background-color: var(--color-white);
            border-radius: 16px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
        }

        .result-container::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 8px;
            background: linear-gradient(to right, var(--secondary-color), var(--primary-color));
        }

        .score-display {
            font-size: 4.5rem;
            font-weight: bold;
            margin: 1.5rem 0;
            color: var(--primary-color);
            text-shadow: 2px 2px 0 rgba(0,0,0,0.1);
            position: relative;
            display: inline-block;
        }

        .score-display::after {
            content: "🎯";
            position: absolute;
            font-size: 2rem;
            top: -10px;
            right: -40px;
        }

        .pass-badge {
            display: inline-block;
            padding: 0.75rem 2rem;
            border-radius: 50px;
            font-weight: bold;
            font-size: 1.2rem;
            margin: 1.5rem 0;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            position: relative;
        }

        .pass-badge.passed {
            background-color: #d4edda;
            color: #155724;
        }

        .pass-badge.passed::before {
            content: "🎉";
            margin-right: 10px;
        }

        .pass-badge.failed {
            background-color: #f8d7da;
            color: #721c24;
        }

        .pass-badge.failed::before {
            content: "😢";
            margin-right: 10px;
        }

        .result-row {
            display: flex;
            flex-wrap: wrap;
            margin: 0 -0.75rem;
            justify-content: center;
        }

        .result-column {
            flex: 0 0 33.333333%;
            max-width: 33.333333%;
            padding: 0 0.75rem;
        }

        .card {
            position: relative;
            display: flex;
            flex-direction: column;
            min-width: 0;
            word-wrap: break-word;
            background-color: #fff;
            background-clip: border-box;
            border: 2px solid rgba(0,0,0,.1);
            border-radius: var(--border-radius);
            margin-bottom: 1.5rem;
            overflow: hidden;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 15px rgba(0,0,0,0.1);
        }

        .card-body {
            flex: 1 1 auto;
            padding: 1.5rem;
            text-align: center;
        }

        .card h5 {
            color: var(--primary-color);
            font-size: 1.1rem;
            margin-top: 0;
            margin-bottom: 1rem;
        }

        .fs-3 {
            font-size: 2.5rem !important;
        }

        .fw-bold {
            font-weight: 700 !important;
        }

        .text-success {
            color: var(--secondary-color) !important;
        }

        .text-danger {
            color: var(--danger-color) !important;
        }

        .question-review {
            text-align: left;
            margin-top: 2.5rem;
            padding: 1rem;
            background-color: #f8f9fa;
            border-radius: var(--border-radius);
        }

        .question-review h3 {
            color: var(--primary-color);
            margin-bottom: 1.5rem;
            padding-bottom: 0.75rem;
            border-bottom: 2px dashed var(--light-accent);
        }

        .review-item {
            padding: 1.25rem;
            border: 2px solid #dee2e6;
            border-radius: var(--border-radius);
            margin-bottom: 1.5rem;
            background-color: #fff;
            box-shadow: 0 4px 8px rgba(0,0,0,0.05);
            position: relative;
            overflow: hidden;
        }

        .review-item.correct {
            border-left: 8px solid var(--secondary-color);
        }

        .review-item.incorrect {
            border-left: 8px solid var(--danger-color);
        }

        .feedback-text {
            font-weight: bold;
            margin: 0.75rem 0;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            display: inline-block;
        }

        .feedback-text.correct {
            color: white;
            background-color: var(--secondary-color);
        }

        .feedback-text.incorrect {
            color: white;
            background-color: var(--danger-color);
        }

        .answer-details {
            margin-top: 1.25rem;
            padding: 1rem;
            border: 1px solid #eee;
            border-radius: var(--border-radius);
            background-color: #f9f9f9;
        }

        .answer-details p {
            margin-bottom: 0.75rem;
        }

        .answer-details p:last-child {
            margin-bottom: 0;
        }

        .image-question img {
            max-width: 100%;
            max-height: 300px;
            margin: 1rem 0;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            border: 3px solid #eee;
        }

        .option-image img {
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            border: 2px solid #eee;
            transition: all 0.3s ease;
        }

        .option-input:checked + .option-label .option-image img {
            border-color: var(--primary-color);
            box-shadow: 0 4px 10px rgba(74, 137, 220, 0.25);
        }

        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(255, 255, 255, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }

        .alert {
            position: relative;
            padding: 1.25rem;
            margin-bottom: 1.5rem;
            border: 2px solid transparent;
            border-radius: var(--border-radius);
            box-shadow: 0 4px 8px rgba(0,0,0,0.05);
        }

        .alert-info {
            color: #0c5460;
            background-color: #d1ecf1;
            border-color: #bee5eb;
        }

        #submission-status {
            background-color: var(--light-accent);
            border-radius: var(--border-radius);
            padding: 1rem;
            margin-top: 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 6px rgba(0,0,0,0.05);
        }

        #submission-status .spinner {
            width: 1.5rem;
            height: 1.5rem;
            margin-right: 0.75rem;
        }

        @media (max-width: 768px) {
            .result-column {
                flex: 0 0 100%;
                max-width: 100%;
            }

            .assessment-container {
                padding: 1.5rem;
                margin: 1rem;
            }

            .assessment-header h1 {
                font-size: 1.8rem;
            }

            .question-text {
                font-size: 1.1rem;
            }

            .option-label {
                padding: 0.75rem 1rem;
            }

            .score-display {
                font-size: 3.5rem;
            }
        }

        /* Special styles for children */
        .assessment-container::after {
            content: "";
            position: absolute;
            width: 120px;
            height: 120px;
            background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24"><path fill="none" d="M0 0h24v24H0z"/><path d="M12 17l-5.878 3.59 1.598-6.7-5.23-4.48 6.865-.55L12 2.5l2.645 6.36 6.866.55-5.23 4.48 1.598 6.7z" fill="rgba(255, 191, 0, 0.2)"/></svg>');
            background-repeat: repeat;
            opacity: 0.5;
            z-index: -1;
            top: 20px;
            right: 20px;
        }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <div class="header-top">
                    <a class="back-button" href="assessments.html"
                        title="Back to Assessments"></a>
                    <h1 class="header-title" id="assessment-title">Loading
                        Assessment...</h1>
                </div>
                <p class="header-subtitle" id="assessment-description"></p>
            </div>

            <div class="content">
                <!-- Assessment Header Info -->
                <div class="flex-container" style="margin-bottom: 20px;">
                    <div>
                        <span class="badge bg-primary"
                            id="subject-badge"></span>
                        <span class="badge bg-secondary"
                            id="topic-badge"></span>
                        <span class="badge"
                            style="background-color: #9c27b0; color: white;"
                            id="attempt-badge"></span>
                    </div>
                    <div class="timer-container" id="timer-container">
                        Time remaining: <span class="timer"
                            id="timer">30:00</span>
                    </div>
                </div>

                <!-- Attempt Information -->
                <div id="attempt-info" class="hide"
                    style="background-color: #f8f9fa; border-radius: 12px; padding: 16px; margin-bottom: 20px; border-left: 4px solid #9c27b0;">
                    <h4 style="margin: 0 0 8px 0; color: #9c27b0;">📊 Your
                        Assessment History</h4>
                    <div id="attempt-details"></div>
                </div>

                <!-- Loading State -->
                <div id="loading" class="loading-center">
                    <div class="spinner"></div>
                    <p class="mt-3">Loading assessment questions...</p>
                </div>

                <!-- Questions Container -->
                <div id="questions-container" class="hide">
                    <!-- Questions will be loaded here dynamically -->
                </div>

                <!-- Results Container -->
                <div id="result-container" class="result-container hide">
                    <h2>Assessment Results</h2>
                    <div class="score-display" id="score-display">85%</div>
                    <div class="pass-badge passed" id="pass-badge">PASSED</div>
                    <div class="result-row mt-4">
                        <div class="result-column">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h5>Total Questions</h5>
                                    <p class="fs-3 fw-bold"
                                        id="total-questions">10</p>
                                </div>
                            </div>
                        </div>
                        <div class="result-column">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h5>Correct Answers</h5>
                                    <p class="fs-3 fw-bold text-success"
                                        id="correct-answers">8</p>
                                </div>
                            </div>
                        </div>
                        <div class="result-column">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h5>Incorrect Answers</h5>
                                    <p class="fs-3 fw-bold text-danger"
                                        id="incorrect-answers">2</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="question-review" id="question-review">
                        <!-- Question review will be loaded here -->
                    </div>

                    <div class="mt-4">
                        <button class="btn btn-primary"
                            id="back-to-assessments">Back to
                            Assessments</button>
                        <button class="btn btn-secondary"
                            id="retake-assessment" style="margin-left: 10px;">
                            🔄 Retake Assessment</button>
                    </div>
                </div>

                <!-- Submit Button -->
                <div class="text-center mt-4" id="submit-container">
                    <button id="submit-assessment" class="btn-submit">Submit
                        Assessment</button>
                    <div id="submission-status"
                        style="display:none; margin-top:10px;">
                        <div class="spinner"
                            style="display:inline-block; width:1.5rem; height:1.5rem;"></div>
                        <span style="margin-left:8px;">Processing your
                            submission...</span>
                    </div>
                </div>
            </div>
        </div>

        <script src="jquery.js"></script>
        <script src="axios.js"></script>
        <script src="auth.js"></script>
        <script src="content.js"></script>
        <script>
        // Configuration
        const API_URL = "https://lomoni.kaizen-mw.com/api";
        const AUTH_TOKEN = localStorage.getItem("auth_token");
        const urlParams = new URLSearchParams(window.location.search);
        const assessmentId = urlParams.get('id');
        const userId = getStudentDetails().id;

        // State Variables
        let assessment = null;
        let questions = [];
        let resultId = null;
        let timerInterval = null;
        let timeRemaining = 30 * 60; // 30 minutes in seconds
        let attemptInfo = null;

        // Initialize the application when document is ready
        $(document).ready(function() {
            if (!assessmentId) {
                window.location.href = 'assessments.html'; // Redirect to assessments list if no ID provided
                return;
            }

            // Set up event listeners
            $("#submit-assessment").on("click", submitAssessment);
            $("#back-to-assessments").on("click", function() {
                // Stop any playing audio before navigating away
                if (window.currentAudio) {
                    window.currentAudio.pause();
                    window.currentAudio = null;
                }
                window.location.href = 'assessments.html';
            });
            $("#retake-assessment").on("click", function() {
                // Stop any playing audio before navigating away
                if (window.currentAudio) {
                    window.currentAudio.pause();
                    window.currentAudio = null;
                }
                // Reload the page to start a new attempt
                window.location.reload();
            });

            // Clean up audio when navigating away from the page
            $(window).on('beforeunload', function() {
                if (window.currentAudio) {
                    window.currentAudio.pause();
                    window.currentAudio = null;
                }
            });

            // Load assessment
            loadAssessment();
        });

        // Load assessment data from API
        function loadAssessment() {
            axios.get(`${API_URL}/assessments/details/${assessmentId}/${userId}`)
            .then(function(response) {
                const data = response.data;

                if (!data.success) {
                    if (data.result) {
                        // Assessment already completed
                        showCompletedAssessmentMessage(data.result);
                        return;
                    }
                    throw new Error(data.message || 'Failed to load assessment');
                }

                assessment = data.assessment;
                questions = assessment.questions;
                resultId = data.result_id;
                attemptInfo = data.attempt_info;

                // Update assessment details
                $("#assessment-title").text(assessment.title);
                $("#assessment-description").text(assessment.description);
                $("#subject-badge").text(assessment.subject);

                if (assessment.topic) {
                    $("#topic-badge").text(assessment.topic);
                } else {
                    $("#topic-badge").addClass("hide");
                }

                // Update attempt information
                updateAttemptInfo();

                // Render questions
                renderQuestions();

                // Start timer
                startTimer();

                // Hide loading, show questions
                $("#loading").addClass("hide");
                $("#questions-container").removeClass("hide");
            })
            .catch(function(error) {
                console.error('Failed to load assessment:', error);
                alert('Failed to load assessment. Please try again later.');
            });
        }

        // Update attempt information display
        function updateAttemptInfo() {
            if (!attemptInfo) return;

            // Update attempt badge
            $("#attempt-badge").text(`Attempt ${attemptInfo.current_attempt}`);

            // Show attempt history if this is a retake
            if (attemptInfo.is_retake && attemptInfo.completed_attempts > 0) {
                const $attemptDetails = $("#attempt-details");

                let historyHtml = `
                    <p style="margin: 0 0 8px 0; font-size: 14px;">
                        <strong>Previous Attempts:</strong> ${attemptInfo.completed_attempts}
                    </p>
                `;

                if (attemptInfo.average_score !== null) {
                    const avgStatus = attemptInfo.average_score >= 50 ? 'passed' : 'failed';
                    const statusColor = avgStatus === 'passed' ? '#4caf50' : '#f44336';

                    historyHtml += `
                        <p style="margin: 0 0 8px 0; font-size: 14px;">
                            <strong>Average Score:</strong>
                            <span style="color: ${statusColor}; font-weight: bold;">
                                ${attemptInfo.average_score}% (${avgStatus.toUpperCase()})
                            </span>
                        </p>
                    `;
                }

                historyHtml += `
                    <p style="margin: 0; font-size: 12px; color: #666;">
                        💡 This is attempt #${attemptInfo.current_attempt}. Your final score will be the average of all attempts.
                    </p>
                `;

                $attemptDetails.html(historyHtml);
                $("#attempt-info").removeClass("hide");
            }
        }

        // Render questions in the UI
        function renderQuestions() {
            const $questionsContainer = $("#questions-container");
            $questionsContainer.empty();

            questions.forEach(function(question, index) {
                const $questionCard = $("<div>").addClass("question-card");

                const $questionNumber = $("<div>").addClass("question-number")
                    .text(`Question ${index + 1} of ${questions.length}`);

                // Add audio icon if audio_url exists
                if (question.audio_url) {
                    const $audioIcon = $('<div>').addClass('question-audio-icon')
                        .html('<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polygon points="11 5 6 9 2 9 2 15 6 15 11 19 11 5"></polygon><path d="M15.54 8.46a5 5 0 0 1 0 7.07"></path><path d="M19.07 4.93a10 10 0 0 1 0 14.14"></path></svg>')
                        .attr('title', 'Play audio')
                        .on('click', function(event) {
                            playQuestionAudio(question.audio_url, event.currentTarget);
                        });
                    $questionNumber.append($audioIcon);
                }

                const $questionText = $("<div>").addClass("question-text");

                if (question.question_type === 'image') {
                    // For image questions, display the image
                    $questionText.html(`
                        <div class="image-question">
                            <img src="https://lomoni.kaizen-mw.com${question.question}" alt="Question image" class="img-fluid">
                        </div>
                    `);
                } else {
                    // For text questions, display the text
                    $questionText.text(question.question);
                }

                const $optionsContainer = $("<div>").addClass("options-container");

                // Create options (A, B, C, D)
                const options = ['A', 'B', 'C', 'D'];
                options.forEach(function(option) {
                    const $optionDiv = $("<div>").addClass("option-item");

                    const $input = $("<input>").attr({
                        type: "radio",
                        name: `question_${question.id}`,
                        id: `question_${question.id}_option_${option}`,
                        class: "option-input",
                        value: option,
                        "data-question-id": question.id
                    });

                    let $label;

                    if (question.question_type === 'image') {
                        // For image questions, options are also images
                        $label = $("<label>").attr({
                            for: `question_${question.id}_option_${option}`,
                            class: "option-label"
                        }).html(`
                            <strong>${option}.</strong>
                            <div class="option-image">
                                <img src="https://lomoni.kaizen-mw.com${question.options[option]}" alt="Option ${option}" class="img-fluid" style="max-height: 100px;">
                            </div>
                        `);
                    } else {
                        // For regular questions, options are text
                        $label = $("<label>").attr({
                            for: `question_${question.id}_option_${option}`,
                            class: "option-label"
                        }).html(`<strong>${option}.</strong> ${question.options[option]}`);
                    }

                    $optionDiv.append($input).append($label);
                    $optionsContainer.append($optionDiv);
                });

                $questionCard.append($questionNumber)
                    .append($questionText)
                    .append($optionsContainer);

                $questionsContainer.append($questionCard);
            });
        }

        // Play question audio
        function playQuestionAudio(audioUrl, clickedElement) {
            // Stop any currently playing audio
            if (window.currentAudio) {
                window.currentAudio.pause();
                window.currentAudio.currentTime = 0;
            }

            // Create new audio element or use existing one
            if (!window.currentAudio) {
                window.currentAudio = new Audio();

                // Set up error handling
                window.currentAudio.onerror = function() {
                    console.error('Error loading audio file');
                    $('.question-audio-icon').removeClass('playing');
                    alert('Could not play audio. The file may be missing or corrupted.');
                };

                // Clean up when audio ends
                window.currentAudio.onended = function() {
                    $('.question-audio-icon').removeClass('playing');
                };
            }

            try {
                // Set the source and play
                window.currentAudio.src = `https://lomoni.kaizen-mw.com${audioUrl}`;

                // Visual feedback for the clicked icon
                $('.question-audio-icon').removeClass('playing');
                $(clickedElement).addClass('playing');

                // Play the audio with proper error handling
                window.currentAudio.play()
                    .catch(error => {
                        console.error('Error playing audio:', error);
                        $('.question-audio-icon').removeClass('playing');
                        alert('Could not play audio. Please try again.');
                    });
            } catch (error) {
                console.error('Error setting up audio:', error);
                $('.question-audio-icon').removeClass('playing');
            }
        }

        // Start the assessment timer
        function startTimer() {
            updateTimerDisplay();

            timerInterval = setInterval(function() {
                timeRemaining--;

                if (timeRemaining <= 0) {
                    clearInterval(timerInterval);
                    submitAssessment(); // Auto-submit when time is up
                } else {
                    updateTimerDisplay();
                }
            }, 1000);
        }

        // Update the timer display
        function updateTimerDisplay() {
            const minutes = Math.floor(timeRemaining / 60);
            const seconds = timeRemaining % 60;
            $("#timer").text(`${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`);

            // Change color when less than 5 minutes remaining
            if (timeRemaining < 300) {
                $("#timer").css("color", "#dc3545");
            }
        }

        // Submit the assessment
        function submitAssessment() {
            // Validate all questions are answered
            const user_id = getStudentDetails().id;
            const unansweredQuestions = questions.filter(function(question) {
                const radios = $(`input[name="question_${question.id}"]:checked`);
                return radios.length === 0;
            });

            if (unansweredQuestions.length > 0) {
                if (!confirm(`You have ${unansweredQuestions.length} unanswered questions. Are you sure you want to submit?`)) {
                    return;
                }
            }

            // Collect answers
            const answers = [];
            questions.forEach(function(question) {
                const selectedOption = $(`input[name="question_${question.id}"]:checked`);
                if (selectedOption.length > 0) {
                    answers.push({
                        question_id: question.id,
                        answer: selectedOption.val()
                    });
                }
            });

            // Disable submit button and show status
            $("#submit-assessment").prop("disabled", true);
            $("#submission-status").show();

            axios.post(`${API_URL}/assessments/${assessmentId}/submit`, {
                result_id: resultId,
                answers: answers,
                user_id: user_id
            })
            .then(function(response) {
                const data = response.data;

                if (!data.success) {
                    throw new Error(data.message || 'Failed to submit assessment');
                }

                // Stop timer
                clearInterval(timerInterval);

                // Get detailed results
                loadResults();
            })
            .catch(function(error) {
                console.error('Failed to submit assessment:', error);
                alert('Failed to submit assessment. Please try again.');
                // Re-enable submit button and hide status
                $("#submit-assessment").prop("disabled", false);
                $("#submission-status").hide();
            });
        }

        // Load detailed results after submission
        function loadResults() {
            // Already showing loading status, just leave it
            const user_id = getStudentDetails().id;
            axios.get(`${API_URL}/assessments/${assessmentId}/result/${user_id}`)
            .then(function(response) {
                const data = response.data;

                if (!data.success) {
                    throw new Error(data.message || 'Failed to load results');
                }

                const result = data.result;

                // Display results
                displayResults(result);

                // Hide questions and show results
                $("#questions-container").addClass("hide");
                $("#result-container").removeClass("hide");
                $("#submit-container").addClass("hide");
                $("#timer-container").addClass("hide");

                // Add event listeners for audio icons in the review section
                $(document).on('click', '.review-audio', function() {
                    const audioUrl = $(this).data('audio-url');
                    playQuestionAudio(audioUrl, this);
                });
            })
            .catch(function(error) {
                console.error('Failed to load results:', error);
                alert('Assessment was submitted, but we could not load your results. Please check your assessments page.');
                // Re-enable submit button and hide status
                $("#submit-assessment").prop("disabled", false);
                $("#submission-status").hide();
            });
        }

        // Display assessment results
        function displayResults(result) {
            // Check if this is the new multi-attempt format
            const currentAttempt = result.current_attempt || result;
            const score = currentAttempt.score || result.score;
            const passed = currentAttempt.passed || result.passed;
            const correctQuestions = currentAttempt.correct_questions || result.correct_questions;
            const failedQuestions = currentAttempt.failed_questions || result.failed_questions;

            // Update score display - show current attempt score
            $("#score-display").text(`${Math.round(score)}%`);

            // Update pass/fail badge
            const $passBadge = $("#pass-badge");
            $passBadge.text(passed ? 'PASSED' : 'FAILED');
            $passBadge.removeClass().addClass(`pass-badge ${passed ? 'passed' : 'failed'}`);

            // Update statistics
            $("#total-questions").text(correctQuestions + failedQuestions);
            $("#correct-answers").text(correctQuestions);
            $("#incorrect-answers").text(failedQuestions);

            // Add attempt information if available
            if (result.current_attempt) {
                const attemptInfoHtml = `
                    <div style="background-color: #f8f9fa; border-radius: 12px; padding: 16px; margin: 20px 0; border-left: 4px solid #9c27b0;">
                        <h4 style="margin: 0 0 12px 0; color: #9c27b0;">📊 Attempt Summary</h4>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px; font-size: 14px;">
                            <div>
                                <strong>This Attempt (#${result.current_attempt.attempt_number}):</strong><br>
                                Score: ${Math.round(result.current_attempt.score)}%
                                (${result.current_attempt.passed ? 'PASSED' : 'FAILED'})
                            </div>
                            ${result.average_score ? `
                            <div>
                                <strong>Overall Average:</strong><br>
                                Score: ${result.average_score}%
                                (${result.average_passed ? 'PASSED' : 'FAILED'})
                            </div>
                            ` : ''}
                        </div>
                        ${result.total_attempts > 1 ? `
                        <p style="margin: 12px 0 0 0; font-size: 12px; color: #666;">
                            💡 You have completed ${result.total_attempts} attempts. Your final grade is based on the average score.
                        </p>
                        ` : ''}
                    </div>
                `;
                $("#question-review").before(attemptInfoHtml);
            }

            // Display question review
            const $questionReview = $("#question-review");
            $questionReview.html('<h3 class="mb-4">Question Review</h3>');

            result.questions.forEach(function(question, index) {
                const $reviewItem = $("<div>").addClass(`review-item ${question.is_correct ? 'correct' : 'incorrect'}`);

                let questionContent;
                let studentAnswerContent;
                let correctAnswerContent;

                // Handle question content based on question type
                if (question.question_type === 'image') {
                    questionContent = `
                        <div class="image-question">
                            <img src="https://lomoni.kaizen-mw.com${question.question}" alt="Question image" class="img-fluid">
                        </div>
                    `;

                    // For image questions, show the options as images
                    studentAnswerContent = `
                        <strong>Your answer:</strong> ${question.student_answer}.
                        <div class="option-image mt-2">
                            <img src="https://lomoni.kaizen-mw.com${question.options[question.student_answer]}" alt="Your answer" class="img-fluid" style="max-height: 100px;">
                        </div>
                    `;

                    if (!question.is_correct) {
                        correctAnswerContent = `
                            <strong>Correct answer:</strong> ${question.correct_answer}.
                            <div class="option-image mt-2">
                                <img src="https://lomoni.kaizen-mw.com${question.options[question.correct_answer]}" alt="Correct answer" class="img-fluid" style="max-height: 100px;">
                            </div>
                        `;
                    }
                } else {
                    // For text questions, display as normal
                    questionContent = question.question;
                    studentAnswerContent = `<strong>Your answer:</strong> ${question.student_answer}. ${question.options[question.student_answer]}`;
                    if (!question.is_correct) {
                        correctAnswerContent = `<strong>Correct answer:</strong> ${question.correct_answer}. ${question.options[question.correct_answer]}`;
                    }
                }

                $reviewItem.html(`
                    <div class="question-number">
                        Question ${index + 1}
                        ${question.audio_url ? `
                        <div class="question-audio-icon review-audio" data-audio-url="${question.audio_url}">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polygon points="11 5 6 9 2 9 2 15 6 15 11 19 11 5"></polygon><path d="M15.54 8.46a5 5 0 0 1 0 7.07"></path><path d="M19.07 4.93a10 10 0 0 1 0 14.14"></path></svg>
                        </div>
                        ` : ''}
                    </div>
                    <div class="question-text">${questionContent}</div>

                    <div class="feedback-text ${question.is_correct ? 'correct' : 'incorrect'}">
                        ${question.is_correct ? '✓ Correct' : '✗ Incorrect'}
                    </div>

                    <div class="answer-details">
                        <p>${studentAnswerContent}</p>
                        ${!question.is_correct ? `<p>${correctAnswerContent}</p>` : ''}
                    </div>
                `);

                $questionReview.append($reviewItem);
            });
        }

        // Show message for already completed assessment
        function showCompletedAssessmentMessage(result) {
            $("#loading").addClass("hide");
            $("#timer-container").addClass("hide");
            $("#submit-container").addClass("hide");

            const $messageContainer = $("<div>").addClass("text-center py-5");
            $messageContainer.html(`
                <div class="alert alert-info">
                    <h4>Assessment Already Completed</h4>
                    <p>You have already completed this assessment with a score of <strong>${result.score}%</strong>.</p>
                    <p>Status: <span class="badge ${result.passed ? 'bg-success' : 'bg-danger'}">
                        ${result.passed ? 'PASSED' : 'FAILED'}
                    </span></p>
                    <p>Completed on: ${new Date(result.completed_at).toLocaleString()}</p>
                </div>
                <button class="btn btn-primary" id="view-results">View Results</button>
                <button class="btn btn-secondary" id="back-to-list">Back to Assessment List</button>
            `);

            $(".content").append($messageContainer);

            // When viewing results of an already completed assessment
            $("#view-results").on("click", function() {
                loadResults();
                $messageContainer.remove(); // Remove the message after clicking view results
            });

            $("#back-to-list").on("click", function() {
                window.location.href = 'assessments.html';
            });
        }
        </script>
    </body>
</html>
