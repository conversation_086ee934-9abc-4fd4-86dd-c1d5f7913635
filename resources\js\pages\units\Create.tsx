import { useForm, usePage } from '@inertiajs/react';
import { Plus } from 'lucide-react';
import { useState } from 'react';

import CustomButton from '@/components/CustomButton';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { SchoolClass, Subject } from '@/types';

function Create() {
    const [open, setOpen] = useState(false);
    const schoolclass = usePage().props.class as SchoolClass;
    const subject = usePage().props.subject as Subject;

    const { data, setData, post, processing, errors, reset } = useForm({
        name: '',
        class_id: schoolclass.id,
        subject_id: subject.id,
    });

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { name, value } = e.target;
        setData(name as keyof typeof data, value);
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();

        post(route('units.store'), {
            preserveScroll: true,
            onSuccess: () => {
                setOpen(false);
                reset();
            },
        });
    };

    const handleOpenChange = (open: boolean) => {
        setOpen(open);
        if (!open) {
            reset();
        }
    };

    return (
        <Dialog open={open} onOpenChange={handleOpenChange}>
            <DialogTrigger asChild>
                <CustomButton onClick={() => setOpen(true)}>
                    <Plus className="h-4 w-4" />
                    Add Unit
                </CustomButton>
            </DialogTrigger>
            <DialogContent className="sm:max-w-md">
                <DialogHeader>
                    <DialogTitle>Add New Unit</DialogTitle>
                    <DialogDescription>Create a new unit in {subject.name} subject.</DialogDescription>
                </DialogHeader>
                <form onSubmit={handleSubmit}>
                    <div className="grid gap-4 py-4">
                        <div className="grid gap-2">
                            <Label htmlFor="name">Unit Name</Label>
                            <Input
                                id="name"
                                name="name"
                                value={data.name}
                                onChange={handleChange}
                                placeholder="Enter unit name"
                                className={errors.name ? 'border-red-500' : ''}
                                autoFocus
                            />
                            {errors.name && <p className="text-xs text-red-500">{errors.name}</p>}
                        </div>
                    </div>
                    <DialogFooter>
                        <button
                            type="button"
                            onClick={() => handleOpenChange(false)}
                            className="cursor-pointer rounded-md px-4 py-2 text-sm font-medium text-gray-600 hover:bg-gray-100"
                            disabled={processing}
                        >
                            Cancel
                        </button>
                        <button
                            type="submit"
                            className="cursor-pointer rounded-md bg-amber-500 px-4 py-2 text-sm font-medium text-white hover:bg-amber-500/90 disabled:opacity-50"
                            disabled={processing}
                        >
                            {processing ? 'Creating...' : 'Create Unit'}
                        </button>
                    </DialogFooter>
                </form>
            </DialogContent>
        </Dialog>
    );
}

export default Create;
