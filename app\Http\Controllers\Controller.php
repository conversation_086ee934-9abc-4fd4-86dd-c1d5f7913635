<?php

namespace App\Http\Controllers;

abstract class Controller
{
    //
    protected $data = [];



    // Magic method to set a property
    public function __set($name, $value)
    {
        $this->data[$name] = $value;
    }

    // Magic method to get a property
    public function __get($name)
    {
        return $this->data[$name] ?? null; // return null if the key doesn't exist
    }

    // Magic method to check if a property is set
    public function __isset($name)
    {
        return isset($this->data[$name]);
    }
}