import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { DateRangePicker } from '@/components/ui/date-range-picker';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head } from '@inertiajs/react';
import axios from 'axios';
import { addDays, format } from 'date-fns';
import {
    Activity,
    AlertTriangle,
    BookOpen,
    CheckCircle,
    Download,
    FileText,
    GraduationCap,
    Info,
    RefreshCw,
    TrendingDown,
    TrendingUp,
    UserCheck,
    Users,
} from 'lucide-react';
import { useCallback, useEffect, useState } from 'react';
import { <PERSON>, <PERSON>hart, CartesianGrid, Cell, Legend, Line, LineChart, Pie, <PERSON>hart, ResponsiveContainer, Tooltip, XAxis, YAxis } from 'recharts';

// Additional imports for enhanced dashboard

interface DashboardProps {
    classes: Array<{
        id: number;
        name: string;
    }>;
    subjects: Array<{
        id: number;
        name: string;
        class_id: number;
    }>;
    assessmentStats: {
        total: number;
        activeToday: number;
        avgScore: number;
        completionRate: number;
    };
    studentStats: {
        total: number;
        activeToday: number;
        averagePerformance: number;
    };
    contentStats: {
        totalTopics: number;
        totalUnits: number;
        totalContentBlocks: number;
    };
    lessonStats: {
        totalLessons: number;
        publishedLessons: number;
        completionRate: number;
        averageProgress: number;
    };
    teacherStats: {
        totalTeachers: number;
        activeToday: number;
        avgContentCreated: number;
        engagementRate: number;
    };
    performanceBySubject: Array<{
        subject: string;
        avgScore: number;
    }>;
    assessmentCompletionData: Array<{
        date: string;
        completed: number;
        started: number;
    }>;
    contentTypeDistribution: Array<{
        type: string;
        count: number;
    }>;
    studentProgressData: Array<{
        class: string;
        totalStudents: number;
        averageProgress: number;
        completedTopics: number;
    }>;
    lessonCompletionTrends: Array<{
        date: string;
        completions: number;
        dayOfWeek: string;
    }>;
    classPerformanceComparison: Array<{
        class: string;
        averageScore: number;
        totalAssessments: number;
        completionRate: number;
    }>;
    recentAssessments: Array<{
        id: number;
        title: string;
        subject: string;
        class: string;
        submissions: number;
        avgScore: number;
        date: string;
    }>;
    recentActivities: Array<{
        type: string;
        title: string;
        description: string;
        score?: number;
        timestamp: string;
        icon: string;
    }>;
    alerts: Array<{
        type: string;
        title: string;
        message: string;
        action: string;
        priority: string;
    }>;
}

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
];

// Enhanced color palettes for charts and visualizations
const CHART_COLORS = {
    // Primary palette - vibrant and accessible
    primary: ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#06B6D4', '#84CC16', '#F97316'],

    // Performance metrics - warm colors
    performance: ['#EF4444', '#F97316', '#F59E0B', '#84CC16', '#10B981'],

    // Completion data - cool colors
    completion: ['#3B82F6', '#06B6D4', '#8B5CF6', '#6366F1', '#0EA5E9'],

    // Educational theme colors
    educational: {
        assessments: '#3B82F6', // Blue - knowledge/testing
        students: '#10B981', // Green - growth/progress
        content: '#8B5CF6', // Purple - creativity/learning
        lessons: '#F59E0B', // Amber - enlightenment/teaching
        teachers: '#EF4444', // Red - passion/dedication
    },

    // Status colors
    status: {
        success: '#10B981',
        warning: '#F59E0B',
        error: '#EF4444',
        info: '#3B82F6',
    },
};

// Note: CHART_COLORS.primary is used directly in pie chart components

export default function Dashboard({
    classes,
    subjects,
    assessmentStats,
    studentStats,
    contentStats,
    lessonStats,
    teacherStats,
    performanceBySubject,
    assessmentCompletionData,
    contentTypeDistribution,
    studentProgressData,
    lessonCompletionTrends,
    classPerformanceComparison,
    recentAssessments: initialAssessments,
    recentActivities,
    alerts,
}: DashboardProps) {
    // Filter states
    const [selectedClass, setSelectedClass] = useState<string>('all');
    const [selectedSubject, setSelectedSubject] = useState<string>('all');
    const [dateRange, setDateRange] = useState({
        from: addDays(new Date(), -30),
        to: new Date(),
    });
    const [filteredSubjects, setFilteredSubjects] = useState(subjects);
    const [recentAssessments, setRecentAssessments] = useState(initialAssessments);
    const [isLoading, setIsLoading] = useState(false);
    const [refreshing, setRefreshing] = useState(false);

    // Handle form submission for filtering data
    const submitFilter = useCallback(async () => {
        try {
            setIsLoading(true);
            const response = await axios.post('/dashboard/filter', {
                class_id: selectedClass,
                subject_id: selectedSubject,
                date_from: format(dateRange.from, 'yyyy-MM-dd'),
                date_to: format(dateRange.to, 'yyyy-MM-dd'),
            });

            if (response.data.success) {
                setRecentAssessments(response.data.data.recentAssessments);
            }
        } catch (error) {
            console.error('Error filtering dashboard data:', error);
        } finally {
            setIsLoading(false);
        }
    }, [selectedClass, selectedSubject, dateRange.from, dateRange.to]);

    // Update filtered subjects when class changes
    useEffect(() => {
        if (selectedClass === 'all') {
            setFilteredSubjects(subjects);
        } else {
            setFilteredSubjects(subjects.filter((subject) => subject.class_id === Number(selectedClass)));
        }
    }, [selectedClass, subjects]);

    // Filter data when filters change
    useEffect(() => {
        submitFilter();
    }, [selectedClass, selectedSubject, submitFilter]);

    // Format percentage for display
    const formatPercentage = (value: number): string => `${value}%`;

    // Determine if a metric is improving or declining
    const getMetricTrend = (current: number, threshold: number): boolean => {
        return current > threshold;
    };

    // Custom tooltip formatter for charts
    const customTooltipFormatter = (value: number, name: string): [string, string] => {
        if (name === 'avgScore') {
            return [`${value}%`, 'Average Score'];
        }
        if (name === 'completed') {
            return [`${value}`, 'Completed'];
        }
        if (name === 'started') {
            return [`${value}`, 'Started'];
        }
        if (name === 'completions') {
            return [`${value}`, 'Completions'];
        }
        if (name === 'averageProgress') {
            return [`${value}%`, 'Average Progress'];
        }
        return [`${value}`, name];
    };

    // Handle refresh
    const handleRefresh = async () => {
        setRefreshing(true);
        try {
            window.location.reload();
        } catch (error) {
            console.error('Error refreshing dashboard:', error);
        } finally {
            setRefreshing(false);
        }
    };

    // Handle export
    const handleExport = () => {
        // This would typically trigger a download of dashboard data
        console.log('Exporting dashboard data...');
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Dashboard" />
            <div className="flex flex-col gap-6 p-6">
                {/* Header with Filters and Actions */}
                <div className="bg-card flex flex-col gap-4 rounded-xl border p-4 shadow">
                    <div className="flex flex-col gap-4 lg:flex-row lg:items-center lg:justify-between">
                        <div className="flex-1 space-y-1">
                            <h2 className="text-lg font-semibold">Dashboard Overview</h2>
                            <p className="text-muted-foreground text-sm">View and analyze assessment and performance data</p>
                        </div>
                        <div className="flex flex-col gap-3 sm:flex-row">
                            <Button variant="outline" size="sm" onClick={handleRefresh} disabled={refreshing} className="flex items-center gap-2">
                                <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
                                Refresh
                            </Button>
                            <Button variant="outline" size="sm" onClick={handleExport} className="flex items-center gap-2">
                                <Download className="h-4 w-4" />
                                Export
                            </Button>
                        </div>
                    </div>

                    {/* Filters Row */}
                    <div className="flex flex-col gap-3 sm:flex-row sm:items-center">
                        <DateRangePicker value={dateRange} onChange={setDateRange} className="w-full sm:w-auto" />
                        <Select
                            value={selectedClass}
                            onValueChange={(value) => {
                                setSelectedClass(value);
                                if (value !== 'all') {
                                    setSelectedSubject('all');
                                }
                            }}
                        >
                            <SelectTrigger className="w-full sm:w-[180px]">
                                <SelectValue placeholder="Select Class" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="all">All Classes</SelectItem>
                                {classes.map((classItem) => (
                                    <SelectItem key={classItem.id} value={String(classItem.id)}>
                                        {classItem.name}
                                    </SelectItem>
                                ))}
                            </SelectContent>
                        </Select>
                        <Select
                            value={selectedSubject}
                            onValueChange={(value) => {
                                setSelectedSubject(value);
                                setTimeout(submitFilter, 10);
                            }}
                        >
                            <SelectTrigger className="w-full sm:w-[180px]">
                                <SelectValue placeholder="Select Subject" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="all">All Subjects</SelectItem>
                                {filteredSubjects.map((subject) => (
                                    <SelectItem key={subject.id} value={String(subject.id)}>
                                        {subject.name}
                                    </SelectItem>
                                ))}
                            </SelectContent>
                        </Select>
                    </div>
                </div>

                {/* Enhanced Alerts Section */}
                {alerts && alerts.length > 0 && (
                    <div className="space-y-3">
                        {alerts.map((alert, index) => (
                            <div
                                key={index}
                                className={`group relative overflow-hidden rounded-lg border p-4 transition-all duration-300 hover:shadow-md ${
                                    alert.type === 'warning'
                                        ? 'border-amber-200 bg-gradient-to-r from-amber-50 to-amber-100/50 hover:shadow-amber-100'
                                        : alert.type === 'error'
                                          ? 'border-red-200 bg-gradient-to-r from-red-50 to-red-100/50 hover:shadow-red-100'
                                          : 'border-blue-200 bg-gradient-to-r from-blue-50 to-blue-100/50 hover:shadow-blue-100'
                                }`}
                            >
                                <div className="flex items-center gap-4">
                                    <div
                                        className={`rounded-full p-2 ${
                                            alert.type === 'warning' ? 'bg-amber-500/10' : alert.type === 'error' ? 'bg-red-500/10' : 'bg-blue-500/10'
                                        }`}
                                    >
                                        {alert.type === 'warning' ? (
                                            <AlertTriangle className="h-5 w-5 text-amber-600" />
                                        ) : alert.type === 'error' ? (
                                            <AlertTriangle className="h-5 w-5 text-red-600" />
                                        ) : (
                                            <Info className="h-5 w-5 text-blue-600" />
                                        )}
                                    </div>
                                    <div className="flex-1">
                                        <h4
                                            className={`font-semibold ${
                                                alert.type === 'warning'
                                                    ? 'text-amber-900'
                                                    : alert.type === 'error'
                                                      ? 'text-red-900'
                                                      : 'text-blue-900'
                                            }`}
                                        >
                                            {alert.title}
                                        </h4>
                                        <p
                                            className={`text-sm ${
                                                alert.type === 'warning'
                                                    ? 'text-amber-700'
                                                    : alert.type === 'error'
                                                      ? 'text-red-700'
                                                      : 'text-blue-700'
                                            }`}
                                        >
                                            {alert.message}
                                        </p>
                                    </div>
                                    <Button
                                        variant="outline"
                                        size="sm"
                                        className={`transition-colors duration-300 ${
                                            alert.type === 'warning'
                                                ? 'border-amber-300 text-amber-700 hover:bg-amber-100'
                                                : alert.type === 'error'
                                                  ? 'border-red-300 text-red-700 hover:bg-red-100'
                                                  : 'border-blue-300 text-blue-700 hover:bg-blue-100'
                                        }`}
                                    >
                                        {alert.action}
                                    </Button>
                                </div>
                                {/* Priority indicator */}
                                {alert.priority === 'high' && (
                                    <div className="absolute top-0 left-0 h-full w-1 bg-gradient-to-b from-red-500 to-red-600" />
                                )}
                            </div>
                        ))}
                    </div>
                )}

                {/* Stats Overview */}
                <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-5">
                    {/* Assessments Card - Blue Theme */}
                    <Card className="group relative overflow-hidden border-blue-200 bg-gradient-to-br from-blue-50 to-blue-100/50 transition-all duration-300 hover:shadow-lg hover:shadow-blue-100">
                        <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-blue-600/10 opacity-0 transition-opacity duration-300 group-hover:opacity-100" />
                        <CardHeader className="relative flex flex-row items-center justify-between pb-2">
                            <CardTitle className="text-sm font-medium text-blue-900">Assessments</CardTitle>
                            <div className="rounded-full bg-blue-500/10 p-2 transition-colors duration-300 group-hover:bg-blue-500/20">
                                <FileText className="h-4 w-4 text-blue-600" />
                            </div>
                        </CardHeader>
                        <CardContent className="relative">
                            <div className="flex items-baseline justify-between">
                                <div className="text-2xl font-bold text-blue-900">{assessmentStats.total}</div>
                                <div className="flex items-center gap-1 text-xs text-blue-700">
                                    <span className="font-medium">Today:</span>
                                    <span className="rounded-full bg-blue-500/20 px-2 py-0.5 font-semibold">{assessmentStats.activeToday}</span>
                                </div>
                            </div>
                            <div className="mt-4 grid grid-cols-2 gap-4">
                                <div className="flex flex-col">
                                    <span className="text-xs text-blue-600">Avg. Score</span>
                                    <div className="flex items-center gap-1">
                                        <span className="text-lg font-bold text-blue-900">{formatPercentage(assessmentStats.avgScore)}</span>
                                        {getMetricTrend(assessmentStats.avgScore, 70) ? (
                                            <TrendingUp className="h-4 w-4 text-emerald-500" />
                                        ) : (
                                            <TrendingDown className="h-4 w-4 text-red-500" />
                                        )}
                                    </div>
                                </div>
                                <div className="flex flex-col">
                                    <span className="text-xs text-blue-600">Completion Rate</span>
                                    <div className="flex items-center gap-1">
                                        <span className="text-lg font-bold text-blue-900">{formatPercentage(assessmentStats.completionRate)}</span>
                                        {getMetricTrend(assessmentStats.completionRate, 65) ? (
                                            <TrendingUp className="h-4 w-4 text-emerald-500" />
                                        ) : (
                                            <TrendingDown className="h-4 w-4 text-red-500" />
                                        )}
                                    </div>
                                </div>
                            </div>
                            {/* Progress bar for completion rate */}
                            <div className="mt-3">
                                <div className="h-2 w-full rounded-full bg-blue-200">
                                    <div
                                        className="h-2 rounded-full bg-gradient-to-r from-blue-500 to-blue-600 transition-all duration-500"
                                        style={{ width: `${assessmentStats.completionRate}%` }}
                                    />
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Students Card - Green Theme */}
                    <Card className="group relative overflow-hidden border-emerald-200 bg-gradient-to-br from-emerald-50 to-emerald-100/50 transition-all duration-300 hover:shadow-lg hover:shadow-emerald-100">
                        <div className="absolute inset-0 bg-gradient-to-br from-emerald-500/5 to-emerald-600/10 opacity-0 transition-opacity duration-300 group-hover:opacity-100" />
                        <CardHeader className="relative flex flex-row items-center justify-between pb-2">
                            <CardTitle className="text-sm font-medium text-emerald-900">Students</CardTitle>
                            <div className="rounded-full bg-emerald-500/10 p-2 transition-colors duration-300 group-hover:bg-emerald-500/20">
                                <Users className="h-4 w-4 text-emerald-600" />
                            </div>
                        </CardHeader>
                        <CardContent className="relative">
                            <div className="flex items-baseline justify-between">
                                <div className="text-2xl font-bold text-emerald-900">{studentStats.total}</div>
                                <div className="flex items-center gap-1 text-xs text-emerald-700">
                                    <span className="font-medium">Active Today:</span>
                                    <span className="rounded-full bg-emerald-500/20 px-2 py-0.5 font-semibold">{studentStats.activeToday}</span>
                                </div>
                            </div>
                            <div className="mt-4 flex items-baseline justify-between">
                                <div className="flex flex-col">
                                    <span className="text-xs text-emerald-600">Avg. Performance</span>
                                    <div className="flex items-center gap-1">
                                        <span className="text-lg font-bold text-emerald-900">
                                            {formatPercentage(studentStats.averagePerformance)}
                                        </span>
                                        {getMetricTrend(studentStats.averagePerformance, 75) ? (
                                            <TrendingUp className="h-4 w-4 text-emerald-500" />
                                        ) : (
                                            <TrendingDown className="h-4 w-4 text-red-500" />
                                        )}
                                    </div>
                                </div>
                                <div className="flex flex-col items-end">
                                    <div className="flex h-8 w-8 items-center justify-center rounded-full bg-emerald-500/10 transition-colors duration-300 group-hover:bg-emerald-500/20">
                                        <Activity className="h-4 w-4 text-emerald-600" />
                                    </div>
                                </div>
                            </div>
                            {/* Progress bar for performance */}
                            <div className="mt-3">
                                <div className="h-2 w-full rounded-full bg-emerald-200">
                                    <div
                                        className="h-2 rounded-full bg-gradient-to-r from-emerald-500 to-emerald-600 transition-all duration-500"
                                        style={{ width: `${studentStats.averagePerformance}%` }}
                                    />
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Content Card - Purple Theme */}
                    <Card className="group relative overflow-hidden border-purple-200 bg-gradient-to-br from-purple-50 to-purple-100/50 transition-all duration-300 hover:shadow-lg hover:shadow-purple-100">
                        <div className="absolute inset-0 bg-gradient-to-br from-purple-500/5 to-purple-600/10 opacity-0 transition-opacity duration-300 group-hover:opacity-100" />
                        <CardHeader className="relative flex flex-row items-center justify-between pb-2">
                            <CardTitle className="text-sm font-medium text-purple-900">Content</CardTitle>
                            <div className="rounded-full bg-purple-500/10 p-2 transition-colors duration-300 group-hover:bg-purple-500/20">
                                <BookOpen className="h-4 w-4 text-purple-600" />
                            </div>
                        </CardHeader>
                        <CardContent className="relative">
                            <div className="flex items-baseline justify-between">
                                <div className="text-2xl font-bold text-purple-900">{contentStats.totalContentBlocks}</div>
                                <div className="flex items-center gap-1 text-xs text-purple-700">
                                    <span className="rounded-full bg-purple-500/20 px-2 py-0.5 font-semibold">Content Blocks</span>
                                </div>
                            </div>
                            <div className="mt-4 grid grid-cols-2 gap-4">
                                <div className="flex flex-col">
                                    <span className="text-xs text-purple-600">Topics</span>
                                    <span className="text-lg font-bold text-purple-900">{contentStats.totalTopics}</span>
                                </div>
                                <div className="flex flex-col">
                                    <span className="text-xs text-purple-600">Units</span>
                                    <span className="text-lg font-bold text-purple-900">{contentStats.totalUnits}</span>
                                </div>
                            </div>
                            {/* Content distribution visualization */}
                            <div className="mt-3 flex gap-1">
                                <div className="h-2 flex-1 rounded-full bg-purple-300" title="Topics">
                                    <div className="h-2 w-full rounded-full bg-gradient-to-r from-purple-500 to-purple-600" />
                                </div>
                                <div className="h-2 flex-1 rounded-full bg-purple-200" title="Units">
                                    <div className="h-2 w-3/4 rounded-full bg-gradient-to-r from-purple-400 to-purple-500" />
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Lessons Card - Amber Theme */}
                    <Card className="group relative overflow-hidden border-amber-200 bg-gradient-to-br from-amber-50 to-amber-100/50 transition-all duration-300 hover:shadow-lg hover:shadow-amber-100">
                        <div className="absolute inset-0 bg-gradient-to-br from-amber-500/5 to-amber-600/10 opacity-0 transition-opacity duration-300 group-hover:opacity-100" />
                        <CardHeader className="relative flex flex-row items-center justify-between pb-2">
                            <CardTitle className="text-sm font-medium text-amber-900">Lessons</CardTitle>
                            <div className="rounded-full bg-amber-500/10 p-2 transition-colors duration-300 group-hover:bg-amber-500/20">
                                <GraduationCap className="h-4 w-4 text-amber-600" />
                            </div>
                        </CardHeader>
                        <CardContent className="relative">
                            <div className="flex items-baseline justify-between">
                                <div className="text-2xl font-bold text-amber-900">{lessonStats.totalLessons}</div>
                                <div className="flex items-center gap-1 text-xs text-amber-700">
                                    <span className="font-medium">Published:</span>
                                    <span className="rounded-full bg-amber-500/20 px-2 py-0.5 font-semibold">{lessonStats.publishedLessons}</span>
                                </div>
                            </div>
                            <div className="mt-4 grid grid-cols-2 gap-4">
                                <div className="flex flex-col">
                                    <span className="text-xs text-amber-600">Completion Rate</span>
                                    <div className="flex items-center gap-1">
                                        <span className="text-lg font-bold text-amber-900">{formatPercentage(lessonStats.completionRate)}</span>
                                        {getMetricTrend(lessonStats.completionRate, 70) ? (
                                            <TrendingUp className="h-4 w-4 text-emerald-500" />
                                        ) : (
                                            <TrendingDown className="h-4 w-4 text-red-500" />
                                        )}
                                    </div>
                                </div>
                                <div className="flex flex-col">
                                    <span className="text-xs text-amber-600">Avg. Progress</span>
                                    <span className="text-lg font-bold text-amber-900">{formatPercentage(lessonStats.averageProgress)}</span>
                                </div>
                            </div>
                            {/* Progress bar for completion rate */}
                            <div className="mt-3">
                                <div className="h-2 w-full rounded-full bg-amber-200">
                                    <div
                                        className="h-2 rounded-full bg-gradient-to-r from-amber-500 to-amber-600 transition-all duration-500"
                                        style={{ width: `${lessonStats.completionRate}%` }}
                                    />
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Teachers Card - Red Theme */}
                    <Card className="group relative overflow-hidden border-red-200 bg-gradient-to-br from-red-50 to-red-100/50 transition-all duration-300 hover:shadow-lg hover:shadow-red-100">
                        <div className="absolute inset-0 bg-gradient-to-br from-red-500/5 to-red-600/10 opacity-0 transition-opacity duration-300 group-hover:opacity-100" />
                        <CardHeader className="relative flex flex-row items-center justify-between pb-2">
                            <CardTitle className="text-sm font-medium text-red-900">Teachers</CardTitle>
                            <div className="rounded-full bg-red-500/10 p-2 transition-colors duration-300 group-hover:bg-red-500/20">
                                <UserCheck className="h-4 w-4 text-red-600" />
                            </div>
                        </CardHeader>
                        <CardContent className="relative">
                            <div className="flex items-baseline justify-between">
                                <div className="text-2xl font-bold text-red-900">{teacherStats.totalTeachers}</div>
                                <div className="flex items-center gap-1 text-xs text-red-700">
                                    <span className="font-medium">Active Today:</span>
                                    <span className="rounded-full bg-red-500/20 px-2 py-0.5 font-semibold">{teacherStats.activeToday}</span>
                                </div>
                            </div>
                            <div className="mt-4 grid grid-cols-2 gap-4">
                                <div className="flex flex-col">
                                    <span className="text-xs text-red-600">Avg. Content</span>
                                    <span className="text-lg font-bold text-red-900">{teacherStats.avgContentCreated}</span>
                                </div>
                                <div className="flex flex-col">
                                    <span className="text-xs text-red-600">Engagement</span>
                                    <div className="flex items-center gap-1">
                                        <span className="text-lg font-bold text-red-900">{formatPercentage(teacherStats.engagementRate)}</span>
                                        {getMetricTrend(teacherStats.engagementRate, 60) ? (
                                            <TrendingUp className="h-4 w-4 text-emerald-500" />
                                        ) : (
                                            <TrendingDown className="h-4 w-4 text-red-500" />
                                        )}
                                    </div>
                                </div>
                            </div>
                            {/* Progress bar for engagement */}
                            <div className="mt-3">
                                <div className="h-2 w-full rounded-full bg-red-200">
                                    <div
                                        className="h-2 rounded-full bg-gradient-to-r from-red-500 to-red-600 transition-all duration-500"
                                        style={{ width: `${teacherStats.engagementRate}%` }}
                                    />
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Charts Section */}
                <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                    <Card className="col-span-full lg:col-span-2">
                        <CardHeader>
                            <CardTitle>Assessment Completion Trends</CardTitle>
                            <CardDescription>Weekly overview of started vs completed assessments</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <ResponsiveContainer width="100%" height={270}>
                                <BarChart data={assessmentCompletionData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
                                    <CartesianGrid strokeDasharray="3 3" stroke="#f1f5f9" />
                                    <XAxis dataKey="date" stroke="#64748b" fontSize={12} />
                                    <YAxis stroke="#64748b" fontSize={12} />
                                    <Tooltip
                                        formatter={customTooltipFormatter}
                                        contentStyle={{
                                            backgroundColor: '#ffffff',
                                            border: '1px solid #e2e8f0',
                                            borderRadius: '8px',
                                            boxShadow: '0 4px 6px -1px rgb(0 0 0 / 0.1)',
                                        }}
                                    />
                                    <Legend />
                                    <Bar dataKey="started" fill={CHART_COLORS.completion[0]} name="Started" radius={[4, 4, 0, 0]} />
                                    <Bar dataKey="completed" fill={CHART_COLORS.completion[1]} name="Completed" radius={[4, 4, 0, 0]} />
                                </BarChart>
                            </ResponsiveContainer>
                        </CardContent>
                    </Card>

                    <Card className="lg:row-span-2">
                        <CardHeader>
                            <CardTitle>Content Type Distribution</CardTitle>
                            <CardDescription>Breakdown of content by type</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <ResponsiveContainer width="100%" height={270}>
                                <PieChart>
                                    <Pie
                                        data={contentTypeDistribution}
                                        cx="50%"
                                        cy="50%"
                                        labelLine={false}
                                        label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                                        outerRadius={80}
                                        fill="#8884d8"
                                        dataKey="count"
                                        nameKey="type"
                                        stroke="#ffffff"
                                        strokeWidth={2}
                                    >
                                        {contentTypeDistribution.map((_, index) => (
                                            <Cell key={`cell-${index}`} fill={CHART_COLORS.primary[index % CHART_COLORS.primary.length]} />
                                        ))}
                                    </Pie>
                                    <Tooltip
                                        formatter={(value: number) => [`${value} blocks`, 'Count']}
                                        contentStyle={{
                                            backgroundColor: '#ffffff',
                                            border: '1px solid #e2e8f0',
                                            borderRadius: '8px',
                                            boxShadow: '0 4px 6px -1px rgb(0 0 0 / 0.1)',
                                        }}
                                    />
                                    <Legend />
                                </PieChart>
                            </ResponsiveContainer>
                        </CardContent>
                    </Card>

                    <Card className="col-span-full lg:col-span-2">
                        <CardHeader>
                            <CardTitle>Subject Performance Analysis</CardTitle>
                            <CardDescription>Average scores across different subjects</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <ResponsiveContainer width="100%" height={270}>
                                <LineChart data={performanceBySubject} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
                                    <CartesianGrid strokeDasharray="3 3" stroke="#f1f5f9" />
                                    <XAxis dataKey="subject" stroke="#64748b" fontSize={12} />
                                    <YAxis domain={[0, 100]} stroke="#64748b" fontSize={12} />
                                    <Tooltip
                                        formatter={(value: number) => [`${value}%`, 'Average Score']}
                                        contentStyle={{
                                            backgroundColor: '#ffffff',
                                            border: '1px solid #e2e8f0',
                                            borderRadius: '8px',
                                            boxShadow: '0 4px 6px -1px rgb(0 0 0 / 0.1)',
                                        }}
                                    />
                                    <Legend />
                                    <Line
                                        type="monotone"
                                        dataKey="avgScore"
                                        stroke={CHART_COLORS.performance[4]}
                                        strokeWidth={3}
                                        name="Average Score"
                                        activeDot={{ r: 6, fill: CHART_COLORS.performance[4], stroke: '#ffffff', strokeWidth: 2 }}
                                        dot={{ fill: CHART_COLORS.performance[4], strokeWidth: 2, stroke: '#ffffff', r: 4 }}
                                    />
                                </LineChart>
                            </ResponsiveContainer>
                        </CardContent>
                    </Card>
                </div>

                {/* Additional Charts Section */}
                <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                    <Card className="col-span-full lg:col-span-2">
                        <CardHeader>
                            <CardTitle>Lesson Completion Trends</CardTitle>
                            <CardDescription>Daily lesson completion activity over the past week</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <ResponsiveContainer width="100%" height={270}>
                                <BarChart data={lessonCompletionTrends} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
                                    <CartesianGrid strokeDasharray="3 3" stroke="#f1f5f9" />
                                    <XAxis dataKey="date" stroke="#64748b" fontSize={12} />
                                    <YAxis stroke="#64748b" fontSize={12} />
                                    <Tooltip
                                        formatter={customTooltipFormatter}
                                        contentStyle={{
                                            backgroundColor: '#ffffff',
                                            border: '1px solid #e2e8f0',
                                            borderRadius: '8px',
                                            boxShadow: '0 4px 6px -1px rgb(0 0 0 / 0.1)',
                                        }}
                                    />
                                    <Legend />
                                    <Bar dataKey="completions" fill={CHART_COLORS.educational.lessons} name="Completions" radius={[4, 4, 0, 0]} />
                                </BarChart>
                            </ResponsiveContainer>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader>
                            <CardTitle>Student Progress by Class</CardTitle>
                            <CardDescription>Average progress across different classes</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <ResponsiveContainer width="100%" height={270}>
                                <BarChart data={studentProgressData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
                                    <CartesianGrid strokeDasharray="3 3" stroke="#f1f5f9" />
                                    <XAxis dataKey="class" stroke="#64748b" fontSize={12} />
                                    <YAxis domain={[0, 100]} stroke="#64748b" fontSize={12} />
                                    <Tooltip
                                        formatter={customTooltipFormatter}
                                        contentStyle={{
                                            backgroundColor: '#ffffff',
                                            border: '1px solid #e2e8f0',
                                            borderRadius: '8px',
                                            boxShadow: '0 4px 6px -1px rgb(0 0 0 / 0.1)',
                                        }}
                                    />
                                    <Bar
                                        dataKey="averageProgress"
                                        fill={CHART_COLORS.educational.students}
                                        name="Average Progress %"
                                        radius={[4, 4, 0, 0]}
                                    />
                                </BarChart>
                            </ResponsiveContainer>
                        </CardContent>
                    </Card>

                    <Card className="col-span-full lg:col-span-3">
                        <CardHeader>
                            <CardTitle>Class Performance Comparison</CardTitle>
                            <CardDescription>Comprehensive comparison of class performance metrics</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <ResponsiveContainer width="100%" height={270}>
                                <BarChart data={classPerformanceComparison} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
                                    <CartesianGrid strokeDasharray="3 3" stroke="#f1f5f9" />
                                    <XAxis dataKey="class" stroke="#64748b" fontSize={12} />
                                    <YAxis stroke="#64748b" fontSize={12} />
                                    <Tooltip
                                        formatter={(value: number, name: string) => {
                                            if (name === 'averageScore' || name === 'completionRate') {
                                                return [`${value}%`, name === 'averageScore' ? 'Average Score' : 'Completion Rate'];
                                            }
                                            return [`${value}`, 'Total Assessments'];
                                        }}
                                        contentStyle={{
                                            backgroundColor: '#ffffff',
                                            border: '1px solid #e2e8f0',
                                            borderRadius: '8px',
                                            boxShadow: '0 4px 6px -1px rgb(0 0 0 / 0.1)',
                                        }}
                                    />
                                    <Legend />
                                    <Bar dataKey="averageScore" fill={CHART_COLORS.performance[2]} name="Average Score %" radius={[4, 4, 0, 0]} />
                                    <Bar dataKey="completionRate" fill={CHART_COLORS.performance[4]} name="Completion Rate %" radius={[4, 4, 0, 0]} />
                                </BarChart>
                            </ResponsiveContainer>
                        </CardContent>
                    </Card>
                </div>

                {/* Activity Feed and Recent Assessments */}
                <div className="grid gap-6 lg:grid-cols-2">
                    <Card>
                        <CardHeader>
                            <CardTitle>Recent Activity</CardTitle>
                            <CardDescription>Latest student activities and achievements</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-4">
                                {recentActivities && recentActivities.length > 0 ? (
                                    recentActivities.map((activity, index) => (
                                        <div
                                            key={index}
                                            className="group flex items-center gap-3 rounded-lg border border-gray-200 bg-gradient-to-r from-gray-50 to-white p-3 transition-all duration-300 hover:shadow-md hover:shadow-gray-100"
                                        >
                                            <div
                                                className={`flex h-10 w-10 items-center justify-center rounded-full transition-colors duration-300 ${
                                                    activity.icon === 'FileText'
                                                        ? 'bg-blue-100 group-hover:bg-blue-200'
                                                        : 'bg-emerald-100 group-hover:bg-emerald-200'
                                                }`}
                                            >
                                                {activity.icon === 'FileText' ? (
                                                    <FileText className="h-5 w-5 text-blue-600" />
                                                ) : (
                                                    <CheckCircle className="h-5 w-5 text-emerald-600" />
                                                )}
                                            </div>
                                            <div className="flex-1">
                                                <h4 className="text-sm font-semibold text-gray-900">{activity.title}</h4>
                                                <p className="text-xs text-gray-600">{activity.description}</p>
                                                {activity.score && (
                                                    <div className="mt-1">
                                                        <span
                                                            className={`inline-flex items-center rounded-full px-2 py-0.5 text-xs font-medium ${
                                                                activity.score >= 80
                                                                    ? 'bg-emerald-100 text-emerald-800'
                                                                    : activity.score >= 70
                                                                      ? 'bg-amber-100 text-amber-800'
                                                                      : 'bg-red-100 text-red-800'
                                                            }`}
                                                        >
                                                            Score: {activity.score}%
                                                        </span>
                                                    </div>
                                                )}
                                            </div>
                                            <div className="text-xs font-medium text-gray-500">
                                                {format(new Date(activity.timestamp), 'MMM d, HH:mm')}
                                            </div>
                                        </div>
                                    ))
                                ) : (
                                    <div className="flex flex-col items-center justify-center p-8 text-center">
                                        <div className="mb-3 rounded-full bg-gray-100 p-3">
                                            <Activity className="h-6 w-6 text-gray-400" />
                                        </div>
                                        <p className="font-medium text-gray-500">No recent activities</p>
                                        <p className="mt-1 text-xs text-gray-400">Activity will appear here as students engage with content</p>
                                    </div>
                                )}
                            </div>
                        </CardContent>
                    </Card>

                    {/* Recent Assessments */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Recent Assessments</CardTitle>
                            <CardDescription>Overview of the latest assessment activities</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <Tabs defaultValue="all">
                                <TabsList className="mb-4">
                                    <TabsTrigger value="all">All</TabsTrigger>
                                    <TabsTrigger value="high">High Performance</TabsTrigger>
                                    <TabsTrigger value="low">Needs Attention</TabsTrigger>
                                </TabsList>
                                <TabsContent value="all" className="space-y-0">
                                    <div className="rounded-md border">
                                        <div className="bg-muted/50 grid grid-cols-6 p-4 text-sm font-medium">
                                            <div>Assessment</div>
                                            <div>Subject</div>
                                            <div>Class</div>
                                            <div className="text-center">Submissions</div>
                                            <div className="text-center">Avg. Score</div>
                                            <div className="text-right">Date</div>
                                        </div>
                                        <div className="divide-y">
                                            {isLoading ? (
                                                <div className="p-8 text-center">Loading...</div>
                                            ) : recentAssessments.length > 0 ? (
                                                recentAssessments.map((assessment) => (
                                                    <div key={assessment.id} className="grid grid-cols-6 items-center p-4">
                                                        <div className="font-medium">{assessment.title}</div>
                                                        <div className="text-sm">{assessment.subject}</div>
                                                        <div className="text-sm">{assessment.class}</div>
                                                        <div className="text-center">{assessment.submissions}</div>
                                                        <div className="flex items-center justify-center">
                                                            <span
                                                                className={`rounded-full px-3 py-1 text-xs font-semibold shadow-sm ${
                                                                    assessment.avgScore >= 80
                                                                        ? 'border border-emerald-200 bg-emerald-100 text-emerald-800'
                                                                        : assessment.avgScore >= 70
                                                                          ? 'border border-amber-200 bg-amber-100 text-amber-800'
                                                                          : 'border border-red-200 bg-red-100 text-red-800'
                                                                }`}
                                                            >
                                                                {formatPercentage(assessment.avgScore)}
                                                            </span>
                                                        </div>
                                                        <div className="text-muted-foreground text-right text-sm">
                                                            {format(new Date(assessment.date), 'MMM d, yyyy')}
                                                        </div>
                                                    </div>
                                                ))
                                            ) : (
                                                <div className="p-8 text-center">No assessments found</div>
                                            )}
                                        </div>
                                    </div>
                                </TabsContent>

                                <TabsContent value="high" className="space-y-0">
                                    <div className="rounded-md border">
                                        <div className="bg-muted/50 grid grid-cols-6 p-4 text-sm font-medium">
                                            <div>Assessment</div>
                                            <div>Subject</div>
                                            <div>Class</div>
                                            <div className="text-center">Submissions</div>
                                            <div className="text-center">Avg. Score</div>
                                            <div className="text-right">Date</div>
                                        </div>
                                        <div className="divide-y">
                                            {recentAssessments
                                                .filter((a) => a.avgScore >= 80)
                                                .map((assessment) => (
                                                    <div key={assessment.id} className="grid grid-cols-6 items-center p-4">
                                                        <div className="font-medium">{assessment.title}</div>
                                                        <div className="text-sm">{assessment.subject}</div>
                                                        <div className="text-sm">{assessment.class}</div>
                                                        <div className="text-center">{assessment.submissions}</div>
                                                        <div className="flex items-center justify-center">
                                                            <span className="rounded-full border border-emerald-200 bg-emerald-100 px-3 py-1 text-xs font-semibold text-emerald-800 shadow-sm">
                                                                {formatPercentage(assessment.avgScore)}
                                                            </span>
                                                        </div>
                                                        <div className="text-muted-foreground text-right text-sm">
                                                            {format(new Date(assessment.date), 'MMM d, yyyy')}
                                                        </div>
                                                    </div>
                                                ))}
                                        </div>
                                    </div>
                                </TabsContent>

                                <TabsContent value="low" className="space-y-0">
                                    <div className="rounded-md border">
                                        <div className="bg-muted/50 grid grid-cols-6 p-4 text-sm font-medium">
                                            <div>Assessment</div>
                                            <div>Subject</div>
                                            <div>Class</div>
                                            <div className="text-center">Submissions</div>
                                            <div className="text-center">Avg. Score</div>
                                            <div className="text-right">Date</div>
                                        </div>
                                        <div className="divide-y">
                                            {recentAssessments
                                                .filter((a) => a.avgScore < 70)
                                                .map((assessment) => (
                                                    <div key={assessment.id} className="grid grid-cols-6 items-center p-4">
                                                        <div className="font-medium">{assessment.title}</div>
                                                        <div className="text-sm">{assessment.subject}</div>
                                                        <div className="text-sm">{assessment.class}</div>
                                                        <div className="text-center">{assessment.submissions}</div>
                                                        <div className="flex items-center justify-center">
                                                            <span className="rounded-full border border-red-200 bg-red-100 px-3 py-1 text-xs font-semibold text-red-800 shadow-sm">
                                                                {formatPercentage(assessment.avgScore)}
                                                            </span>
                                                        </div>
                                                        <div className="text-muted-foreground text-right text-sm">
                                                            {format(new Date(assessment.date), 'MMM d, yyyy')}
                                                        </div>
                                                    </div>
                                                ))}
                                        </div>
                                    </div>
                                </TabsContent>
                            </Tabs>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </AppLayout>
    );
}
