#!/bin/bash

# <PERSON><PERSON><PERSON> to apply the Questions component fix
echo "Applying Questions component fix for image uploads..."

# Check if the file exists
if [ ! -f "resources/js/pages/assessments/Questions.tsx" ]; then
    echo "Error: Could not find Questions.tsx file at resources/js/pages/assessments/Questions.tsx"
    exit 1
fi

# Check if the fixed file exists
if [ ! -f "resources/js/pages/assessments/QuestionsFixed.tsx" ]; then
    echo "Error: Could not find QuestionsFixed.tsx file at resources/js/pages/assessments/QuestionsFixed.tsx"
    echo "Please make sure you've downloaded or created the fixed file first."
    exit 1
fi

# Create a backup of the original file
cp resources/js/pages/assessments/Questions.tsx resources/js/pages/assessments/Questions.tsx.bak
echo "✓ Created backup at resources/js/pages/assessments/Questions.tsx.bak"

# Apply the fix by copying the fixed file
cp resources/js/pages/assessments/QuestionsFixed.tsx resources/js/pages/assessments/Questions.tsx
echo "✓ Applied the fix by replacing Questions.tsx with the fixed version"

# Make the file executable in case permissions were lost
chmod +x resources/js/pages/assessments/Questions.tsx
echo "✓ Updated file permissions"

echo ""
echo "Fix successfully applied! The original file has been backed up."
echo "You should now be able to submit image questions without issues."
echo ""
echo "If you encounter any problems, you can restore the original file with:"
echo "mv resources/js/pages/assessments/Questions.tsx.bak resources/js/pages/assessments/Questions.tsx" 