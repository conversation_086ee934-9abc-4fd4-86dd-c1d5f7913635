<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug QR Login - Nthanda</title>
    <link rel="stylesheet" href="styles.css">
    <script src="./axios.js"></script>
    <style>
        body {
            background-color: var(--color-yellow);
            padding: 20px;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: var(--color-white);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
        }
        
        .header h1 {
            color: var(--color-blue);
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 10px;
            text-align: center;
        }
        
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 2px solid var(--color-blue);
            border-radius: 15px;
        }
        
        .test-button {
            background-color: var(--color-green);
            color: var(--color-white);
            border: none;
            border-radius: 10px;
            padding: 12px 24px;
            font-weight: 600;
            cursor: pointer;
            margin: 10px;
            transition: transform 0.2s;
        }
        
        .test-button:hover {
            transform: translateY(-2px);
        }
        
        .result {
            margin: 20px 0;
            padding: 15px;
            border-radius: 10px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        
        .input-group {
            margin: 15px 0;
        }
        
        .input-group label {
            display: block;
            color: var(--color-blue);
            font-weight: 600;
            margin-bottom: 5px;
        }
        
        .input-group input {
            width: 100%;
            padding: 10px;
            border: 2px solid var(--color-blue);
            border-radius: 8px;
            font-size: 16px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>QR Login Debug Tool</h1>
        </div>
        
        <div class="test-section">
            <h3 style="color: var(--color-blue);">Test QR Login API</h3>
            <div class="input-group">
                <label for="qr-token">QR Token:</label>
                <input type="text" id="qr-token" value="STU_L497muLQ1XUBhy3PkyOXwrUA70Yarbzu" placeholder="Enter QR token">
            </div>
            <button class="test-button" onclick="testQRLogin()">Test QR Login</button>
            <button class="test-button" onclick="testPINLogin()">Test PIN Login (1234)</button>
            <button class="test-button" onclick="clearResults()">Clear Results</button>
            <div id="qr-result" class="result info" style="display: none;"></div>
        </div>
        
        <div class="test-section">
            <h3 style="color: var(--color-blue);">Test Student Data API</h3>
            <button class="test-button" onclick="testStudentData()">Get Students with QR Tokens</button>
            <div id="student-result" class="result info" style="display: none;"></div>
        </div>
        
        <div class="test-section">
            <h3 style="color: var(--color-blue);">Browser Console Logs</h3>
            <p style="color: var(--color-blue);">Open browser developer tools (F12) to see detailed console logs during testing.</p>
        </div>
    </div>

    <script>
        function showResult(elementId, content, type = 'info') {
            const element = document.getElementById(elementId);
            element.className = `result ${type}`;
            element.textContent = content;
            element.style.display = 'block';
        }

        async function testQRLogin() {
            const token = document.getElementById('qr-token').value;
            console.log('Testing QR login with token:', token);
            
            if (!token) {
                showResult('qr-result', 'Please enter a QR token', 'error');
                return;
            }
            
            try {
                showResult('qr-result', 'Making API request...', 'info');
                
                const response = await axios.post('http://localhost:8000/api/student/login-qr', {
                    qr_token: token
                });
                
                console.log('QR Login Response:', response);
                
                if (response.data.success) {
                    const result = `SUCCESS!\n\nStudent: ${response.data.student.name}\nGrade: ${response.data.student.grade}\nSubjects: ${response.data.subjects.length}\nUnits: ${response.data.units.length}\nTopics: ${response.data.topics.length}`;
                    showResult('qr-result', result, 'success');
                } else {
                    showResult('qr-result', `FAILED: ${response.data.message}`, 'error');
                }
            } catch (error) {
                console.error('QR Login Error:', error);
                showResult('qr-result', `ERROR: ${error.message}\n\nDetails: ${JSON.stringify(error.response?.data || error, null, 2)}`, 'error');
            }
        }

        async function testPINLogin() {
            console.log('Testing PIN login with PIN: 1234');
            
            try {
                showResult('qr-result', 'Making PIN login API request...', 'info');
                
                const response = await axios.post('http://localhost:8000/api/student/login', {
                    pin: '1234'
                });
                
                console.log('PIN Login Response:', response);
                
                if (response.data.success) {
                    const result = `PIN LOGIN SUCCESS!\n\nStudent: ${response.data.student.name}\nGrade: ${response.data.student.grade}`;
                    showResult('qr-result', result, 'success');
                } else {
                    showResult('qr-result', `PIN LOGIN FAILED: ${response.data.message}`, 'error');
                }
            } catch (error) {
                console.error('PIN Login Error:', error);
                showResult('qr-result', `PIN LOGIN ERROR: ${error.message}`, 'error');
            }
        }

        async function testStudentData() {
            console.log('Testing student data API');
            
            try {
                showResult('student-result', 'Fetching student data...', 'info');
                
                const response = await axios.get('http://localhost:8000/api/students/qr-tokens');
                
                console.log('Student Data Response:', response);
                
                const students = response.data.students;
                const result = `Found ${students.length} students with QR tokens:\n\n${students.map(s => `${s.name} (ID: ${s.id})\nToken: ${s.qr_token}`).join('\n\n')}`;
                showResult('student-result', result, 'success');
            } catch (error) {
                console.error('Student Data Error:', error);
                showResult('student-result', `ERROR: ${error.message}`, 'error');
            }
        }

        function clearResults() {
            document.getElementById('qr-result').style.display = 'none';
            document.getElementById('student-result').style.display = 'none';
        }

        // Test on page load
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Debug page loaded. Ready for testing.');
        });
    </script>
</body>
</html>
