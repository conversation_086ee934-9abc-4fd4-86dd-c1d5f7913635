# Troubleshooting Guide

This guide provides solutions for common issues you might encounter when working with the Lesson Content Management System.

## TypeScript Errors

### Type Errors for UI Components

If you encounter TypeScript errors related to UI components like:

```
Cannot find module '@/components/ui/switch' or its corresponding type declarations.
```

**Solution:**

1. Make sure you've installed all UI dependencies:
    ```bash
    bash install-ui-deps.sh
    ```
2. Check that type declaration files exist in the components directory. If not, create them manually:

    ```typescript
    // resources/js/components/ui/component-name.d.ts
    import * as React from 'react';

    export interface ComponentProps {
        // Define props here
    }

    export const ComponentName: React.FC<ComponentProps>;
    ```

### Argument Type Errors

For errors like:

```
Argument of type 'boolean' is not assignable to parameter of type 'false'.
```

**Solution:**
Check the component's expected prop types and adjust your usage to match the expected types. Consider creating or updating type declarations.

## Installation Issues

### Missing Dependencies

If you encounter unexpected "module not found" errors after a fresh installation:

**Solution:**

1. Run the setup script:
    ```bash
    bash setup-dev.sh
    ```
2. Manually install additional dependencies:
    ```bash
    npm install --save-dev @typescript-eslint/parser @typescript-eslint/eslint-plugin
    ```

### UI Component Rendering Issues

If components don't render correctly:

**Solution:**

1. Clear the Vite cache:
    ```bash
    rm -rf node_modules/.vite
    ```
2. Restart the development server:
    ```bash
    npm run dev
    ```

## Media File Issues

### Uploaded Files Not Displaying

If uploaded media files aren't displaying:

**Solution:**

1. Check that storage is linked:
    ```bash
    php artisan storage:link
    ```
2. Verify file permissions:
    ```bash
    chmod -R 755 storage
    ```
3. Check file paths in the console for errors

### Audio/Video Playback Problems

If audio or video files won't play:

**Solution:**

1. Check file format compatibility (MP3/MP4 recommended)
2. Verify MIME types are properly configured in config/filesystems.php
3. Try uploading a different file to isolate the issue

## Database Issues

### Migration Errors

If you encounter database migration errors:

**Solution:**

1. Reset the database:
    ```bash
    php artisan migrate:fresh
    ```
2. If seeding is available:
    ```bash
    php artisan db:seed
    ```

## Performance Optimization

If the application feels slow:

**Solution:**

1. Use production builds for better performance:
    ```bash
    npm run build
    ```
2. Optimize database queries by adding indexes
3. Implement lazy loading for content blocks in lessons with many blocks

## Development Workflow Tips

1. Use the development setup script to ensure consistency
2. Clear cache regularly during development
3. Check the browser console for frontend errors
4. Check Laravel logs for backend errors: `storage/logs/laravel.log`
