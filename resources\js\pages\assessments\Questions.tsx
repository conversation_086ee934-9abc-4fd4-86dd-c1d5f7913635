import { <PERSON><PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { Textarea } from '@/components/ui/textarea';
import AppLayout from '@/layouts/app-layout';
import { BreadcrumbItem } from '@/types';
import { Head, Link, router, useForm } from '@inertiajs/react';
import { ArrowLeft, CheckCircle, Edit, File, Image, MoreHorizontal, Plus, Trash, Upload } from 'lucide-react';
import { useEffect, useState } from 'react';
import { useDropzone } from 'react-dropzone';

interface Assessment {
    id: number;
    title: string;
    description: string;
    class_id: number;
    subject_id: number;
    unit_id: number;
    topic_id: number;
}

interface Question {
    id: number;
    assessment_id: number;
    question: string;
    question_type: 'text' | 'image';
    option_a: string;
    option_b: string;
    option_c: string;
    option_d: string;
    correct_answer: 'A' | 'B' | 'C' | 'D';
    audio_url?: string;
}

interface QuestionForm {
    question: string;
    question_type: 'text' | 'image';
    option_a: string;
    option_b: string;
    option_c: string;
    option_d: string;
    correct_answer: 'A' | 'B' | 'C' | 'D';
    audio_url?: string;
    _method?: string;
    [key: string]: string | undefined;
}

interface Props {
    assessment: Assessment;
    questions: Question[];
}

function Questions({ assessment, questions }: Props) {
    const [isAddingQuestion, setIsAddingQuestion] = useState(false);
    const [isEditingQuestion, setIsEditingQuestion] = useState(false);
    const [currentQuestion, setCurrentQuestion] = useState<Question | null>(null);
    const [isConfirmingDelete, setIsConfirmingDelete] = useState(false);
    const [questionToDelete, setQuestionToDelete] = useState<Question | null>(null);
    const [audioFile, setAudioFile] = useState<File | null>(null);
    const [audioPreview, setAudioPreview] = useState<string>('');
    const [imageFiles, setImageFiles] = useState<Record<string, File | null>>({
        option_a: null,
        option_b: null,
        option_c: null,
        option_d: null,
    });
    const [imagePreviews, setImagePreviews] = useState<Record<string, string>>({
        option_a: '',
        option_b: '',
        option_c: '',
        option_d: '',
    });

    const { data, setData, processing, errors, reset } = useForm<QuestionForm>({
        question: '',
        question_type: 'text',
        option_a: '',
        option_b: '',
        option_c: '',
        option_d: '',
        correct_answer: 'A',
        audio_url: '',
    });

    // Create dropzones for each option
    const optionADropzone = useDropzone({
        accept: { 'image/*': ['.jpeg', '.jpg', '.png', '.gif'] },
        onDrop: (acceptedFiles: File[]) => {
            if (acceptedFiles.length === 0) return;
            const file = acceptedFiles[0];

            // Revoke any existing object URL to prevent memory leaks
            if (imagePreviews.option_a) {
                URL.revokeObjectURL(imagePreviews.option_a);
            }

            const objectUrl = URL.createObjectURL(file);
            setImageFiles((prev) => ({ ...prev, option_a: file }));
            setImagePreviews((prev) => ({ ...prev, option_a: objectUrl }));
        },
    });

    const optionBDropzone = useDropzone({
        accept: { 'image/*': ['.jpeg', '.jpg', '.png', '.gif'] },
        onDrop: (acceptedFiles: File[]) => {
            if (acceptedFiles.length === 0) return;
            const file = acceptedFiles[0];

            // Revoke any existing object URL to prevent memory leaks
            if (imagePreviews.option_b) {
                URL.revokeObjectURL(imagePreviews.option_b);
            }

            const objectUrl = URL.createObjectURL(file);
            setImageFiles((prev) => ({ ...prev, option_b: file }));
            setImagePreviews((prev) => ({ ...prev, option_b: objectUrl }));
        },
    });

    const optionCDropzone = useDropzone({
        accept: { 'image/*': ['.jpeg', '.jpg', '.png', '.gif'] },
        onDrop: (acceptedFiles: File[]) => {
            if (acceptedFiles.length === 0) return;
            const file = acceptedFiles[0];

            // Revoke any existing object URL to prevent memory leaks
            if (imagePreviews.option_c) {
                URL.revokeObjectURL(imagePreviews.option_c);
            }

            const objectUrl = URL.createObjectURL(file);
            setImageFiles((prev) => ({ ...prev, option_c: file }));
            setImagePreviews((prev) => ({ ...prev, option_c: objectUrl }));
        },
    });

    const optionDDropzone = useDropzone({
        accept: { 'image/*': ['.jpeg', '.jpg', '.png', '.gif'] },
        onDrop: (acceptedFiles: File[]) => {
            if (acceptedFiles.length === 0) return;
            const file = acceptedFiles[0];

            // Revoke any existing object URL to prevent memory leaks
            if (imagePreviews.option_d) {
                URL.revokeObjectURL(imagePreviews.option_d);
            }

            const objectUrl = URL.createObjectURL(file);
            setImageFiles((prev) => ({ ...prev, option_d: file }));
            setImagePreviews((prev) => ({ ...prev, option_d: objectUrl }));
        },
    });

    // Cleanup object URLs when component unmounts
    useEffect(() => {
        return () => {
            // Revoke all object URLs when component unmounts
            Object.values(imagePreviews).forEach((url) => {
                if (url && url.startsWith('blob:')) {
                    URL.revokeObjectURL(url);
                }
            });
        };
    }, []);

    // Get the appropriate dropzone for a given option
    const getDropzoneForOption = (option: string) => {
        switch (option.toLowerCase()) {
            case 'a':
                return optionADropzone;
            case 'b':
                return optionBDropzone;
            case 'c':
                return optionCDropzone;
            case 'd':
                return optionDDropzone;
            default:
                return optionADropzone;
        }
    };

    const resetForm = () => {
        // Clean up any existing object URLs first
        Object.values(imagePreviews).forEach((url) => {
            if (url && url.startsWith('blob:')) {
                URL.revokeObjectURL(url);
            }
        });

        // Clean up audio preview URL
        if (audioPreview && audioPreview.startsWith('blob:')) {
            URL.revokeObjectURL(audioPreview);
        }

        reset();
        setAudioFile(null);
        setAudioPreview('');
        setImageFiles({
            option_a: null,
            option_b: null,
            option_c: null,
            option_d: null,
        });
        setImagePreviews({
            option_a: '',
            option_b: '',
            option_c: '',
            option_d: '',
        });
    };

    // Add audio file handling
    const handleAudioChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0];
        if (!file) return;

        // Clean up previous audio preview URL
        if (audioPreview && audioPreview.startsWith('blob:')) {
            URL.revokeObjectURL(audioPreview);
        }

        // Validate file type
        const validTypes = ['audio/mpeg', 'audio/wav', 'audio/ogg'];
        if (!validTypes.includes(file.type)) {
            alert('Please upload a valid audio file (MP3, WAV, or OGG)');
            return;
        }

        const objectUrl = URL.createObjectURL(file);
        setAudioFile(file);
        setAudioPreview(objectUrl);
        setData('audio_url', ''); // Clear any existing audio_url when new file is selected
    };

    const removeAudio = () => {
        if (audioPreview && audioPreview.startsWith('blob:')) {
            URL.revokeObjectURL(audioPreview);
        }
        setAudioFile(null);
        setAudioPreview('');
    };

    const validateImageOptions = () => {
        if (data.question_type !== 'image') return true;

        // Check that each option has either a preview URL or a file selected
        const options = ['a', 'b', 'c', 'd'];
        const missingOptions = options.filter((opt) => {
            const key = `option_${opt}`;
            return !imageFiles[key] && !imagePreviews[key];
        });

        if (missingOptions.length > 0) {
            const missingLabels = missingOptions.map((opt) => opt.toUpperCase()).join(', ');
            alert(`Please upload images for all options. Missing: Option ${missingLabels}`);
            return false;
        }

        // Make sure the correct answer has an image
        const correctOptionKey = `option_${data.correct_answer.toLowerCase()}`;
        if (!imageFiles[correctOptionKey] && !imagePreviews[correctOptionKey]) {
            alert(`Please upload an image for the correct answer (Option ${data.correct_answer})`);
            return false;
        }

        return true;
    };

    const onSubmit = (e: React.FormEvent) => {
        e.preventDefault();

        // Validate image options if using image type
        if (!validateImageOptions()) {
            return;
        }

        try {
            // Create a FormData object for multipart/form-data submission
            const formData = new FormData();

            // Add basic question data
            formData.append('question_type', data.question_type); // Add question type first to ensure it's the first item in the FormData
            formData.append('question', data.question);
            formData.append('correct_answer', data.correct_answer);

            // Add audio file if present
            if (audioFile) {
                formData.append('audio', audioFile);
            } else if (audioPreview) {
                formData.append('audio_url', audioPreview);
            }

            // Handle options based on question type
            if (data.question_type === 'text') {
                // For text questions, simply append the text options
                formData.append('option_a', data.option_a || '');
                formData.append('option_b', data.option_b || '');
                formData.append('option_c', data.option_c || '');
                formData.append('option_d', data.option_d || '');
            } else {
                // For image questions, handle file uploads or existing URLs
                ['a', 'b', 'c', 'd'].forEach((opt) => {
                    const optionKey = `option_${opt}`;
                    const file = imageFiles[optionKey];

                    if (file) {
                        // If we have a new file, append it to the form data
                        formData.append(optionKey, file);
                    } else if (imagePreviews[optionKey]) {
                        // If we're keeping an existing image URL (from the database)
                        formData.append(optionKey, imagePreviews[optionKey]);
                    }
                });
            }

            // Add method override for PATCH requests when updating
            if (currentQuestion) {
                formData.append('_method', 'PATCH');
            }

            // Determine the submission URL
            const url = currentQuestion ? `/assessments/${assessment.id}/questions/${currentQuestion.id}` : `/assessments/${assessment.id}/questions`;

            // Use Inertia router with forceFormData option
            router.post(url, formData, {
                forceFormData: true,
                onSuccess: () => {
                    if (currentQuestion) {
                        setIsEditingQuestion(false);
                    } else {
                        setIsAddingQuestion(false);
                    }
                    resetForm(); // Reset the form and audio preview
                    setCurrentQuestion(null);
                },
                onError: (errors) => {
                    console.error('Submission errors:', errors);
                    alert('There was an error saving your question. Please check the form and try again.');
                },
            });
        } catch (error) {
            console.error('Error preparing form submission:', error);
            alert('There was an unexpected error. Please try again.');
        }
    };

    const handleEdit = (question: Question) => {
        setCurrentQuestion(question);
        setData({
            question: question.question,
            question_type: question.question_type,
            option_a: question.option_a,
            option_b: question.option_b,
            option_c: question.option_c,
            option_d: question.option_d,
            correct_answer: question.correct_answer,
            audio_url: question.audio_url || '',
        });

        // Set audio preview if exists
        if (question.audio_url) {
            setAudioPreview(question.audio_url);
            setAudioFile(null); // Clear any existing audio file when editing
        } else {
            setAudioPreview('');
            setAudioFile(null);
        }

        if (question.question_type === 'image') {
            // For image-type questions, load existing image URLs
            const previewUrls = {
                option_a: question.option_a || '',
                option_b: question.option_b || '',
                option_c: question.option_c || '',
                option_d: question.option_d || '',
            };

            setImagePreviews(previewUrls);
            setImageFiles({
                option_a: null,
                option_b: null,
                option_c: null,
                option_d: null,
            });
        }

        setIsEditingQuestion(true);
    };

    const handleDelete = () => {
        if (!questionToDelete) return;

        router.delete(`/assessments/${assessment.id}/questions/${questionToDelete.id}`, {
            onSuccess: () => {
                setIsConfirmingDelete(false);
                setQuestionToDelete(null);
            },
        });
    };

    // Add this function to your component for image cleanup
    const cleanupImage = (optionKey: string) => {
        const url = imagePreviews[optionKey];
        if (url && url.startsWith('blob:')) {
            URL.revokeObjectURL(url);
        }
        setImageFiles((prev) => ({
            ...prev,
            [optionKey]: null,
        }));
        setImagePreviews((prev) => ({
            ...prev,
            [optionKey]: '',
        }));
    };

    const breadcrumbs: BreadcrumbItem[] = [
        {
            title: 'Assessments',
            href: '/assessments',
        },
        {
            title: assessment.title,
            href: route('assessments.index', { class_id: assessment.class_id, subject_id: assessment.subject_id }),
        },
        {
            title: 'Questions',
            href: `/assessments/${assessment.id}/questions`,
        },
    ];

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title={`${assessment.title} - Questions`} />
            <div className="container mx-auto p-6">
                <div className="mb-8 flex items-center justify-between">
                    <div>
                        <div className="flex items-center gap-2">
                            <Link href={route('assessments.index', { class_id: assessment.class_id, subject_id: assessment.subject_id })}>
                                <Button variant="ghost" size="icon" className="rounded-full">
                                    <ArrowLeft className="h-5 w-5" />
                                </Button>
                            </Link>
                            <h1 className="text-2xl font-bold text-gray-900">{assessment.title}</h1>
                        </div>
                        <p className="text-sm text-gray-500">{assessment.description}</p>
                    </div>
                    <Button onClick={() => setIsAddingQuestion(true)}>
                        <Plus className="mr-2 h-4 w-4" /> Add Question
                    </Button>
                </div>

                <Separator className="my-6" />

                {questions.length === 0 ? (
                    <div className="flex flex-col items-center justify-center rounded-lg border-2 border-dashed border-gray-300 p-12 text-center">
                        <File className="mx-auto h-12 w-12 text-gray-400" />
                        <h3 className="mt-2 text-sm font-medium text-gray-900">No questions yet</h3>
                        <p className="mt-1 text-sm text-gray-500">Get started by adding questions to this assessment.</p>
                        <div className="mt-6">
                            <Button onClick={() => setIsAddingQuestion(true)}>
                                <Plus className="mr-2 h-4 w-4" /> Add Question
                            </Button>
                        </div>
                    </div>
                ) : (
                    <div className="grid gap-6 md:grid-cols-2">
                        {questions.map((question, index) => (
                            <Card key={question.id} className="overflow-hidden">
                                <CardHeader className="bg-gray-50 py-3">
                                    <div className="flex items-start justify-between">
                                        <div className="flex">
                                            <div className="mr-3 flex h-8 w-8 items-center justify-center rounded-full bg-blue-950 text-sm font-medium text-white">
                                                {index + 1}
                                            </div>
                                            <div>
                                                <CardTitle className="text-base">{question.question}</CardTitle>
                                                <p className="text-xs text-gray-500">
                                                    Type: {question.question_type === 'image' ? 'Image choices' : 'Text choices'}
                                                </p>
                                            </div>
                                        </div>
                                        <DropdownMenu>
                                            <DropdownMenuTrigger className="cursor-pointer rounded-md p-1 text-gray-400 hover:bg-gray-100">
                                                <MoreHorizontal className="h-5 w-5" />
                                            </DropdownMenuTrigger>
                                            <DropdownMenuContent align="end">
                                                <DropdownMenuItem className="cursor-pointer" onClick={() => handleEdit(question)}>
                                                    <Edit className="mr-2 h-4 w-4" />
                                                    Edit
                                                </DropdownMenuItem>
                                                <DropdownMenuItem
                                                    className="cursor-pointer text-red-500"
                                                    onClick={() => {
                                                        setQuestionToDelete(question);
                                                        setIsConfirmingDelete(true);
                                                    }}
                                                >
                                                    <Trash className="mr-2 h-4 w-4" />
                                                    Delete
                                                </DropdownMenuItem>
                                            </DropdownMenuContent>
                                        </DropdownMenu>
                                    </div>
                                </CardHeader>
                                <CardContent className="p-5">
                                    {question.question_type === 'text' ? (
                                        <div className="grid grid-cols-1 gap-3 sm:grid-cols-2">
                                            {['A', 'B', 'C', 'D'].map((option) => {
                                                const optionKey = `option_${option.toLowerCase()}` as keyof Question;
                                                const isCorrect = question.correct_answer === option;
                                                return (
                                                    <div
                                                        key={option}
                                                        className={`flex items-center rounded-md border p-3 ${
                                                            isCorrect ? 'border-green-500 bg-green-50' : 'border-gray-200'
                                                        }`}
                                                    >
                                                        <div
                                                            className={`mr-3 flex h-6 w-6 items-center justify-center rounded-full ${
                                                                isCorrect ? 'bg-green-500 text-white' : 'bg-gray-200 text-gray-700'
                                                            }`}
                                                        >
                                                            {option}
                                                        </div>
                                                        <span>{question[optionKey] as string}</span>
                                                        {isCorrect && <CheckCircle className="ml-auto h-4 w-4 text-green-500" />}
                                                    </div>
                                                );
                                            })}
                                        </div>
                                    ) : (
                                        <div className="grid grid-cols-1 gap-3 sm:grid-cols-2">
                                            {['A', 'B', 'C', 'D'].map((option) => {
                                                const optionKey = `option_${option.toLowerCase()}` as keyof Question;
                                                const isCorrect = question.correct_answer === option;
                                                return (
                                                    <div
                                                        key={option}
                                                        className={`flex flex-col overflow-hidden rounded-md border ${
                                                            isCorrect ? 'border-green-500' : 'border-gray-200'
                                                        }`}
                                                    >
                                                        <div
                                                            className={`flex items-center justify-between px-3 py-2 ${
                                                                isCorrect ? 'bg-green-500 text-white' : 'bg-gray-100'
                                                            }`}
                                                        >
                                                            <span>Option {option}</span>
                                                            {isCorrect && <CheckCircle className="h-4 w-4" />}
                                                        </div>
                                                        <div className="p-2">
                                                            <img
                                                                src={question[optionKey] as string}
                                                                alt={`Option ${option}`}
                                                                className="aspect-video w-full rounded object-cover"
                                                            />
                                                        </div>
                                                    </div>
                                                );
                                            })}
                                        </div>
                                    )}

                                    {/* Question Card Audio Preview */}
                                    {question.audio_url && (
                                        <div className="mt-4 rounded-lg border p-4">
                                            <div className="flex items-center justify-between">
                                                <audio controls className="max-w-[200px]">
                                                    <source src={question.audio_url} type="audio/mpeg" />
                                                    Your browser does not support the audio element.
                                                </audio>
                                            </div>
                                        </div>
                                    )}
                                </CardContent>
                            </Card>
                        ))}
                    </div>
                )}

                {/* Add/Edit Question Dialog */}
                <Dialog
                    open={isAddingQuestion || isEditingQuestion}
                    onOpenChange={(open) => {
                        if (!open) {
                            setIsAddingQuestion(false);
                            setIsEditingQuestion(false);
                            reset();
                        }
                    }}
                >
                    <DialogContent className="max-h-[90vh] max-w-2xl overflow-y-auto">
                        <DialogHeader className="sticky top-0 z-10 bg-white pb-4">
                            <DialogTitle>{isEditingQuestion ? 'Edit Question' : 'Add New Question'}</DialogTitle>
                            <DialogDescription>
                                {isEditingQuestion
                                    ? 'Make changes to the question and options below.'
                                    : 'Create a new question with multiple choice answers.'}
                            </DialogDescription>
                        </DialogHeader>

                        <form onSubmit={onSubmit} className="space-y-6">
                            <div className="space-y-2">
                                <Label htmlFor="question">Question</Label>
                                <Textarea
                                    id="question"
                                    placeholder="Enter your question here..."
                                    value={data.question}
                                    onChange={(e) => setData('question', e.target.value)}
                                />
                                {errors.question && <p className="text-sm text-red-500">{errors.question}</p>}
                            </div>
                            <div className="space-y-4">
                                {/* Audio Upload Section */}
                                <div>
                                    <Label>Question Audio (Optional)</Label>
                                    <div className="mt-2 space-y-2">
                                        {audioPreview ? (
                                            <div className="rounded-lg border p-4">
                                                <div className="flex items-center justify-between">
                                                    <audio controls className="max-w-[200px]">
                                                        <source src={audioPreview} type={audioFile ? audioFile.type : 'audio/mpeg'} />
                                                        Your browser does not support the audio element.
                                                    </audio>
                                                    <Button type="button" variant="destructive" size="sm" onClick={removeAudio}>
                                                        <Trash className="h-4 w-4" />
                                                    </Button>
                                                </div>
                                            </div>
                                        ) : (
                                            <div className="flex items-center justify-center rounded-lg border border-dashed border-gray-300 px-6 py-4">
                                                <label className="flex cursor-pointer items-center">
                                                    <input type="file" accept="audio/*" onChange={handleAudioChange} className="hidden" />
                                                    <div className="flex items-center gap-2 text-sm text-gray-600">
                                                        <Upload className="h-4 w-4" />
                                                        <span>Upload audio file</span>
                                                    </div>
                                                </label>
                                            </div>
                                        )}
                                    </div>
                                </div>
                                <div className="space-y-2">
                                    <Label>Question Type</Label>
                                    <RadioGroup
                                        value={data.question_type}
                                        onValueChange={(value) => setData('question_type', value as 'text' | 'image')}
                                        className="flex space-x-4"
                                    >
                                        <div className="flex items-center space-x-2">
                                            <RadioGroupItem value="text" id="text" />
                                            <Label htmlFor="text" className="flex items-center">
                                                <File className="mr-1 h-4 w-4" /> Text Options
                                            </Label>
                                        </div>
                                        <div className="flex items-center space-x-2">
                                            <RadioGroupItem value="image" id="image" />
                                            <Label htmlFor="image" className="flex items-center">
                                                <Image className="mr-1 h-4 w-4" /> Image Options
                                            </Label>
                                        </div>
                                    </RadioGroup>
                                    <p className="text-sm text-gray-500">Choose whether answers will be text-based or image-based.</p>
                                    {errors.question_type && <p className="text-sm text-red-500">{errors.question_type}</p>}
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="correct_answer">Correct Answer</Label>
                                    <Select
                                        value={data.correct_answer}
                                        onValueChange={(value) => setData('correct_answer', value as 'A' | 'B' | 'C' | 'D')}
                                    >
                                        <SelectTrigger>
                                            <SelectValue placeholder="Select the correct answer" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="A">Option A</SelectItem>
                                            <SelectItem value="B">Option B</SelectItem>
                                            <SelectItem value="C">Option C</SelectItem>
                                            <SelectItem value="D">Option D</SelectItem>
                                        </SelectContent>
                                    </Select>
                                    {errors.correct_answer && <p className="text-sm text-red-500">{errors.correct_answer}</p>}
                                </div>

                                <div className="rounded-md border">
                                    <div className="sticky top-0 z-10 bg-gray-50 px-4 py-2 text-sm font-medium">Answer Options</div>
                                    <div className="divide-y">
                                        {['A', 'B', 'C', 'D'].map((option) => {
                                            const optionField = `option_${option.toLowerCase()}` as keyof typeof data;
                                            const isCorrectOption = data.correct_answer === option;

                                            return (
                                                <div key={option} className={`p-4 ${isCorrectOption ? 'bg-green-50' : ''}`}>
                                                    <div className="mb-2 flex items-center">
                                                        <div
                                                            className={`mr-2 flex h-6 w-6 items-center justify-center rounded-full ${
                                                                isCorrectOption ? 'bg-green-500 text-white' : 'bg-gray-200 text-gray-700'
                                                            }`}
                                                        >
                                                            {option}
                                                        </div>
                                                        <span className="text-sm font-medium">
                                                            Option {option} {isCorrectOption && '(Correct)'}
                                                        </span>
                                                    </div>

                                                    {data.question_type === 'text' ? (
                                                        <div className="space-y-2">
                                                            <Input
                                                                placeholder={`Enter option ${option}`}
                                                                value={data[optionField] as string}
                                                                onChange={(e) => setData(optionField, e.target.value)}
                                                            />
                                                            {errors[optionField] && <p className="text-sm text-red-500">{errors[optionField]}</p>}
                                                        </div>
                                                    ) : (
                                                        <div>
                                                            <div className="mb-2">
                                                                {imagePreviews[`option_${option.toLowerCase()}`] ? (
                                                                    <div className="relative">
                                                                        <img
                                                                            src={imagePreviews[`option_${option.toLowerCase()}`]}
                                                                            alt={`Preview ${option}`}
                                                                            className="h-32 w-full rounded border object-cover"
                                                                        />
                                                                        <Button
                                                                            type="button"
                                                                            variant="outline"
                                                                            size="sm"
                                                                            className="absolute right-2 bottom-2"
                                                                            onClick={() => {
                                                                                cleanupImage(`option_${option.toLowerCase()}`);
                                                                            }}
                                                                        >
                                                                            <Trash className="h-4 w-4" />
                                                                        </Button>
                                                                    </div>
                                                                ) : (
                                                                    <div
                                                                        {...getDropzoneForOption(option).getRootProps()}
                                                                        className="flex h-32 cursor-pointer flex-col items-center justify-center rounded border border-dashed border-gray-300 p-4 text-center hover:bg-gray-50"
                                                                    >
                                                                        <input {...getDropzoneForOption(option).getInputProps()} />
                                                                        <Upload className="mb-2 h-6 w-6 text-gray-400" />
                                                                        <p className="text-sm text-gray-500">
                                                                            Click or drag to upload image for Option {option}
                                                                        </p>
                                                                        <p className="text-xs text-gray-400">PNG, JPG, GIF up to 10MB</p>
                                                                    </div>
                                                                )}
                                                            </div>
                                                        </div>
                                                    )}
                                                </div>
                                            );
                                        })}
                                    </div>
                                </div>
                            </div>

                            <DialogFooter className="sticky bottom-0 z-10 bg-white pt-4">
                                <Button
                                    type="button"
                                    variant="outline"
                                    onClick={() => {
                                        setIsAddingQuestion(false);
                                        setIsEditingQuestion(false);
                                        resetForm();
                                    }}
                                >
                                    Cancel
                                </Button>
                                <Button type="submit" disabled={processing}>
                                    {processing ? 'Saving...' : 'Save Question'}
                                </Button>
                            </DialogFooter>
                        </form>
                    </DialogContent>
                </Dialog>

                {/* Delete Confirmation Dialog */}
                <Dialog open={isConfirmingDelete} onOpenChange={setIsConfirmingDelete}>
                    <DialogContent>
                        <DialogHeader>
                            <DialogTitle>Confirm Deletion</DialogTitle>
                            <DialogDescription>Are you sure you want to delete this question? This action cannot be undone.</DialogDescription>
                        </DialogHeader>
                        <DialogFooter>
                            <Button variant="outline" onClick={() => setIsConfirmingDelete(false)}>
                                Cancel
                            </Button>
                            <Button variant="destructive" onClick={handleDelete}>
                                Delete
                            </Button>
                        </DialogFooter>
                    </DialogContent>
                </Dialog>
            </div>
        </AppLayout>
    );
}

export default Questions;
