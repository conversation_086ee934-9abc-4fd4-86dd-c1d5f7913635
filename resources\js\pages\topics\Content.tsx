import { But<PERSON> } from '@/components/ui/button';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import { Separator } from '@/components/ui/separator';
import { Textarea } from '@/components/ui/textarea';
import WysiwygEditor from '@/components/WysiwygEditor';
import { closestCenter, DndContext, DragEndEvent, KeyboardSensor, PointerSensor, useSensor, useSensors } from '@dnd-kit/core';
import { arrayMove, SortableContext, sortableKeyboardCoordinates, useSortable, verticalListSortingStrategy } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { Head, router, usePage } from '@inertiajs/react';
import axios from 'axios';
import { ChevronLeft, Edit, MoreHorizontal, Plus, Trash, Volume2, X } from 'lucide-react';
import { useEffect, useRef, useState } from 'react';

interface ContentBlock {
    id: number;
    topic_id: number;
    type: 'heading' | 'paragraph' | 'image' | 'video' | 'audio' | 'list' | 'listItem' | 'zip' | 'wysiwyg';
    content?: string;
    media_path?: string;
    audio_path?: string;
    order: number;
    parent_id?: number;
    attributes: {
        level?: string;
        url?: string;
        items?: string[];
        listType?: 'bullet' | 'numbered';
        index_path?: string;
        html_content?: string; // For WYSIWYG rich content
        [key: string]: unknown;
    };
    children?: ContentBlock[];
    created_at: string;
    updated_at: string;
}

interface Topic {
    id: number;
    name: string;
    description?: string;
    unit_id: number;
    order: number;
    created_at: string;
    updated_at: string;
}

interface PageProps {
    topic: Topic;
    contentBlocks: ContentBlock[];
    errors: Record<string, string>;
}

// ContentTypeForm component that renders different forms based on content type
const ContentTypeForm = ({ type, topicId, onCancel, onSuccess }: { type: string; topicId: number; onCancel: () => void; onSuccess: () => void }) => {
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [listItems, setListItems] = useState<string[]>(['']);

    // Optional audio narration for content types except video and audio
    const [narrationAudio, setNarrationAudio] = useState<File | null>(null);
    const audioRef = useRef<HTMLAudioElement>(null);

    // Form state management
    const [headingData, setHeadingData] = useState({ level: 'h2', content: '' });
    const [paragraphData, setParagraphData] = useState({ content: '' });
    const [imageData, setImageData] = useState({ alt: '', media: null as File | null });
    const [videoData, setVideoData] = useState({
        title: '',
        url: '',
        video: null as File | null,
        previewUrl: '',
    });
    const [audioData, setAudioData] = useState({
        title: '',
        audio: null as File | null,
        previewUrl: '',
    });
    const [listData, setListData] = useState({ listType: 'bullet' as 'bullet' | 'numbered' });
    const [zipData, setZipData] = useState({ zip: null as File | null, description: '' });
    const [wysiwygData, setWysiwygData] = useState({ content: '<p>Start writing your content...</p>' });

    // Create preview URLs when files are selected
    useEffect(() => {
        if (videoData.video) {
            const url = URL.createObjectURL(videoData.video);
            setVideoData((prev) => ({ ...prev, previewUrl: url }));
            return () => {
                URL.revokeObjectURL(url);
            };
        }
    }, [videoData.video]);

    useEffect(() => {
        if (audioData.audio) {
            const url = URL.createObjectURL(audioData.audio);
            setAudioData((prev) => ({ ...prev, previewUrl: url }));
            return () => {
                URL.revokeObjectURL(url);
            };
        }
    }, [audioData.audio]);

    useEffect(() => {
        if (narrationAudio && audioRef.current) {
            const url = URL.createObjectURL(narrationAudio);
            audioRef.current.src = url;
            return () => {
                URL.revokeObjectURL(url);
            };
        }
    }, [narrationAudio]);

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        setIsSubmitting(true);

        try {
            // Get max order for new block
            const response = await axios.get(route('topics.content', { topic_id: topicId }));
            const blocks = response.data.contentBlocks || [];
            const maxOrder = blocks.length > 0 ? Math.max(...blocks.map((block: ContentBlock) => block.order)) : 0;

            // Create FormData for file uploads
            const data = new FormData();
            data.append('topic_id', topicId.toString());
            data.append('type', type);
            data.append('order', (maxOrder + 1).toString());

            // Add type-specific data
            switch (type) {
                case 'heading':
                    data.append('content', headingData.content);
                    data.append('attributes', JSON.stringify({ level: headingData.level }));
                    break;
                case 'paragraph':
                    data.append('content', paragraphData.content);
                    break;
                case 'image':
                    data.append('content', imageData.alt);
                    if (imageData.media) {
                        data.append('media', imageData.media);
                    }
                    break;
                case 'video':
                    data.append('content', videoData.title);
                    if (videoData.video) {
                        data.append('video', videoData.video);
                    }
                    data.append('attributes', JSON.stringify({ url: videoData.url }));
                    break;
                case 'audio':
                    data.append('content', audioData.title);
                    if (audioData.audio) {
                        data.append('audio', audioData.audio);
                    }
                    break;
                case 'list':
                    data.append('content', '');
                    data.append(
                        'attributes',
                        JSON.stringify({
                            listType: listData.listType,
                            items: listItems.filter((item) => item.trim() !== ''),
                        }),
                    );
                    break;
                case 'zip':
                    data.append('content', zipData.description);
                    if (zipData.zip) {
                        data.append('zip', zipData.zip);
                    }
                    break;
                case 'wysiwyg':
                    data.append('content', ''); // Title/description can be empty for WYSIWYG
                    data.append('attributes', JSON.stringify({ html_content: wysiwygData.content }));
                    break;
            }

            // Add narration audio for all types except video and audio
            if (type !== 'video' && type !== 'audio' && narrationAudio) {
                data.append('audio', narrationAudio);
            }

            // Send request
            await axios.post(route('content-blocks.store'), data, {
                headers: {
                    'Content-Type': 'multipart/form-data',
                },
            });

            onSuccess();
        } catch (error) {
            console.error('Error creating content block:', error);
        } finally {
            setIsSubmitting(false);
        }
    };

    const addListItem = () => {
        setListItems([...listItems, '']);
    };

    const updateListItem = (index: number, value: string) => {
        const newItems = [...listItems];
        newItems[index] = value;
        setListItems(newItems);
    };

    const removeListItem = (index: number) => {
        if (listItems.length > 1) {
            const newItems = [...listItems];
            newItems.splice(index, 1);
            setListItems(newItems);
        }
    };

    // Render optional audio narration section for applicable content types
    const renderNarrationSection = () => {
        if (type === 'video' || type === 'audio') {
            return null;
        }

        return (
            <div className="mt-4 mb-4 border-t pt-4">
                <h3 className="mb-2 text-sm font-medium">Optional Audio Narration</h3>
                <div className="space-y-2">
                    <Input
                        type="file"
                        accept="audio/*"
                        onChange={(e) => {
                            const file = e.target.files?.[0] || null;
                            setNarrationAudio(file);
                        }}
                    />
                    <p className="text-xs text-gray-500">Add audio narration to assist users who prefer listening.</p>

                    {narrationAudio && (
                        <div className="mt-2">
                            <audio ref={audioRef} controls className="w-full" />
                        </div>
                    )}
                </div>
            </div>
        );
    };

    return (
        <Dialog open={true} onOpenChange={() => onCancel()}>
            <DialogContent className={type === 'wysiwyg' ? 'max-h-[90vh] overflow-y-auto sm:max-w-4xl' : 'sm:max-w-md'}>
                <DialogHeader>
                    <DialogTitle>Add {type === 'wysiwyg' ? 'Rich Text Content' : type.charAt(0).toUpperCase() + type.slice(1)}</DialogTitle>
                </DialogHeader>

                {/* Heading Form */}
                {type === 'heading' && (
                    <form onSubmit={handleSubmit} className="space-y-4">
                        <div className="space-y-2">
                            <label className="block text-sm font-medium">Heading Level</label>
                            <select
                                value={headingData.level}
                                onChange={(e) => setHeadingData({ ...headingData, level: e.target.value })}
                                className="w-full rounded-md border border-gray-300 p-2"
                            >
                                <option value="h1">Heading 1 (Largest)</option>
                                <option value="h2">Heading 2</option>
                                <option value="h3">Heading 3</option>
                                <option value="h4">Heading 4</option>
                                <option value="h5">Heading 5</option>
                                <option value="h6">Heading 6 (Smallest)</option>
                            </select>
                        </div>
                        <div className="space-y-2">
                            <label className="block text-sm font-medium">Heading Text</label>
                            <Input
                                value={headingData.content}
                                onChange={(e) => setHeadingData({ ...headingData, content: e.target.value })}
                                placeholder="Enter heading text"
                                required
                            />
                        </div>

                        {renderNarrationSection()}

                        <div className="flex justify-end gap-2">
                            <Button type="button" variant="outline" onClick={onCancel}>
                                Cancel
                            </Button>
                            <Button type="submit" disabled={isSubmitting}>
                                {isSubmitting ? 'Adding...' : 'Add Heading'}
                            </Button>
                        </div>
                    </form>
                )}

                {/* Paragraph Form */}
                {type === 'paragraph' && (
                    <form onSubmit={handleSubmit} className="space-y-4">
                        <div className="space-y-2">
                            <label className="block text-sm font-medium">Paragraph Text</label>
                            <Textarea
                                value={paragraphData.content}
                                onChange={(e) => setParagraphData({ content: e.target.value })}
                                placeholder="Enter paragraph text"
                                rows={5}
                                required
                            />
                        </div>

                        {renderNarrationSection()}

                        <div className="flex justify-end gap-2">
                            <Button type="button" variant="outline" onClick={onCancel}>
                                Cancel
                            </Button>
                            <Button type="submit" disabled={isSubmitting}>
                                {isSubmitting ? 'Adding...' : 'Add Paragraph'}
                            </Button>
                        </div>
                    </form>
                )}

                {/* Image Form */}
                {type === 'image' && (
                    <form onSubmit={handleSubmit} className="space-y-4">
                        <div className="space-y-2">
                            <label className="block text-sm font-medium">Image</label>
                            <Input
                                type="file"
                                accept="image/*"
                                onChange={(e) => {
                                    const file = e.target.files?.[0] || null;
                                    setImageData({ ...imageData, media: file });
                                }}
                                required
                            />
                        </div>
                        <div className="space-y-2">
                            <label className="block text-sm font-medium">Alt Text</label>
                            <Input
                                value={imageData.alt}
                                onChange={(e) => setImageData({ ...imageData, alt: e.target.value })}
                                placeholder="Describe the image for accessibility"
                                required
                            />
                        </div>

                        {renderNarrationSection()}

                        <div className="flex justify-end gap-2">
                            <Button type="button" variant="outline" onClick={onCancel}>
                                Cancel
                            </Button>
                            <Button type="submit" disabled={isSubmitting}>
                                {isSubmitting ? 'Adding...' : 'Add Image'}
                            </Button>
                        </div>
                    </form>
                )}

                {/* Video Form */}
                {type === 'video' && (
                    <form onSubmit={handleSubmit} className="space-y-4">
                        <div className="space-y-2">
                            <label className="block text-sm font-medium">Video Title</label>
                            <Input
                                value={videoData.title}
                                onChange={(e) => setVideoData({ ...videoData, title: e.target.value })}
                                placeholder="Enter video title"
                                required
                            />
                        </div>
                        <div className="space-y-2">
                            <label className="block text-sm font-medium">Upload Video</label>
                            <Input
                                type="file"
                                accept="video/*"
                                onChange={(e) => {
                                    const file = e.target.files?.[0] || null;
                                    setVideoData({ ...videoData, video: file });
                                }}
                            />
                            <p className="text-xs text-gray-500">Upload your own video file, or use a URL below</p>

                            {videoData.previewUrl && (
                                <div className="mt-2">
                                    <label className="mb-1 block text-sm font-medium">Preview:</label>
                                    <video controls className="h-auto w-full rounded border border-gray-200" src={videoData.previewUrl}></video>
                                </div>
                            )}
                        </div>
                        <div className="space-y-2">
                            <label className="block text-sm font-medium">OR Video URL</label>
                            <Input
                                value={videoData.url}
                                onChange={(e) => setVideoData({ ...videoData, url: e.target.value })}
                                placeholder="Enter YouTube or other video URL"
                            />
                            <p className="text-xs text-gray-500">Either upload a video file above or provide a URL to an external video</p>
                        </div>
                        <div className="flex justify-end gap-2">
                            <Button type="button" variant="outline" onClick={onCancel}>
                                Cancel
                            </Button>
                            <Button type="submit" disabled={isSubmitting}>
                                {isSubmitting ? 'Adding...' : 'Add Video'}
                            </Button>
                        </div>
                    </form>
                )}

                {/* Audio Form */}
                {type === 'audio' && (
                    <form onSubmit={handleSubmit} className="space-y-4">
                        <div className="space-y-2">
                            <label className="block text-sm font-medium">Audio Title</label>
                            <Input
                                value={audioData.title}
                                onChange={(e) => setAudioData({ ...audioData, title: e.target.value })}
                                placeholder="Enter audio title"
                                required
                            />
                        </div>
                        <div className="space-y-2">
                            <label className="block text-sm font-medium">Audio File</label>
                            <Input
                                type="file"
                                accept="audio/*"
                                onChange={(e) => {
                                    const file = e.target.files?.[0] || null;
                                    setAudioData({ ...audioData, audio: file });
                                }}
                                required
                            />

                            {audioData.previewUrl && (
                                <div className="mt-2">
                                    <label className="mb-1 block text-sm font-medium">Preview:</label>
                                    <audio controls className="w-full" src={audioData.previewUrl}></audio>
                                </div>
                            )}
                        </div>
                        <div className="flex justify-end gap-2">
                            <Button type="button" variant="outline" onClick={onCancel}>
                                Cancel
                            </Button>
                            <Button type="submit" disabled={isSubmitting}>
                                {isSubmitting ? 'Adding...' : 'Add Audio'}
                            </Button>
                        </div>
                    </form>
                )}

                {/* List Form */}
                {type === 'list' && (
                    <form onSubmit={handleSubmit} className="space-y-4">
                        <div className="space-y-2">
                            <label className="block text-sm font-medium">List Type</label>
                            <select
                                value={listData.listType}
                                onChange={(e) => setListData({ listType: e.target.value as 'bullet' | 'numbered' })}
                                className="w-full rounded-md border border-gray-300 p-2"
                            >
                                <option value="bullet">Bullet List</option>
                                <option value="numbered">Numbered List</option>
                            </select>
                        </div>
                        <div className="space-y-2">
                            <label className="block text-sm font-medium">List Items</label>
                            {listItems.map((item, index) => (
                                <div key={index} className="flex items-center gap-2">
                                    <Input
                                        value={item}
                                        onChange={(e) => updateListItem(index, e.target.value)}
                                        placeholder={`List item ${index + 1}`}
                                    />
                                    <Button
                                        type="button"
                                        variant="ghost"
                                        size="icon"
                                        onClick={() => removeListItem(index)}
                                        disabled={listItems.length === 1}
                                    >
                                        <X className="h-4 w-4" />
                                    </Button>
                                </div>
                            ))}
                            <Button type="button" variant="outline" size="sm" onClick={addListItem} className="mt-2">
                                <Plus className="mr-2 h-4 w-4" /> Add Item
                            </Button>
                        </div>

                        {renderNarrationSection()}

                        <div className="flex justify-end gap-2">
                            <Button type="button" variant="outline" onClick={onCancel}>
                                Cancel
                            </Button>
                            <Button type="submit" disabled={isSubmitting}>
                                {isSubmitting ? 'Adding...' : 'Add List'}
                            </Button>
                        </div>
                    </form>
                )}

                {/* Zip Form */}
                {type === 'zip' && (
                    <form onSubmit={handleSubmit} className="space-y-4">
                        <div className="space-y-2">
                            <label className="block text-sm font-medium">HTML5 Content Package</label>
                            <Input
                                type="file"
                                accept=".zip"
                                onChange={(e) => {
                                    const file = e.target.files?.[0] || null;
                                    setZipData({ ...zipData, zip: file });
                                }}
                                required
                            />
                            <p className="text-xs text-gray-500">Upload a ZIP file containing your HTML5 content with an index.html file</p>
                        </div>
                        <div className="space-y-2">
                            <label className="block text-sm font-medium">Description (optional)</label>
                            <Input
                                type="text"
                                value={zipData.description}
                                placeholder="Enter a description for this content"
                                onChange={(e) => setZipData({ ...zipData, description: e.target.value })}
                            />
                        </div>
                        <div className="flex justify-end gap-2">
                            <Button type="button" variant="outline" onClick={onCancel}>
                                Cancel
                            </Button>
                            <Button type="submit" disabled={isSubmitting}>
                                {isSubmitting ? 'Adding...' : 'Add HTML5 Content'}
                            </Button>
                        </div>
                    </form>
                )}

                {/* WYSIWYG Form */}
                {type === 'wysiwyg' && (
                    <form onSubmit={handleSubmit} className="space-y-4">
                        <div className="space-y-2">
                            <label className="block text-sm font-medium">Rich Text Content</label>
                            <div className="rounded-lg border border-gray-200 bg-white">
                                <div className="border-b border-gray-200 bg-purple-50 p-3">
                                    <p className="text-sm font-medium text-purple-700">✨ Rich Text Editor</p>
                                    <p className="mt-1 text-xs text-purple-600">Create formatted content with headings, lists, links, and styling</p>
                                </div>
                                <div className="p-0">
                                    {/* Temporarily using a placeholder until import is resolved */}
                                    <div className="min-h-[300px] rounded border border-gray-200 p-4">
                                        <WysiwygEditor
                                            content={wysiwygData.content}
                                            onChange={(content) => setWysiwygData({ ...wysiwygData, content })}
                                            placeholder="Start writing your content..."
                                        />
                                    </div>
                                </div>
                            </div>
                            <p className="text-xs text-gray-500">
                                Use the toolbar above to format your text with headings, bold, italic, lists, links, and more.
                            </p>
                        </div>

                        {renderNarrationSection()}

                        <div className="flex justify-end gap-2">
                            <Button type="button" variant="outline" onClick={onCancel}>
                                Cancel
                            </Button>
                            <Button type="submit" disabled={isSubmitting}>
                                {isSubmitting ? 'Adding...' : 'Add Rich Text Content'}
                            </Button>
                        </div>
                    </form>
                )}
            </DialogContent>
        </Dialog>
    );
};

const SortableContentBlock = ({ contentBlock }: { contentBlock: ContentBlock }) => {
    const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({
        id: contentBlock.id,
    });
    const [isConfirmingDelete, setIsConfirmingDelete] = useState(false);

    const style = {
        transform: CSS.Transform.toString(transform),
        transition,
        zIndex: isDragging ? 1 : 0,
    };

    const handleDelete = async () => {
        try {
            await axios.delete(route('content-blocks.destroy', { content_block: contentBlock.id }));
            router.reload();
        } catch (error) {
            console.error('Error deleting content block:', error);
        }
    };

    // Render content based on type
    const renderContent = () => {
        switch (contentBlock.type) {
            case 'heading': {
                const level = contentBlock.attributes?.level || 'h2';
                return (
                    <div className="text-xs text-gray-500">
                        <span className="font-semibold">{level.toUpperCase()}: </span>
                        {contentBlock.content}
                        {contentBlock.audio_path && (
                            <span className="ml-2 flex items-center text-blue-500">
                                <Volume2 className="mr-1 h-3 w-3" /> Has narration
                            </span>
                        )}
                    </div>
                );
            }
            case 'paragraph':
                return (
                    <div className="text-xs text-gray-500">
                        <p>
                            {contentBlock.content && contentBlock.content.length > 50
                                ? contentBlock.content.substring(0, 50) + '...'
                                : contentBlock.content}
                        </p>
                        {contentBlock.audio_path && (
                            <span className="mt-1 flex items-center text-blue-500">
                                <Volume2 className="mr-1 h-3 w-3" /> Has narration
                            </span>
                        )}
                    </div>
                );
            case 'image':
                return (
                    <div className="text-xs text-gray-500">
                        <span className="font-semibold">Image: </span>
                        {contentBlock.content || 'No alt text'}
                        {contentBlock.media_path && <span className="ml-1 text-blue-500">[Has image]</span>}
                        {contentBlock.audio_path && (
                            <span className="ml-2 flex items-center text-blue-500">
                                <Volume2 className="mr-1 h-3 w-3" /> Has narration
                            </span>
                        )}
                    </div>
                );
            case 'video': {
                const videoUrl = contentBlock.attributes?.url || '';
                const videoPath = contentBlock.attributes?.video_path;
                return (
                    <div className="text-xs text-gray-500">
                        <span className="font-semibold">Video: </span>
                        {contentBlock.content}
                        {videoUrl && <span className="ml-1 text-blue-500">[External URL]</span>}
                        {typeof videoPath === 'string' && videoPath && <span className="ml-1 text-blue-500">[Uploaded video]</span>}
                    </div>
                );
            }
            case 'audio':
                return (
                    <div className="text-xs text-gray-500">
                        <span className="font-semibold">Audio: </span>
                        {contentBlock.content}
                        {contentBlock.audio_path && <span className="ml-1 text-blue-500">[Has audio]</span>}
                    </div>
                );
            case 'list': {
                const items = contentBlock.attributes?.items || [];
                return (
                    <div className="text-xs text-gray-500">
                        <span className="font-semibold">{contentBlock.attributes?.listType === 'numbered' ? 'Numbered List' : 'Bullet List'}:</span>
                        <span className="ml-1">{items.length} items</span>
                        {contentBlock.audio_path && (
                            <span className="ml-2 flex items-center text-blue-500">
                                <Volume2 className="mr-1 h-3 w-3" /> Has narration
                            </span>
                        )}
                    </div>
                );
            }
            case 'zip':
                return (
                    <div className="text-xs text-gray-500">
                        <span className="font-semibold">HTML5 Content: </span>
                        {contentBlock.content || 'Interactive content'}
                        {contentBlock.attributes?.index_path && (
                            <div className="mt-2">
                                <iframe
                                    src={`/storage/${contentBlock.attributes.index_path}`}
                                    className="h-[200px] w-full rounded border border-gray-200"
                                    title="HTML5 Content Preview"
                                    sandbox="allow-scripts allow-same-origin"
                                ></iframe>
                            </div>
                        )}
                    </div>
                );
            case 'wysiwyg': {
                const htmlContent = contentBlock.attributes?.html_content || '';
                // Strip HTML tags for preview and limit length
                const textContent = htmlContent.replace(/<[^>]*>/g, '').trim();
                return (
                    <div className="text-xs text-gray-500">
                        <span className="font-semibold">Rich Text: </span>
                        <span>{textContent.length > 50 ? textContent.substring(0, 50) + '...' : textContent || 'No content'}</span>
                        {contentBlock.audio_path && (
                            <span className="ml-2 flex items-center text-blue-500">
                                <Volume2 className="mr-1 h-3 w-3" /> Has narration
                            </span>
                        )}
                        <div className="mt-2 text-xs text-purple-600">
                            <span className="rounded bg-purple-100 px-2 py-1">Rich formatted content</span>
                        </div>
                    </div>
                );
            }
            default:
                return (
                    <p className="text-xs text-gray-500">
                        {contentBlock.content && contentBlock.content.length > 50
                            ? contentBlock.content.substring(0, 50) + '...'
                            : contentBlock.content || 'No content'}
                    </p>
                );
        }
    };

    return (
        <div
            ref={setNodeRef}
            style={style}
            {...attributes}
            {...listeners}
            className="mb-4 rounded-lg border border-gray-200 bg-white p-4 shadow-sm transition-all hover:border-gray-300"
        >
            <div className="flex items-center justify-between">
                <div className="flex items-center">
                    <div className="mr-4 h-10 w-10 flex-shrink-0 rounded-md bg-gray-100 p-2 text-center">
                        {contentBlock.type.charAt(0).toUpperCase()}
                    </div>
                    <div>
                        <h3 className="text-sm font-medium text-gray-900">
                            {contentBlock.type.charAt(0).toUpperCase() + contentBlock.type.slice(1)}
                        </h3>
                        {renderContent()}
                    </div>
                </div>
                <DropdownMenu>
                    <DropdownMenuTrigger className="cursor-pointer rounded-md p-1 text-gray-400 hover:bg-gray-100">
                        <MoreHorizontal className="h-5 w-5" />
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                        <DropdownMenuItem className="cursor-pointer" onClick={() => router.visit(`/content-blocks/${contentBlock.id}/edit`)}>
                            <Edit className="mr-2 h-4 w-4" />
                            Edit
                        </DropdownMenuItem>
                        <DropdownMenuItem
                            variant="destructive"
                            className="cursor-pointer text-red-600 focus:text-red-600"
                            onClick={() => setIsConfirmingDelete(true)}
                        >
                            <Trash className="mr-2 h-4 w-4" />
                            Delete
                        </DropdownMenuItem>
                    </DropdownMenuContent>
                </DropdownMenu>
            </div>

            {/* Delete Confirmation Dialog */}
            {isConfirmingDelete && (
                <Dialog open={isConfirmingDelete} onOpenChange={setIsConfirmingDelete}>
                    <DialogContent className="sm:max-w-md">
                        <DialogHeader>
                            <DialogTitle>Delete Content Block</DialogTitle>
                        </DialogHeader>
                        <div className="py-4">
                            <p>Are you sure you want to delete this content block? This action cannot be undone.</p>
                        </div>
                        <div className="flex justify-end gap-2">
                            <Button variant="outline" onClick={() => setIsConfirmingDelete(false)}>
                                Cancel
                            </Button>
                            <Button variant="destructive" onClick={handleDelete}>
                                Delete
                            </Button>
                        </div>
                    </DialogContent>
                </Dialog>
            )}
        </div>
    );
};

function Content({ topic, contentBlocks }: PageProps) {
    const pageProps = usePage().props;
    const unit = pageProps.unit as { id: number };
    const subject = pageProps.subject as { id: number };
    const classObj = pageProps.class as { id: number };
    const [sortedBlocks, setSortedBlocks] = useState<ContentBlock[]>(contentBlocks);
    const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
    const [selectedContentType, setSelectedContentType] = useState<string | null>(null);

    // Set up sensors for drag and drop
    const sensors = useSensors(
        useSensor(PointerSensor, {
            activationConstraint: {
                distance: 8,
            },
        }),
        useSensor(KeyboardSensor, {
            coordinateGetter: sortableKeyboardCoordinates,
        }),
    );

    useEffect(() => {
        setSortedBlocks(contentBlocks);
    }, [contentBlocks]);

    const handleDragEnd = async (event: DragEndEvent) => {
        const { active, over } = event;

        if (over && active.id !== over.id) {
            setSortedBlocks((items) => {
                const oldIndex = items.findIndex((item) => item.id === active.id);
                const newIndex = items.findIndex((item) => item.id === over.id);

                const reordered = arrayMove(items, oldIndex, newIndex);

                // Update order values
                const updatedItems = reordered.map((item, index) => ({
                    ...item,
                    order: index + 1,
                }));

                // Save the new order to the server
                saveOrder(updatedItems);

                return updatedItems;
            });
        }
    };

    const saveOrder = async (items: ContentBlock[]) => {
        try {
            await axios.post(route('content-blocks.reorder'), {
                blocks: items.map((item) => ({ id: item.id, order: item.order, topic_id: topic.id })),
            });
        } catch (error) {
            console.error('Error saving content block order:', error);
        }
    };

    // Content type options for the create dialog
    const contentTypes = [
        { id: 'heading', name: 'Heading', description: 'Add a section title or subtitle' },
        { id: 'paragraph', name: 'Paragraph', description: 'Add text content or explanations' },
        { id: 'wysiwyg', name: 'Rich Text', description: 'Create formatted content with headings, lists, links, and styling' },
        { id: 'image', name: 'Image', description: 'Upload and display an image' },
        { id: 'video', name: 'Video', description: 'Embed a video from URL or upload' },
        { id: 'audio', name: 'Audio', description: 'Add audio content or narration' },
        { id: 'list', name: 'List', description: 'Create a bullet or numbered list' },
        { id: 'zip', name: 'HTML5 Content', description: 'Upload a zip file containing your HTML5 content with an index.html file' },
    ];

    const handleContentTypeSelect = (typeId: string) => {
        setIsCreateDialogOpen(false);
        setSelectedContentType(typeId);
    };

    const handleContentAddSuccess = () => {
        setSelectedContentType(null);
        router.reload();
    };

    return (
        <div>
            <Head title={`Content for ${topic.name}`} />
            <div className="mx-auto w-full max-w-5xl px-4 py-8 sm:px-6 lg:px-8">
                <div className="mb-6 flex items-center justify-between">
                    <div className="flex items-center gap-4">
                        <Button
                            variant="outline"
                            size="icon"
                            onClick={() =>
                                router.visit(route('topics.index', { class_id: classObj?.id, subject_id: subject?.id, unit_id: unit?.id }))
                            }
                        >
                            <ChevronLeft className="h-5 w-5" />
                        </Button>
                        <div>
                            <h1 className="text-2xl font-bold text-gray-900">{topic.name}</h1>
                            <p className="text-sm text-gray-500">Manage content blocks for this topic</p>
                        </div>
                    </div>
                    <div className="flex items-center gap-2">
                        <Button variant="outline" onClick={() => router.visit(route('topics.preview', { topic_id: topic.id }))}>
                            Preview Lesson
                        </Button>
                        <Button onClick={() => setIsCreateDialogOpen(true)}>
                            <Plus className="mr-2 h-4 w-4" /> Add Content Block
                        </Button>
                    </div>
                </div>

                <Separator className="my-6" />

                {sortedBlocks.length > 0 ? (
                    <DndContext sensors={sensors} collisionDetection={closestCenter} onDragEnd={handleDragEnd}>
                        <SortableContext items={sortedBlocks.map((block) => block.id)} strategy={verticalListSortingStrategy}>
                            {sortedBlocks.map((block) => (
                                <SortableContentBlock key={block.id} contentBlock={block} />
                            ))}
                        </SortableContext>
                    </DndContext>
                ) : (
                    <div className="flex flex-col items-center justify-center rounded-lg border-2 border-dashed border-gray-300 p-12 text-center">
                        <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M9 13h6m-3-3v6m5 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                            />
                        </svg>
                        <h3 className="mt-2 text-sm font-medium text-gray-900">No content blocks</h3>
                        <p className="mt-1 text-sm text-gray-500">Get started by adding your first content block.</p>
                        <div className="mt-6">
                            <Button onClick={() => setIsCreateDialogOpen(true)}>
                                <Plus className="mr-2 h-4 w-4" /> Add Content Block
                            </Button>
                        </div>
                    </div>
                )}
            </div>

            {/* Content Type Selection Dialog */}
            {isCreateDialogOpen && (
                <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
                    <DialogContent className="sm:max-w-md">
                        <DialogHeader>
                            <DialogTitle>Add Content Block</DialogTitle>
                        </DialogHeader>
                        <div className="mt-4">
                            <p className="mb-4 text-sm text-gray-500">Select the type of content block you want to add:</p>
                            <div className="grid grid-cols-2 gap-4">
                                {contentTypes.map((type) => (
                                    <div
                                        key={type.id}
                                        className="cursor-pointer rounded-lg border border-gray-200 p-4 hover:border-blue-500"
                                        onClick={() => handleContentTypeSelect(type.id)}
                                    >
                                        <h3 className="font-medium text-gray-900">{type.name}</h3>
                                        <p className="mt-1 text-xs text-gray-500">{type.description}</p>
                                    </div>
                                ))}
                            </div>
                        </div>
                    </DialogContent>
                </Dialog>
            )}

            {/* Type-specific content form */}
            {selectedContentType && (
                <ContentTypeForm
                    type={selectedContentType}
                    topicId={topic.id}
                    onCancel={() => setSelectedContentType(null)}
                    onSuccess={handleContentAddSuccess}
                />
            )}
        </div>
    );
}

export default Content;
