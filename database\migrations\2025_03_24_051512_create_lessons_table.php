<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('lessons', function (Blueprint $table) {
            $table->id();
            $table->foreignId('topic_id')->constrained('topics')->onDelete('cascade');
            $table->string('type'); // heading, paragraph, image, video, audio, list, listItem
            $table->text('content')->nullable(); // For text-based content
            $table->json('media_path')->nullable(); // For images, videos, audio files
            $table->string('audio_path')->nullable(); // For optional audio narration
            $table->integer('order')->default(0); // For ordering lessons
            $table->foreignId('parent_id')->nullable()->constrained('lessons')->onDelete('cascade'); // For nested structures like lists
            $table->json('attributes')->nullable(); // For additional attributes like heading level, etc.
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('lessons');
    }
};