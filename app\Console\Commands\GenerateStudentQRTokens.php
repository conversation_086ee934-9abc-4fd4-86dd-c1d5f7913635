<?php

namespace App\Console\Commands;

use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Str;

class GenerateStudentQRTokens extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'students:generate-qr-tokens';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate QR code tokens for students who do not have them';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Generating QR tokens for students...');

        $studentsWithoutQR = User::where('role', 'student')
            ->whereNull('qr_code_token')
            ->get();

        if ($studentsWithoutQR->isEmpty()) {
            $this->info('All students already have QR tokens.');
            return;
        }

        $count = 0;
        foreach ($studentsWithoutQR as $student) {
            // Generate a unique QR code token
            $qrCodeToken = null;
            do {
                $qrCodeToken = 'STU_' . Str::random(32);
                $tokenExists = User::where('qr_code_token', $qrCodeToken)->exists();
            } while ($tokenExists);

            $student->update(['qr_code_token' => $qrCodeToken]);
            $count++;

            $this->line("Generated QR token for: {$student->name} (ID: {$student->id})");
        }

        $this->info("Successfully generated QR tokens for {$count} students.");
    }
}
