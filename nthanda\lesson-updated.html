<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Fun Learning Adventure!</title>
        <link rel="stylesheet" href="styles.css">
        <style>
            :root {
                --color-blue: #1c407c;
                --color-yellow: #ffd93d;
                --color-dark-yellow: #e6c235;
                --color-white: #ffffff;
                --color-red: #ff5252;
                --color-green: #4caf50;
                --color-dark-green: #388e3c;
                --color-orange: #FF9800;
                --color-purple: #9C27B0;
                --color-gray: #F5F5F5;
                --color-text-gray: #666;
            }

            * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
            }

            body {
                background-color: var(--color-gray);
                margin: 0;
                padding: 0;
                font-family: "Fredoka", sans-serif;
                color: var(--color-blue);
            }

            

            .content-block {
                background: white;
                border-radius: 16px;
                padding: 20px;
                margin-bottom: 20px;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
                transition: transform 0.3s ease;
            }

            .content-block:hover {
                transform: translateY(-5px);
            }

            .heading {
                color: var(--color-blue);
                font-size: 20px;
                font-weight: 600;
                margin-bottom: 15px;
                border-bottom: 2px solid var(--color-yellow);
                padding-bottom: 10px;
            }

            .paragraph {
                font-size: 16px;
                line-height: 1.6;
                color: var(--color-text-gray);
                margin-bottom: 15px;
            }

            .image-container {
                text-align: center;
                margin: 10px 0;
            }

            .lesson-image {
                max-width: 100%;
                border-radius: 12px;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
                object-fit: cover;
            }

            .video-container {
                width: 100%;
                border-radius: 16px;
                overflow: hidden;
                margin-bottom: 16px;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            }

            .video-container video {
                width: 100%;
                display: block;
                border-radius: 16px;
            }

            .list-container {
                background: white;
                padding: 10px;
                border-radius: 10px;
                margin: 10px 0;
            }

            .list-item {
                margin: 10px 0;
                padding-left: 25px;
                position: relative;
                font-size: 16px;
                color: var(--color-text-gray);
            }

            .list-item::before {
                content: "🌟";
                position: absolute;
                left: 0;
                color: var(--color-orange);
            }

            .numbered-list-item {
                margin: 10px 0;
                padding-left: 25px;
                position: relative;
                font-size: 16px;
                color: var(--color-text-gray);
            }

            .numbered-list-item::before {
                position: absolute;
                left: 0;
                font-weight: bold;
                color: var(--color-orange);
            }

            .numbered-list-item:nth-child(1)::before { content: "1"; }
            .numbered-list-item:nth-child(2)::before { content: "2"; }
            .numbered-list-item:nth-child(3)::before { content: "3"; }
            .numbered-list-item:nth-child(4)::before { content: "4"; }

            .narration-button {
                background: var(--color-blue);
                color: white;
                border: none;
                padding: 10px;
                border-radius: 25px;
                cursor: pointer;
                font-family: "Fredoka", sans-serif;
                font-size: 14px;
                display: flex;
                align-items: center;
                gap: 10px;
                margin: 10px 0;
                transition: transform 0.2s ease;
            }

            .narration-button:hover {
                transform: scale(1.05);
            }

            .narration-button i {
                font-size: 1.2em;
            }

            .progress-bar {
                position: fixed;
                top: 0;
                left: 0;
                height: 5px;
                background: var(--color-green);
                transition: width 0.3s ease;
                z-index: 1000;
            }

            .audio-block {
                display: flex;
                align-items: center;
                background: var(--color-gray);
                border-radius: 16px;
                padding: 16px;
                margin-bottom: 16px;
            }

            .audio-title {
                flex-grow: 1;
                font-weight: 500;
                margin-left: 12px;
                font-size: 16px;
                color: var(--color-blue);
            }

            .play-button {
                background: var(--color-blue);
                color: white;
                border: none;
                border-radius: 50%;
                width: 48px;
                height: 48px;
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                font-size: 1.5em;
                transition: transform 0.2s ease;
            }

            .play-button:hover {
                transform: scale(1.1);
            }

            @keyframes bounce {
                0%, 100% { transform: translateY(0); }
                50% { transform: translateY(-5px); }
            }

            .floating-elements {
                position: fixed;
                width: 100%;
                height: 100%;
                pointer-events: none;
                z-index: 1000;
            }

            .floating-element {
                position: absolute;
                font-size: 2em;
                animation: float 6s infinite;
            }

            @keyframes float {
                0% { transform: translateY(0) rotate(0deg); }
                50% { transform: translateY(-20px) rotate(10deg); }
                100% { transform: translateY(0) rotate(0deg); }
            }

            .achievement-badge {
                position: fixed;
                bottom: 20px;
                right: 20px;
                background: var(--color-yellow);
                padding: 15px;
                border-radius: 50%;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
                animation: popIn 0.5s ease;
                color: var(--color-blue);
            }

            @keyframes popIn {
                0% { transform: scale(0); }
                50% { transform: scale(1.2); }
                100% { transform: scale(1); }
            }

            .back-button {
                margin-right: 16px;
                background-color: var(--color-blue);
                width: 40px;
                height: 40px;
                border-radius: 20px;
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                margin: 16px 0;
                border: none;
            }

            .back-button:hover {
                transform: scale(1.05);
            }

            .content {
                padding: 16px;
                padding-top: 0px;
            }

            .lesson-header-container {
                display: flex;
                padding: 16px;
                gap: 16px;
                background-color: var(--color-yellow);
                border-bottom-left-radius: 24px;
                border-bottom-right-radius: 24px;
                margin-bottom: 12px;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
            }

            .lesson-container {
                max-width: 800px;
                margin: 0 auto;
                padding: 0;
                position: relative;
                
            }

            .lesson-header {
                text-align: center;
                animation: bounce 2s infinite;
                position: relative;
            }

            .lesson-title {
                color: var(--color-blue);
                font-weight: 700;
                font-size: 28px;
                margin: 0;
                text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
            }

            .lesson-subtitle {
                color: var(--color-text-gray);
                font-weight: 400;
                font-size: 16px;
                margin-top: 8px;
                opacity: 0.9;
            }
        </style>
    </head>
    <body>
        <div class="progress-bar" id="progressBar"></div>
        <div class="lesson-container">
            <div class="content">

                <div class="lesson-header-container">
                    <div class="back-button" onclick="goBack()">
                        <img src="assets/icons/arrowleft.png" alt="Back"
                            style="width: 24px; height: 24px;">
                    </div>
                    <div class="lesson-header">
                        <div class="lesson-icon">📖</div>
                        <h1 class="lesson-title">Interactive Lesson</h1>
                        <p class="lesson-subtitle">Explore and learn with fun
                            activities!</p>
                    </div>
                </div>

                <div id="contentBlocks">
                    <!-- Heading (ID: 1) -->
                    <div class="content-block">
                        <h2 class="heading">Heading</h2>
                    </div>

                    <!-- Paragraph (ID: 5) -->
                    <div class="content-block">
                        <p class="paragraph">Displays a form textarea or a
                            component that looks like a textarea.</p>
                        <button class="narration-button"
                            onclick="playAudio('content-blocks/audio/u10Qn5GOw072ifvtaG87tkpQb2Qmh4auE0IIXcSL.mp3')">
                            <i>🔊</i>
                        </button>
                    </div>

                    <!-- Image (ID: 4) -->
                    <div class="content-block">
                        <div class="image-container">
                            <img
                                src="content-blocks/media/tVTtp3wyPlrXaYBjcASRtrFLTgxhasmdUqbcFOnk.png"
                                alt="image" class="lesson-image">
                        </div>
                    </div>

                    <!-- Paragraph (ID: 6) -->
                    <div class="content-block">
                        <p class="paragraph">You know how most traditional
                            component libraries work: you install a package from
                            NPM, import the components, and use them in your
                            app.</p>
                    </div>

                    <!-- List (ID: 3) - Bullet -->
                    <div class="content-block">
                        <ul class="list-container">
                            <li class="list-item">Mphatso</li>
                            <li class="list-item">Wezzie</li>
                            <li class="list-item">Zaithwa</li>
                            <li class="list-item">PKay</li>
                        </ul>
                    </div>

                    <!-- Audio (ID: 7) -->
                    <div class="content-block">
                        <div class="audio-block">
                            <button class="play-button"
                                onclick="playAudio('content-blocks/audio/Wtm3yQcAyv18l3GVu1GYcbnAFVlduTsKHHEOY4Tr.mp3')">▶</button>
                            <div class="audio-title">Audio beats</div>
                        </div>
                    </div>

                    <!-- Video (ID: 8) -->
                    <div class="content-block">
                        <h3 class="heading">Nthano</h3>
                        <div class="video-container">
                            <video controls>
                                <source
                                    src="content-blocks/videos/i8upFvll6ddLA59lPRA8d8S4AazFnvbzTbrvZfjx.mp4"
                                    type="video/mp4">
                                Your browser does not support the video tag.
                            </video>
                        </div>
                    </div>

                    <!-- Paragraph (ID: 9) -->
                    <div class="content-block">
                        <p class="paragraph">This is what shadcn/ui aims to
                            solve. It is built around the following
                            principles:</p>
                    </div>

                    <!-- List (ID: 10) - Numbered -->
                    <div class="content-block">
                        <ol class="list-container">
                            <li class="numbered-list-item">Open Code: The top
                                layer of your component code is open for
                                modification.</li>
                            <li class="numbered-list-item">Composition: Every
                                component uses a common, composable interface,
                                making them predictable.</li>
                            <li class="numbered-list-item">Distribution: A
                                flat-file schema and command-line tool make it
                                easy to distribute components.</li>
                            <li class="numbered-list-item">Beautiful Defaults:
                                Carefully chosen default styles, so you get
                                great design out-of-the-box.</li>
                        </ol>
                    </div>
                </div>

                <!-- Complete Button -->
                <button class="complete-button" onclick="completeLesson()"
                    style="margin: 20px 0; border-radius: 30px; padding: 12px 0; background-color: var(--color-green); color: white; font-weight: 600; font-size: 18px; text-align: center; cursor: pointer; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2); border: none; width: 100%;">
                    I Did It! 🎉
                </button>

                <div class="achievement-badge" id="achievementBadge"
                    style="display: none;">
                    🏆 Great job!
                </div>
            </div>
        </div>

        <script>
            // Initialize floating elements
            function initializeFloatingElements() {
                const floatingElements = document.getElementById('floatingElements');
                const emojis = ['🌟', '🎈', '🎨', '📚', '🎯', '🎪', '🎭', '🎪'];
                for (let i = 0; i < 10; i++) {
                    const element = document.createElement('div');
                    element.className = 'floating-element';
                    element.textContent = emojis[Math.floor(Math.random() * emojis.length)];
                    element.style.left = `${Math.random() * 100}%`;
                    element.style.top = `${Math.random() * 100}%`;
                    element.style.animationDelay = `${Math.random() * 5}s`;
                    floatingElements.appendChild(element);
                }
            }

            // Progress bar
            function initializeProgressBar() {
                setTimeout(function() {
                    document.getElementById('progressBar').style.width = '0%';
                }, 300);
                
                window.addEventListener('scroll', () => {
                    const winScroll = document.body.scrollTop || document.documentElement.scrollTop;
                    const height = document.documentElement.scrollHeight - document.documentElement.clientHeight;
                    const scrolled = (winScroll / height) * 100;
                    document.getElementById('progressBar').style.width = scrolled + '%';
                });
            }

            // Achievement badge
            function initializeAchievementBadge() {
                let scrollCount = 0;
                window.addEventListener('scroll', () => {
                    scrollCount++;
                    if (scrollCount > 5) {
                        const badge = document.getElementById('achievementBadge');
                        badge.style.display = 'block';
                        setTimeout(() => {
                            badge.style.display = 'none';
                        }, 3000);
                    }
                });
            }

            // Audio playback
            function playAudio(audioId) {
                // In a real implementation, this would play the audio
                alert('Playing audio: ' + audioId);
            }

            // Go back function
            function goBack() {
                window.history.back();
            }
            
            function completeLesson() {
                // Update progress bar
                document.getElementById('progressBar').style.width = '100%';
                
                // Wait 2 seconds and go back
                setTimeout(function() {
                    goBack();
                }, 2000);
            }

            // Initialize everything when the page loads
            window.addEventListener('load', () => {
                initializeFloatingElements();
                initializeProgressBar();
                initializeAchievementBadge();
            });
        </script>
    </body>
</html>