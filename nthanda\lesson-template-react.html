<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Lesson Preview</title>
        <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');
        
        :root {
            --color-blue-50: #eff6ff;
            --color-blue-600: #2563eb;
            --color-blue: #1c407c;
            --color-yellow: #ffd93d;
            --color-dark-yellow: #e6c235;
            --color-white: #ffffff;
            --color-red: #ff5252;
            --color-green: #4caf50;
            --color-dark-green: #388e3c;
            --color-orange: #FF9800;
            --color-purple: #9C27B0;
            --color-pink: #EC407A;
            --color-teal: #26A69A;
            --color-lime: #C0CA33;
            --color-gray-100: #f3f4f6;
            --color-gray-500: #6b7280;
            --color-gray-700: #374151;
            --color-gray-900: #111827;
        }
        
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f9fafb;
            color: #111827;
            line-height: 1.5;
            background-image: linear-gradient(45deg, rgba(255,217,61,0.1) 25%, transparent 25%, transparent 75%, rgba(255,217,61,0.1) 75%),
                              linear-gradient(45deg, rgba(255,217,61,0.1) 25%, transparent 25%, transparent 75%, rgba(255,217,61,0.1) 75%);
            background-size: 60px 60px;
            background-position: 0 0, 30px 30px;
        }
        
        .separator {
            height: 1px;
            background-color: var(--color-gray-100);
            width: 100%;
            margin: 1.5rem 0;
        }
        
        .button {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            border-radius: 0.375rem;
            font-weight: 500;
            transition: all 0.2s;
            cursor: pointer;
        }
        
        .button-outline {
            background-color: transparent;
            border: 1px solid #e5e7eb;
            color: var(--color-gray-700);
            padding: 0.5rem 1rem;
        }
        
        .button-outline:hover {
            background-color: #f9fafb;
        }
        
        .button-icon {
            padding: 0.5rem;
        }
        
        .print-container {
            max-width: 1024px;
            width: 100%;
            margin: 0 auto;
            padding: 2rem 1rem;
        }
        
        .header-container {
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .header-content {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .breadcrumb {
            font-size: 0.875rem;
            color: var(--color-gray-500);
            margin-bottom: 0.25rem;
        }
        
        .topic-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--color-gray-900);
        }
        
        .print-header {
            margin-bottom: 2rem;
            display: none;
        }
        
        .print-content {
            border: 1px solid #e5e7eb;
            border-radius: 0.5rem;
            background-color: white;
            padding: 1.5rem;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
        }
        
        .content-blocks {
            border-top: 1px solid var(--color-gray-100);
        }
        
        .print-button-container {
            margin-top: 2rem;
            text-align: center;
        }
        
        .narration-button {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            border-radius: 9999px;
            background-color: var(--color-blue-600);
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
            color: var(--color-white);
            cursor: pointer;
            border: none;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
            transition: transform 0.2s ease, background-color 0.2s ease;
        }
        
        .narration-button:hover {
            transform: scale(1.05);
            background-color: var(--color-orange);
        }
        
        .content-block {
            margin-bottom: 0.5rem;
            padding-bottom: 0.5rem;
            border-bottom: none;
            transition: transform 0.2s ease;
        }
        
        .content-block:hover {
            transform: translateY(-2px);
        }
        
        .content-block:last-child {
            margin-bottom: 0;
        }
        
        .heading-h1 {
            font-size: 1.875rem;
            font-weight: 700;
            margin-bottom: 0.75rem;
            color: var(--color-blue);
            text-shadow: 1px 1px 0 rgba(0,0,0,0.1);
            padding-bottom: 0.5rem;
            border-bottom: 2px dashed var(--color-yellow);
        }
        
        .heading-h2 {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 0.75rem;
            color: var(--color-blue);
            text-shadow: 1px 1px 0 rgba(0,0,0,0.1);
            padding-bottom: 0.5rem;
            border-bottom: 2px dashed var(--color-yellow);
        }
        
        .heading-h3 {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 0.75rem;
            color: var(--color-blue);
            text-shadow: 1px 1px 0 rgba(0,0,0,0.1);
        }
        
        .heading-h4 {
            font-size: 1.125rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }
        
        .heading-h5 {
            font-size: 1rem;
            font-weight: 500;
            margin-bottom: 0.5rem;
        }
        
        .heading-h6 {
            font-size: 0.875rem;
            font-weight: 500;
            margin-bottom: 0.5rem;
        }
        
        .paragraph {
            font-size: 1rem;
            line-height: 1.75;
            color: var(--color-gray-700);
            margin-bottom: 1rem;
        }
        
        .image-container {
            padding: 0.75rem;
            background-color: white;
            border-radius: 0.75rem;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            border: 2px solid var(--color-yellow);
            margin-bottom: 1.5rem;
        }
        
        .image {
            max-width: 100%;
            max-height: 24rem;
            margin: 0 auto;
            display: block;
            border-radius: 0.5rem;
        }
        
        .image-caption {
            margin-top: 0.5rem;
            text-align: center;
            font-size: 0.875rem;
            color: var(--color-gray-500);
        }
        
        .video-title {
            margin-bottom: 0.5rem;
            font-size: 1.125rem;
            font-weight: 500;
        }
        
        .video-container {
            padding: 0.75rem;
            background-color: white;
            border-radius: 0.75rem;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            border: 2px solid var(--color-blue);
            width: 100%;
            max-width: 42rem;
            margin: 0 auto 1.5rem;
        }
        
        .video {
            width: 100%;
            border-radius: 0.5rem;
        }
        
        .iframe-container {
            position: relative;
            width: 100%;
            max-width: 42rem;
            margin: 0 auto;
            padding-bottom: 56.25%; /* 16:9 Aspect Ratio */
            overflow: hidden;
            border-radius: 0.5rem;
        }
        
        .iframe-container iframe {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border: 0;
        }
        
        .audio-title {
            margin-bottom: 0.5rem;
            font-size: 1.125rem;
            font-weight: 500;
        }
        
        .audio-player {
            width: 100%;
            max-width: 28rem;
        }
        
        .list-container {
            margin-bottom: 1rem;
        }
        
        .numbered-list {
            list-style-type: none;
            counter-reset: my-counter;
            padding-left: 0;
        }
        
        .numbered-list li {
            position: relative;
            margin-bottom: 1rem;
            padding-left: 2.5rem;
            counter-increment: my-counter;
        }
        
        .numbered-list li::before {
            content: counter(my-counter);
            position: absolute;
            left: 0;
            top: -2px;
            width: 28px;
            height: 28px;
            background-color: #ffd93d;
            border-radius: 50%;
            color: #2563eb;
            font-weight: bold;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            box-shadow: 1px 1px 3px rgba(0,0,0,0.2);
        }
        
        .bullet-list {
            list-style-type: none;
            padding-left: 0;
        }
        
        .bullet-list li {
            position: relative;
            margin-bottom: 1rem;
            padding-left: 2rem;
        }
        
        .bullet-list li::before {
            content: "";
            position: absolute;
            left: 0;
            top: 0.25rem;
            width: 18px;
            height: 18px;
            background-color: #FF9800;
            border-radius: 50%;
            box-shadow: 1px 1px 3px rgba(0,0,0,0.2);
        }
        
        .list-item {
            margin-bottom: 0.5rem;
            line-height: 1.5;
        }
        
        .narration-container {
            margin-top: 0.5rem;
        }
        
        .hidden {
            display: none;
        }
        
        .empty-message {
            padding: 3rem 0;
            text-align: center;
        }
        
        .empty-text {
            color: var(--color-gray-500);
        }
        
        @media print {
            @page {
                margin: 1cm;
            }
            body {
                font-size: 12pt;
                line-height: 1.5;
            }
            .no-print {
                display: none !important;
            }
            .print-container {
                margin: 0;
                padding: 0;
                width: 100%;
            }
            .print-content {
                border: none;
                box-shadow: none;
                padding: 0;
            }
            .print-header {
                display: block;
            }
            video, audio, iframe {
                display: none;
            }
            img {
                max-width: 100%;
                height: auto;
            }
        }
    </style>
    </head>
    <body>
        <div class="print-container">
            <div class="header-container no-print">
                <div class="header-content">
                    <button class="button button-outline button-icon"
                        onclick="goBack()">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20"
                            height="20" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2"
                            stroke-linecap="round" stroke-linejoin="round">
                            <path d="m15 18-6-6 6-6" />
                        </svg>
                    </button>
                    <div>
                        <p class="breadcrumb">
                            <span id="class-name">Standard 4</span> /
                            <span id="subject-name">Science</span> /
                            <span id="unit-name">Unit 1</span>
                        </p>
                        <h1 class="topic-title" id="topic-name">Lesson Topic
                            Name</h1>
                    </div>
                </div>
                <button class="button button-outline no-print"
                    onclick="goBack()">
                    Back to Editor
                </button>
            </div>

            <div class="separator no-print"></div>

            <!-- Print header -->
            <div class="print-header">
                <p class="breadcrumb">
                    <span id="print-class-name">Standard 4</span> /
                    <span id="print-subject-name">Science</span> /
                    <span id="print-unit-name">Unit 1</span>
                </p>
                <h1 class="topic-title" id="print-topic-name">Lesson Topic
                    Name</h1>
            </div>

            <div class="print-content">
                <div class="content-blocks" id="content-blocks-container">
                    <!-- Content blocks will be inserted here -->
                </div>
            </div>

            <div class="print-button-container no-print">
                <button class="button button-outline" onclick="window.print()">
                    Print Lesson
                </button>
            </div>
        </div>

        <script>
        // Sample data - in a real implementation, this would be loaded from a server
        const lessonData = {
            topic: {
                id: 1,
                name: "Introduction to Scientific Research",
                description: "Learn about the basic principles of scientific research",
                unit_id: 1,
                order: 1,
                created_at: "2023-05-01T10:00:00.000Z",
                updated_at: "2023-05-01T10:00:00.000Z"
            },
            unit: {
                id: 1,
                name: "Scientific Method"
            },
            subject: {
                id: 1,
                name: "Science"
            },
            class: {
                id: 4,
                name: "Standard 4"
            },
            contentBlocks: [
                {
                    id: 1,
                    topic_id: 1,
                    type: "heading",
                    content: "What is Scientific Research?",
                    media_path: null,
                    audio_path: "audio/intro-narration.mp3",
                    order: 1,
                    parent_id: null,
                    attributes: {
                        level: "h2"
                    },
                    created_at: "2023-05-01T10:01:00.000Z",
                    updated_at: "2023-05-01T10:01:00.000Z"
                },
                {
                    id: 2,
                    topic_id: 1,
                    type: "paragraph",
                    content: "Scientific research is a systematic process that involves observation, experimentation, and analysis to discover new knowledge or revise existing theories. It follows a structured approach called the scientific method.",
                    media_path: null,
                    audio_path: "audio/paragraph-narration.mp3",
                    order: 2,
                    parent_id: null,
                    attributes: {},
                    created_at: "2023-05-01T10:02:00.000Z",
                    updated_at: "2023-05-01T10:02:00.000Z"
                },
                {
                    id: 3,
                    topic_id: 1,
                    type: "image",
                    content: "Students conducting scientific research in a classroom",
                    media_path: "images/science-research.jpg",
                    audio_path: null,
                    order: 3,
                    parent_id: null,
                    attributes: {},
                    created_at: "2023-05-01T10:03:00.000Z",
                    updated_at: "2023-05-01T10:03:00.000Z"
                },
                {
                    id: 4,
                    topic_id: 1,
                    type: "heading",
                    content: "Steps of Scientific Research",
                    media_path: null,
                    audio_path: null,
                    order: 4,
                    parent_id: null,
                    attributes: {
                        level: "h3"
                    },
                    created_at: "2023-05-01T10:04:00.000Z",
                    updated_at: "2023-05-01T10:04:00.000Z"
                },
                {
                    id: 5,
                    topic_id: 1,
                    type: "list",
                    content: null,
                    media_path: null,
                    audio_path: "audio/list-narration.mp3",
                    order: 5,
                    parent_id: null,
                    attributes: {
                        listType: "numbered",
                        items: [
                            "Ask a question",
                            "Form a hypothesis",
                            "Conduct an experiment",
                            "Collect and analyze data",
                            "Draw conclusions",
                            "Communicate results"
                        ]
                    },
                    created_at: "2023-05-01T10:05:00.000Z",
                    updated_at: "2023-05-01T10:05:00.000Z"
                },
                {
                    id: 6,
                    topic_id: 1,
                    type: "video",
                    content: "Scientific Method Explained",
                    media_path: null,
                    audio_path: null,
                    order: 6,
                    parent_id: null,
                    attributes: {
                        video_path: "videos/scientific-method.mp4"
                    },
                    created_at: "2023-05-01T10:06:00.000Z",
                    updated_at: "2023-05-01T10:06:00.000Z"
                },
                {
                    id: 7,
                    topic_id: 1,
                    type: "audio",
                    content: "Interview with a Scientist",
                    media_path: null,
                    audio_path: "audio/scientist-interview.mp3",
                    order: 7,
                    parent_id: null,
                    attributes: {},
                    created_at: "2023-05-01T10:07:00.000Z",
                    updated_at: "2023-05-01T10:07:00.000Z"
                }
            ]
        };

        // Initialize the page with data
        function initPage() {
            // Set topic information
            document.getElementById('topic-name').textContent = lessonData.topic.name;
            document.getElementById('class-name').textContent = lessonData.class.name;
            document.getElementById('subject-name').textContent = lessonData.subject.name;
            document.getElementById('unit-name').textContent = lessonData.unit.name;
            
            // Set print header information
            document.getElementById('print-topic-name').textContent = lessonData.topic.name;
            document.getElementById('print-class-name').textContent = lessonData.class.name;
            document.getElementById('print-subject-name').textContent = lessonData.subject.name;
            document.getElementById('print-unit-name').textContent = lessonData.unit.name;
            
            // Render content blocks
            renderContentBlocks();
        }

        // Render content blocks
        function renderContentBlocks() {
            const container = document.getElementById('content-blocks-container');
            container.innerHTML = '';
            
            if (lessonData.contentBlocks.length > 0) {
                lessonData.contentBlocks.forEach(block => {
                    const blockElement = renderContentBlock(block);
                    if (blockElement) {
                        container.appendChild(blockElement);
                    }
                });
            } else {
                const emptyMessage = document.createElement('div');
                emptyMessage.className = 'empty-message';
                emptyMessage.innerHTML = '<p class="empty-text">No content has been added to this lesson yet.</p>';
                container.appendChild(emptyMessage);
            }
        }

        // Render a single content block
        function renderContentBlock(block) {
            const blockElement = document.createElement('div');
            blockElement.className = 'content-block';
            
            switch (block.type) {
                case 'heading':
                    blockElement.innerHTML = renderHeading(block);
                    break;
                case 'paragraph':
                    blockElement.innerHTML = renderParagraph(block);
                    break;
                case 'image':
                    blockElement.innerHTML = renderImage(block);
                    break;
                case 'video':
                    blockElement.innerHTML = renderVideo(block);
                    break;
                case 'audio':
                    blockElement.innerHTML = renderAudio(block);
                    break;
                case 'list':
                    blockElement.innerHTML = renderList(block);
                    break;
                default:
                    return null;
            }
            
            return blockElement;
        }

        // Render heading block
        function renderHeading(block) {
            const level = block.attributes?.level || 'h2';
            let headingElement = '';
            
            switch (level) {
                case 'h1':
                    headingElement = `<h1 class="heading-h1">${block.content}</h1>`;
                    break;
                case 'h2':
                    headingElement = `<h2 class="heading-h2">${block.content}</h2>`;
                    break;
                case 'h3':
                    headingElement = `<h3 class="heading-h3">${block.content}</h3>`;
                    break;
                case 'h4':
                    headingElement = `<h4 class="heading-h4">${block.content}</h4>`;
                    break;
                case 'h5':
                    headingElement = `<h5 class="heading-h5">${block.content}</h5>`;
                    break;
                case 'h6':
                    headingElement = `<h6 class="heading-h6">${block.content}</h6>`;
                    break;
                default:
                    headingElement = `<h2 class="heading-h2">${block.content}</h2>`;
            }
            
            return `
                <div>
                    ${headingElement}
                    ${renderNarrationButton(block)}
                </div>
            `;
        }

        // Render paragraph block
        function renderParagraph(block) {
            return `
                <div>
                    <p class="paragraph">${block.content}</p>
                    ${renderNarrationButton(block)}
                </div>
            `;
        }

        // Render image block
        function renderImage(block) {
            return `
                <div>
                    ${block.media_path ? `
                        <figure class="image-container">
                            <img
                                src="/storage/${block.media_path}"
                                alt="${block.content || ''}"
                                class="image"
                            />
                            ${block.content ? `
                                <figcaption class="image-caption">${block.content}</figcaption>
                            ` : ''}
                        </figure>
                    ` : ''}
                    ${renderNarrationButton(block)}
                </div>
            `;
        }

        // Render video block
        function renderVideo(block) {
            const videoUrl = block.attributes?.url;
            const videoPath = block.attributes?.video_path;
            
            return `
                <div>
                    <h3 class="video-title">${block.content}</h3>
                    ${videoPath ? `
                        <div class="video-container">
                            <video controls class="video" src="/storage/${videoPath}">
                                Your browser does not support the video tag.
                            </video>
                        </div>
                    ` : ''}
                    ${!videoPath && videoUrl ? `
                        <div class="iframe-container">
                            <iframe
                                src="${videoUrl.includes('youtube.com') ? videoUrl.replace('watch?v=', 'embed/') : videoUrl}"
                                allowFullScreen
                                title="${block.content || 'Video'}"
                            ></iframe>
                        </div>
                    ` : ''}
                </div>
            `;
        }

        // Render audio block
        function renderAudio(block) {
            return `
                <div>
                    <h3 class="audio-title">${block.content}</h3>
                    ${block.audio_path ? `
                        <audio controls class="audio-player" src="/storage/${block.audio_path}">
                            Your browser does not support the audio element.
                        </audio>
                    ` : ''}
                </div>
            `;
        }

        // Render list block
        function renderList(block) {
            const items = block.attributes?.items || [];
            const listType = block.attributes?.listType || 'bullet';
            
            let listItems = '';
            items.forEach(item => {
                listItems += `<li class="list-item">${item}</li>`;
            });
            
            return `
                <div>
                    ${listType === 'numbered' ? `
                        <ol class="numbered-list list-container">
                            ${listItems}
                        </ol>
                    ` : `
                        <ul class="bullet-list list-container">
                            ${listItems}
                        </ul>
                    `}
                    ${renderNarrationButton(block)}
                </div>
            `;
        }

        // Render narration button
        function renderNarrationButton(block) {
            if (block.audio_path) {
                return `
                    <div class="narration-container">
                        <button onclick="playAudio('${block.audio_path}')" class="narration-button">
                            <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <polygon points="11 5 6 9 2 9 2 15 6 15 11 19 11 5"></polygon>
                                <path d="M15.54 8.46a5 5 0 0 1 0 7.07"></path>
                                <path d="M19.07 4.93a10 10 0 0 1 0 14.14"></path>
                            </svg>
                            Play Narration
                        </button>
                        <audio id="audio-${block.id}" src="/storage/${block.audio_path}" class="hidden"></audio>
                    </div>
                `;
            }
            return '';
        }

        // Play audio narration
        function playAudio(audioPath) {
            // In a real implementation, this would play the audio
            alert('Playing audio: ' + audioPath);
        }

        // Go back function
        function goBack() {
            window.history.back();
        }

        // Initialize the page when loaded
        window.addEventListener('DOMContentLoaded', initPage);
    </script>
    </body>
</html>