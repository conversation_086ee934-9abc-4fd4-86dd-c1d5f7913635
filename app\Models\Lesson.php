<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Lesson extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'title',
        'description',
        'topic_id',
        'order',
        'is_published',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_published' => 'boolean',
        'media_path' => 'array',
    ];

    /**
     * Get the topic that the lesson belongs to.
     */
    public function topic()
    {
        return $this->belongsTo(Topic::class);
    }

    /**
     * Get the content blocks for the lesson.
     */
    public function contentBlocks()
    {
        return $this->hasMany(ContentBlock::class);
    }

    public function parent()
    {
        return $this->belongsTo(Lesson::class, 'parent_id');
    }

    public function children()
    {
        return $this->hasMany(Lesson::class, 'parent_id');
    }
}
