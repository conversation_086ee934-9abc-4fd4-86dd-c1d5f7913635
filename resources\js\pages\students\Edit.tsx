import { useForm } from '@inertiajs/react';
import { useEffect } from 'react';

import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

interface StudentUser {
    id: number;
    name: string;
    pin: number;
    class_id?: number;
}

interface Props {
    student: StudentUser;
    open: boolean;
    onOpenChange: (open: boolean) => void;
}

function EditStudent({ student, open, onOpenChange }: Props) {
    const { data, setData, put, processing, errors, reset } = useForm({
        name: student.name,
    });

    useEffect(() => {
        if (open) {
            setData({
                name: student.name,
            });
        }
    }, [open, student]);

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { name, value } = e.target;
        setData(name as keyof typeof data, value);
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();

        put(`/students/${student.id}`, {
            preserveScroll: true,
            onSuccess: () => {
                onOpenChange(false);
                reset();
            },
        });
    };

    const handleOpenChange = (open: boolean) => {
        onOpenChange(open);
        if (!open) {
            reset();
        }
    };

    return (
        <Dialog open={open} onOpenChange={handleOpenChange}>
            <DialogContent className="sm:max-w-md">
                <DialogHeader>
                    <DialogTitle>Edit Student</DialogTitle>
                    <DialogDescription>Update student information below.</DialogDescription>
                </DialogHeader>
                <form onSubmit={handleSubmit}>
                    <div className="grid gap-4 py-4">
                        <div className="grid gap-2">
                            <Label htmlFor="name">Student Name</Label>
                            <Input
                                id="name"
                                name="name"
                                value={data.name}
                                onChange={handleChange}
                                placeholder="Enter student's full name"
                                className={errors.name ? 'border-red-500' : ''}
                            />
                            {errors.name && <p className="text-xs text-red-500">{errors.name}</p>}
                        </div>

                        <div className="grid gap-2">
                            <Label htmlFor="pin">PIN</Label>
                            <Input id="pin" name="pin" value={student.pin} disabled className="bg-gray-50" />
                            <p className="text-xs text-gray-500">PIN cannot be changed</p>
                        </div>
                    </div>
                    <DialogFooter>
                        <button
                            type="button"
                            onClick={() => handleOpenChange(false)}
                            className="rounded-md px-4 py-2 text-sm font-medium text-gray-600 hover:bg-gray-100"
                            disabled={processing}
                        >
                            Cancel
                        </button>
                        <button
                            type="submit"
                            className="rounded-md bg-blue-950 px-4 py-2 text-sm font-medium text-white hover:bg-blue-950/90 disabled:opacity-50"
                            disabled={processing}
                        >
                            {processing ? 'Updating...' : 'Update Student'}
                        </button>
                    </DialogFooter>
                </form>
            </DialogContent>
        </Dialog>
    );
}

export default EditStudent;
