import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import AppLayout from '@/layouts/app-layout';
import { BreadcrumbItem, Lesson, Subject, Topic, Unit } from '@/types';
import { Head, router } from '@inertiajs/react';
import { BookOpen, File, FilePlus, FolderPlus, List, MessageSquarePlus, Plus } from 'lucide-react';

interface Props {
    unit: Unit;
    topics: Topic[];
    directLessons: Lesson[];
    subject: Subject;
    class_id: number;
    className: string;
}

function Dashboard({ unit, topics, directLessons, subject }: Props) {
    const breadcrumbs: BreadcrumbItem[] = [
        {
            title: 'Subjects',
            href: '/subjects',
        },
        {
            title: subject.name,
            href: `/subjects/${subject.id}/units`,
        },
        {
            title: unit.name,
            href: `/units/${unit.id}/dashboard`,
        },
    ];

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title={`${unit.name} - Dashboard`} />
            <div className="w-full">
                <div className="mx-auto w-full px-4 sm:px-6 lg:px-8">
                    <div className="mb-6 flex items-center justify-between">
                        <div>
                            <h2 className="text-2xl font-semibold">{unit.name}</h2>
                            <p className="mt-1 text-sm text-gray-500">{unit.description}</p>
                        </div>
                        <div className="flex space-x-2">
                            <Button onClick={() => router.get(route('units.lessons.create', unit.id))}>
                                <FilePlus className="mr-2 h-4 w-4" /> Add Lesson
                            </Button>
                            <Button onClick={() => router.get(route('topics.index', unit.id))}>
                                <FolderPlus className="mr-2 h-4 w-4" /> Manage Topics
                            </Button>
                        </div>
                    </div>
                    <Separator className="my-6" />

                    <Tabs defaultValue="overview" className="w-full">
                        <TabsList className="mb-4">
                            <TabsTrigger value="overview">Overview</TabsTrigger>
                            <TabsTrigger value="direct-lessons">Direct Lessons</TabsTrigger>
                            <TabsTrigger value="all-lessons">All Lessons</TabsTrigger>
                        </TabsList>

                        <TabsContent value="overview" className="space-y-6">
                            {/* Topics section */}
                            <div className="space-y-4">
                                <div className="flex items-center justify-between">
                                    <h3 className="text-lg font-medium">Topics</h3>
                                    <Button variant="outline" size="sm" onClick={() => router.get(route('topics.index', unit.id))}>
                                        <List className="mr-2 h-4 w-4" /> View All Topics
                                    </Button>
                                </div>

                                {topics.length > 0 ? (
                                    <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
                                        {topics.slice(0, 6).map((topic) => (
                                            <Card key={topic.id} className="overflow-hidden">
                                                <CardHeader className="pb-3">
                                                    <CardTitle className="text-base">{topic.name}</CardTitle>
                                                    {topic.description && <CardDescription className="text-xs">{topic.description}</CardDescription>}
                                                </CardHeader>
                                                <CardContent className="flex justify-between pt-0">
                                                    <Button
                                                        variant="ghost"
                                                        size="sm"
                                                        onClick={() => router.get(route('topics.lessons.index', topic.id))}
                                                    >
                                                        <BookOpen className="mr-2 h-4 w-4" /> View Lessons
                                                    </Button>
                                                    <Button variant="ghost" size="sm" onClick={() => router.post(route('topics.convert', topic.id))}>
                                                        <MessageSquarePlus className="mr-2 h-4 w-4" /> Convert to Lesson
                                                    </Button>
                                                </CardContent>
                                            </Card>
                                        ))}

                                        {topics.length > 6 && (
                                            <Card className="flex items-center justify-center overflow-hidden p-6">
                                                <Button variant="ghost" onClick={() => router.get(route('topics.index', unit.id))}>
                                                    <Plus className="mr-2 h-4 w-4" /> View All Topics
                                                </Button>
                                            </Card>
                                        )}
                                    </div>
                                ) : (
                                    <div className="flex flex-col items-center justify-center rounded-lg border-2 border-dashed border-gray-300 p-8 text-center">
                                        <FolderPlus className="mx-auto h-12 w-12 text-gray-400" />
                                        <h3 className="mt-2 text-sm font-medium text-gray-900">No topics</h3>
                                        <p className="mt-1 text-sm text-gray-500">Create topics to organize your lessons.</p>
                                        <div className="mt-6">
                                            <Button onClick={() => router.get(route('topics.index', unit.id))}>
                                                <Plus className="mr-2 h-4 w-4" /> Add Topic
                                            </Button>
                                        </div>
                                    </div>
                                )}
                            </div>

                            {/* Direct Lessons section */}
                            <div className="space-y-4">
                                <div className="flex items-center justify-between">
                                    <h3 className="text-lg font-medium">Unit Lessons</h3>
                                    <Button variant="outline" size="sm" onClick={() => router.get(route('units.lessons.index', unit.id))}>
                                        <BookOpen className="mr-2 h-4 w-4" /> View All Lessons
                                    </Button>
                                </div>

                                {directLessons.length > 0 ? (
                                    <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
                                        {directLessons.slice(0, 6).map((lesson) => (
                                            <Card key={lesson.id} className="overflow-hidden">
                                                <CardHeader className="pb-3">
                                                    <CardTitle className="text-base">{lesson.title}</CardTitle>
                                                    {lesson.description && (
                                                        <CardDescription className="text-xs">{lesson.description}</CardDescription>
                                                    )}
                                                </CardHeader>
                                                <CardContent className="flex justify-between pt-0">
                                                    <Button variant="ghost" size="sm" onClick={() => router.get(route('lessons.show', lesson.id))}>
                                                        <File className="mr-2 h-4 w-4" /> View Lesson
                                                    </Button>
                                                    <Button variant="ghost" size="sm" onClick={() => router.get(route('lessons.edit', lesson.id))}>
                                                        <MessageSquarePlus className="mr-2 h-4 w-4" /> Edit Lesson
                                                    </Button>
                                                </CardContent>
                                            </Card>
                                        ))}

                                        {directLessons.length > 6 && (
                                            <Card className="flex items-center justify-center overflow-hidden p-6">
                                                <Button variant="ghost" onClick={() => router.get(route('units.lessons.index', unit.id))}>
                                                    <Plus className="mr-2 h-4 w-4" /> View All Lessons
                                                </Button>
                                            </Card>
                                        )}
                                    </div>
                                ) : (
                                    <div className="flex flex-col items-center justify-center rounded-lg border-2 border-dashed border-gray-300 p-8 text-center">
                                        <FilePlus className="mx-auto h-12 w-12 text-gray-400" />
                                        <h3 className="mt-2 text-sm font-medium text-gray-900">No direct lessons</h3>
                                        <p className="mt-1 text-sm text-gray-500">Add lessons directly to this unit.</p>
                                        <div className="mt-6">
                                            <Button onClick={() => router.get(route('units.lessons.create', unit.id))}>
                                                <Plus className="mr-2 h-4 w-4" /> Add Lesson
                                            </Button>
                                        </div>
                                    </div>
                                )}
                            </div>
                        </TabsContent>

                        <TabsContent value="direct-lessons" className="space-y-4">
                            <div className="flex items-center justify-between">
                                <h3 className="text-lg font-medium">Unit Lessons</h3>
                                <Button onClick={() => router.get(route('units.lessons.create', unit.id))}>
                                    <FilePlus className="mr-2 h-4 w-4" /> Add Lesson
                                </Button>
                            </div>

                            {directLessons.length > 0 ? (
                                <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
                                    {directLessons.map((lesson) => (
                                        <Card key={lesson.id} className="overflow-hidden">
                                            <CardHeader>
                                                <CardTitle>{lesson.title}</CardTitle>
                                                {lesson.description && <CardDescription>{lesson.description}</CardDescription>}
                                            </CardHeader>
                                            <CardContent className="flex justify-between pt-0">
                                                <Button variant="outline" size="sm" onClick={() => router.get(route('lessons.show', lesson.id))}>
                                                    <File className="mr-2 h-4 w-4" /> View
                                                </Button>
                                                <Button variant="outline" size="sm" onClick={() => router.get(route('lessons.edit', lesson.id))}>
                                                    <MessageSquarePlus className="mr-2 h-4 w-4" /> Edit
                                                </Button>
                                            </CardContent>
                                        </Card>
                                    ))}
                                </div>
                            ) : (
                                <div className="flex flex-col items-center justify-center rounded-lg border-2 border-dashed border-gray-300 p-12 text-center">
                                    <FilePlus className="mx-auto h-12 w-12 text-gray-400" />
                                    <h3 className="mt-2 text-sm font-medium text-gray-900">No direct lessons</h3>
                                    <p className="mt-1 text-sm text-gray-500">Add lessons directly to this unit.</p>
                                    <div className="mt-6">
                                        <Button onClick={() => router.get(route('units.lessons.create', unit.id))}>
                                            <Plus className="mr-2 h-4 w-4" /> Add Lesson
                                        </Button>
                                    </div>
                                </div>
                            )}
                        </TabsContent>

                        <TabsContent value="all-lessons">
                            <div className="mb-4 flex items-center justify-between">
                                <h3 className="text-lg font-medium">All Lessons</h3>
                                <div className="flex space-x-2">
                                    <Button variant="outline" onClick={() => router.get(route('units.lessons.create', unit.id))}>
                                        <FilePlus className="mr-2 h-4 w-4" /> Add Unit Lesson
                                    </Button>
                                    <Button onClick={() => router.get(route('units.all-lessons', unit.id))}>
                                        <BookOpen className="mr-2 h-4 w-4" /> View All Lessons
                                    </Button>
                                </div>
                            </div>

                            <p className="mb-4 text-sm text-gray-500">
                                View all lessons across this unit (including both direct unit lessons and lessons within topics).
                            </p>

                            <Button variant="default" onClick={() => router.get(route('units.all-lessons', unit.id))} className="w-full py-8">
                                <BookOpen className="mr-2 h-6 w-6" /> View All Unit Lessons
                            </Button>
                        </TabsContent>
                    </Tabs>
                </div>
            </div>
        </AppLayout>
    );
}

export default Dashboard;
