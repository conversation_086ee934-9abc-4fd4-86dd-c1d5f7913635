import { <PERSON><PERSON> } from '@/components/ui/button';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Separator } from '@/components/ui/separator';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs';
import AppLayout from '@/layouts/app-layout';
import { BreadcrumbItem } from '@/types';
import { Head, Link } from '@inertiajs/react';
import { ArrowLeft, ArrowRight, BarChart2, CheckCircle, Download, Search, Users, XCircle } from 'lucide-react';
import { useState } from 'react';

interface Assessment {
    id: number;
    title: string;
    description: string;
    class_id: number;
    subject_id: number;
    unit_id: number;
    topic_id: number;
    questions_count: number;
}

interface Student {
    id: number;
    name: string;
    email: string;
    avatar?: string;
}

interface AssessmentResult {
    id: number;
    assessment_id: number;
    student_id: number;
    score: number;
    failed_questions: number;
    correct_questions: number;
    status: 'passed' | 'failed';
    created_at: string;
    student: Student;
}

interface Props {
    assessment: Assessment;
    results: {
        data: AssessmentResult[];
        total: number;
        per_page: number;
        current_page: number;
        last_page: number;
    };
}

function Results({ assessment, results }: Props) {
    const [searchTerm, setSearchTerm] = useState('');
    const [filteredResults, setFilteredResults] = useState<AssessmentResult[]>(results.data);
    const [activeTab, setActiveTab] = useState<'overview' | 'students'>('overview');

    // Calculate statistics
    const totalStudents = results.total;
    const passedStudents = results.data.filter((result) => result.status === 'passed').length;
    const failedStudents = results.data.filter((result) => result.status === 'failed').length;
    const averageScore = results.data.length ? results.data.reduce((sum, result) => sum + result.score, 0) / results.data.length : 0;
    const highestScore = results.data.length ? Math.max(...results.data.map((result) => result.score)) : 0;
    const lowestScore = results.data.length ? Math.min(...results.data.map((result) => result.score)) : 0;

    // Filter results based on search term
    const handleSearch = (term: string) => {
        setSearchTerm(term);
        if (term === '') {
            setFilteredResults(results.data);
        } else {
            const filtered = results.data.filter(
                (result) =>
                    result.student.name.toLowerCase().includes(term.toLowerCase()) || result.student.email.toLowerCase().includes(term.toLowerCase()),
            );
            setFilteredResults(filtered);
        }
    };

    // Download results as CSV
    const downloadResults = () => {
        const headers = ['Student Name', 'Email', 'Score', 'Correct Questions', 'Failed Questions', 'Status', 'Date'];
        const csvData = results.data.map((result) => [
            result.student.name,
            result.student.email,
            result.score.toString(),
            result.correct_questions.toString(),
            result.failed_questions.toString(),
            result.status,
            new Date(result.created_at).toLocaleDateString(),
        ]);

        const csvContent = [headers.join(','), ...csvData.map((row) => row.join(','))].join('\n');

        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.setAttribute('href', url);
        link.setAttribute('download', `${assessment.title}-results.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    };

    const breadcrumbs: BreadcrumbItem[] = [
        {
            title: 'Assessments',
            href: '/assessments',
        },
        {
            title: assessment.title,
            href: route('assessments.index', { class_id: assessment.class_id, subject_id: assessment.subject_id }),
        },
        {
            title: 'Results',
            href: `/assessments/${assessment.id}/results`,
        },
    ];

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title={`${assessment.title} - Results`} />
            <div className="container mx-auto p-6">
                <div className="mb-8 flex items-center justify-between">
                    <div>
                        <div className="flex items-center gap-2">
                            <Link href={route('assessments.index', { class_id: assessment.class_id, subject_id: assessment.subject_id })}>
                                <Button variant="ghost" size="icon" className="rounded-full">
                                    <ArrowLeft className="h-5 w-5" />
                                </Button>
                            </Link>
                            <h1 className="text-2xl font-bold text-gray-900">{assessment.title} Results</h1>
                        </div>
                        <p className="text-sm text-gray-500">{assessment.description}</p>
                    </div>
                    <Button variant="outline" onClick={downloadResults}>
                        <Download className="mr-2 h-4 w-4" /> Export Results
                    </Button>
                </div>

                <Tabs defaultValue="overview" value={activeTab} onValueChange={(value) => setActiveTab(value as 'overview' | 'students')}>
                    <TabsList className="mb-6 grid w-full grid-cols-2 lg:w-[400px]">
                        <TabsTrigger value="overview">Overview</TabsTrigger>
                        <TabsTrigger value="students">Student Results</TabsTrigger>
                    </TabsList>

                    <TabsContent value="overview">
                        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
                            <Card>
                                <CardHeader className="pb-2">
                                    <CardTitle className="text-sm font-medium text-gray-500">Total Students</CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <div className="flex items-center">
                                        <Users className="mr-2 h-5 w-5 text-blue-600" />
                                        <span className="text-2xl font-bold">{totalStudents}</span>
                                    </div>
                                </CardContent>
                            </Card>

                            <Card>
                                <CardHeader className="pb-2">
                                    <CardTitle className="text-sm font-medium text-gray-500">Average Score</CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <div className="flex items-center">
                                        <BarChart2 className="mr-2 h-5 w-5 text-purple-600" />
                                        <span className="text-2xl font-bold">{averageScore.toFixed(1)}%</span>
                                    </div>
                                </CardContent>
                            </Card>

                            <Card>
                                <CardHeader className="pb-2">
                                    <CardTitle className="text-sm font-medium text-gray-500">Passed</CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <div className="flex items-center">
                                        <CheckCircle className="mr-2 h-5 w-5 text-green-600" />
                                        <span className="text-2xl font-bold">{passedStudents}</span>
                                        <span className="ml-1 text-sm text-gray-500">
                                            ({totalStudents ? ((passedStudents / totalStudents) * 100).toFixed(0) : 0}%)
                                        </span>
                                    </div>
                                </CardContent>
                            </Card>

                            <Card>
                                <CardHeader className="pb-2">
                                    <CardTitle className="text-sm font-medium text-gray-500">Failed</CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <div className="flex items-center">
                                        <XCircle className="mr-2 h-5 w-5 text-red-600" />
                                        <span className="text-2xl font-bold">{failedStudents}</span>
                                        <span className="ml-1 text-sm text-gray-500">
                                            ({totalStudents ? ((failedStudents / totalStudents) * 100).toFixed(0) : 0}%)
                                        </span>
                                    </div>
                                </CardContent>
                            </Card>
                        </div>

                        <div className="mt-6 grid gap-6 md:grid-cols-2">
                            <Card>
                                <CardHeader>
                                    <CardTitle>Score Distribution</CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <div className="space-y-4">
                                        {[
                                            { range: '90-100%', color: 'bg-green-500' },
                                            { range: '80-89%', color: 'bg-green-400' },
                                            { range: '70-79%', color: 'bg-yellow-400' },
                                            { range: '60-69%', color: 'bg-yellow-500' },
                                            { range: 'Below 60%', color: 'bg-red-500' },
                                        ].map((scoreRange) => {
                                            // Calculate students in this range
                                            let count = 0;
                                            if (scoreRange.range === '90-100%') {
                                                count = results.data.filter((r) => r.score >= 90).length;
                                            } else if (scoreRange.range === '80-89%') {
                                                count = results.data.filter((r) => r.score >= 80 && r.score < 90).length;
                                            } else if (scoreRange.range === '70-79%') {
                                                count = results.data.filter((r) => r.score >= 70 && r.score < 80).length;
                                            } else if (scoreRange.range === '60-69%') {
                                                count = results.data.filter((r) => r.score >= 60 && r.score < 70).length;
                                            } else {
                                                count = results.data.filter((r) => r.score < 60).length;
                                            }

                                            const percentage = totalStudents ? (count / totalStudents) * 100 : 0;

                                            return (
                                                <div key={scoreRange.range}>
                                                    <div className="mb-1 flex justify-between text-sm">
                                                        <span>{scoreRange.range}</span>
                                                        <span>
                                                            {count} students ({percentage.toFixed(0)}%)
                                                        </span>
                                                    </div>
                                                    <div className="h-2 w-full rounded-full bg-gray-100">
                                                        <div
                                                            className={`h-2 rounded-full ${scoreRange.color}`}
                                                            style={{ width: `${percentage}%` }}
                                                        ></div>
                                                    </div>
                                                </div>
                                            );
                                        })}
                                    </div>
                                </CardContent>
                            </Card>

                            <Card>
                                <CardHeader>
                                    <CardTitle>Assessment Summary</CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <dl className="space-y-4">
                                        <div className="flex justify-between">
                                            <dt className="font-medium text-gray-500">Highest Score:</dt>
                                            <dd className="font-bold text-gray-900">{highestScore}%</dd>
                                        </div>
                                        <Separator />
                                        <div className="flex justify-between">
                                            <dt className="font-medium text-gray-500">Lowest Score:</dt>
                                            <dd className="font-bold text-gray-900">{lowestScore}%</dd>
                                        </div>
                                        <Separator />
                                        <div className="flex justify-between">
                                            <dt className="font-medium text-gray-500">Average Score:</dt>
                                            <dd className="font-bold text-gray-900">{averageScore.toFixed(1)}%</dd>
                                        </div>
                                        <Separator />
                                        <div className="flex justify-between">
                                            <dt className="font-medium text-gray-500">Total Questions:</dt>
                                            <dd className="font-bold text-gray-900">{assessment.questions_count}</dd>
                                        </div>
                                        <Separator />
                                        <div className="flex justify-between">
                                            <dt className="font-medium text-gray-500">Passing Rate:</dt>
                                            <dd className="font-bold text-gray-900">
                                                {totalStudents ? ((passedStudents / totalStudents) * 100).toFixed(0) : 0}%
                                            </dd>
                                        </div>
                                    </dl>
                                </CardContent>
                            </Card>
                        </div>
                    </TabsContent>

                    <TabsContent value="students">
                        <div className="mb-6">
                            <div className="relative">
                                <Search className="absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 text-gray-500" />
                                <Input
                                    placeholder="Search students by name or email..."
                                    value={searchTerm}
                                    onChange={(e) => handleSearch(e.target.value)}
                                    className="pl-10"
                                />
                            </div>
                        </div>

                        {filteredResults.length === 0 ? (
                            <div className="rounded-lg border border-gray-200 p-6 text-center text-gray-500">
                                {searchTerm ? `No students matching "${searchTerm}"` : 'No students have taken this assessment yet.'}
                            </div>
                        ) : (
                            <div className="rounded-lg border border-gray-200 bg-white shadow">
                                <div className="overflow-x-auto">
                                    <table className="min-w-full divide-y divide-gray-200">
                                        <thead className="bg-gray-50">
                                            <tr>
                                                <th
                                                    scope="col"
                                                    className="px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase"
                                                >
                                                    Student
                                                </th>
                                                <th
                                                    scope="col"
                                                    className="px-6 py-3 text-center text-xs font-medium tracking-wider text-gray-500 uppercase"
                                                >
                                                    Score
                                                </th>
                                                <th
                                                    scope="col"
                                                    className="px-6 py-3 text-center text-xs font-medium tracking-wider text-gray-500 uppercase"
                                                >
                                                    Correct / Total
                                                </th>
                                                <th
                                                    scope="col"
                                                    className="px-6 py-3 text-center text-xs font-medium tracking-wider text-gray-500 uppercase"
                                                >
                                                    Status
                                                </th>
                                                <th
                                                    scope="col"
                                                    className="px-6 py-3 text-center text-xs font-medium tracking-wider text-gray-500 uppercase"
                                                >
                                                    Date
                                                </th>
                                                <th
                                                    scope="col"
                                                    className="px-6 py-3 text-right text-xs font-medium tracking-wider text-gray-500 uppercase"
                                                >
                                                    Details
                                                </th>
                                            </tr>
                                        </thead>
                                        <tbody className="divide-y divide-gray-200 bg-white">
                                            {filteredResults.map((result) => (
                                                <tr key={result.id} className="hover:bg-gray-50">
                                                    <td className="px-6 py-4 whitespace-nowrap">
                                                        <div className="flex items-center">
                                                            <div className="flex h-10 w-10 flex-shrink-0 items-center justify-center rounded-full bg-blue-950 text-sm font-bold text-white">
                                                                {result.student.avatar ? (
                                                                    <img
                                                                        src={result.student.avatar}
                                                                        alt={result.student.name}
                                                                        className="h-10 w-10 rounded-full object-cover"
                                                                    />
                                                                ) : (
                                                                    result.student.name.charAt(0).toUpperCase()
                                                                )}
                                                            </div>
                                                            <div className="ml-4">
                                                                <div className="text-sm font-medium text-gray-900">{result.student.name}</div>
                                                                <div className="text-sm text-gray-500">{result.student.email}</div>
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td className="px-6 py-4 text-center whitespace-nowrap">
                                                        <div
                                                            className={`text-sm font-bold ${
                                                                result.score >= 80
                                                                    ? 'text-green-600'
                                                                    : result.score >= 60
                                                                      ? 'text-yellow-600'
                                                                      : 'text-red-600'
                                                            }`}
                                                        >
                                                            {result.score}%
                                                        </div>
                                                    </td>
                                                    <td className="px-6 py-4 text-center whitespace-nowrap">
                                                        <div className="text-sm text-gray-900">
                                                            {result.correct_questions} / {assessment.questions_count}
                                                        </div>
                                                    </td>
                                                    <td className="px-6 py-4 text-center whitespace-nowrap">
                                                        <span
                                                            className={`inline-flex rounded-full px-2 text-xs leading-5 font-semibold ${
                                                                result.status === 'passed' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                                                            }`}
                                                        >
                                                            {result.status.charAt(0).toUpperCase() + result.status.slice(1)}
                                                        </span>
                                                    </td>
                                                    <td className="px-6 py-4 text-center text-sm whitespace-nowrap text-gray-500">
                                                        {new Date(result.created_at).toLocaleDateString()}
                                                    </td>
                                                    <td className="px-6 py-4 text-right text-sm whitespace-nowrap">
                                                        <Link href={`/assessments/${assessment.id}/results/${result.id}`}>
                                                            <Button size="sm" variant="ghost">
                                                                View <ArrowRight className="ml-1 h-3 w-3" />
                                                            </Button>
                                                        </Link>
                                                    </td>
                                                </tr>
                                            ))}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        )}

                        {/* Pagination */}
                        {results.last_page > 1 && searchTerm === '' && (
                            <div className="mt-6 flex items-center justify-between">
                                <div className="text-sm text-gray-700">
                                    Showing results {results.per_page * (results.current_page - 1) + 1} to{' '}
                                    {Math.min(results.per_page * results.current_page, results.total)} of {results.total} students
                                </div>
                                <div className="flex space-x-2">
                                    {Array.from({ length: results.last_page }, (_, i) => i + 1).map((page) => (
                                        <Link
                                            key={page}
                                            href={`/assessments/${assessment.id}/results?page=${page}`}
                                            className={`rounded px-3 py-1 ${
                                                page === results.current_page
                                                    ? 'bg-blue-950 text-white'
                                                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                                            }`}
                                        >
                                            {page}
                                        </Link>
                                    ))}
                                </div>
                            </div>
                        )}
                    </TabsContent>
                </Tabs>
            </div>
        </AppLayout>
    );
}

export default Results;
