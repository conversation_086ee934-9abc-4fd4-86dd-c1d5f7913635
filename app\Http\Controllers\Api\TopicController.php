<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\CompletedTopic;
use App\Models\Topic;
use Illuminate\Http\Request;

class TopicController extends Controller
{
    //
    public function getTopics(Request $request, $unit_id)
    {
        $topics = Topic::where('unit_id', $unit_id)->get()->map(function ($topic) {
            return [
                'id' => $topic->id,
                'name' => $topic->name,
                'description' => $topic->description,
                'unit_id' => $topic->unit_id,
                'completed' => CompletedTopic::where('topic_id', $topic->id)->exists()
            ];
        });
        return response()->json($topics);
    }
}
