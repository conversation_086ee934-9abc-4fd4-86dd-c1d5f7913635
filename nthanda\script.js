// Define the grades data
const grades = [
  {
    id: "grade1",
    name: "Standard 1",
    subjects: "3 Subjects",
    color: "#4CAF50",
    icon: "book-open-variant",
  },
  {
    id: "grade2",
    name: "Standard 2",
    subjects: "4 Subjects",
    color: "#2196F3",
    icon: "book-open-page-variant",
  },
  {
    id: "grade3",
    name: "Standard 3",
    subjects: "5 Subjects",
    color: "#9C27B0",
    icon: "notebook",
  },
  {
    id: "grade4",
    name: "Standard 4",
    subjects: "6 Subjects",
    color: "#FF9800",
    icon: "school",
  },
];

// Variables to store form data
let name = "";
let selectedClass = null;
let selectedGender = null;
let error = "";

// DOM Elements
document.addEventListener("DOMContentLoaded", function () {
  const nameInput = document.getElementById("nameInput");
  const errorText = document.getElementById("errorText");
  const registerButton = document.getElementById("registerButton");
  const classGrid = document.getElementById("classGrid");
  const maleButton = document.getElementById("maleButton");
  const femaleButton = document.getElementById("femaleButton");

  // Generate class cards
  grades.forEach((grade) => {
    const card = document.createElement("div");
    card.className = "class-card";
    card.id = `class-${grade.id}`;

    const iconContainer = document.createElement("div");
    iconContainer.className = "class-icon-container";
    iconContainer.style.backgroundColor = `${grade.color}15`;

    const icon = document.createElement("i");
    icon.className = "material-icons";
    icon.textContent = getIconName(grade.icon);
    icon.style.color = grade.color;

    const name = document.createElement("div");
    name.className = "class-name";
    name.textContent = grade.name;

    const subjects = document.createElement("div");
    subjects.className = "subjects-count";
    subjects.textContent = grade.subjects;

    iconContainer.appendChild(icon);
    card.appendChild(iconContainer);
    card.appendChild(name);
    card.appendChild(subjects);

    card.addEventListener("click", () => {
      selectClass(grade.id);
    });

    classGrid.appendChild(card);
  });

  // Event listeners
  nameInput.addEventListener("input", (e) => {
    name = e.target.value;
    errorText.textContent = "";
  });

  maleButton.addEventListener("click", () => {
    selectGender("male");
  });

  femaleButton.addEventListener("click", () => {
    selectGender("female");
  });

  registerButton.addEventListener("click", handleRegister);
});

// Helper function to convert Expo icons to Material Icons
function getIconName(expoIcon) {
  const iconMap = {
    "book-open-variant": "menu_book",
    "book-open-page-variant": "auto_stories",
    notebook: "book",
    school: "school",
  };

  return iconMap[expoIcon] || "book";
}

// Function to select gender
function selectGender(gender) {
  selectedGender = gender;

  const maleButton = document.getElementById("maleButton");
  const femaleButton = document.getElementById("femaleButton");

  if (gender === "male") {
    maleButton.classList.add("selected-gender");
    femaleButton.classList.remove("selected-gender");

    // Add check circle to male
    let checkCircle = maleButton.querySelector(".check-circle");
    if (!checkCircle) {
      checkCircle = document.createElement("div");
      checkCircle.className = "check-circle";
      checkCircle.innerHTML = '<i class="material-icons">check</i>';
      maleButton.appendChild(checkCircle);
    }

    // Remove check circle from female
    const femaleCheckCircle = femaleButton.querySelector(".check-circle");
    if (femaleCheckCircle) {
      femaleButton.removeChild(femaleCheckCircle);
    }
  } else {
    femaleButton.classList.add("selected-gender");
    maleButton.classList.remove("selected-gender");

    // Add check circle to female
    let checkCircle = femaleButton.querySelector(".check-circle");
    if (!checkCircle) {
      checkCircle = document.createElement("div");
      checkCircle.className = "check-circle";
      checkCircle.innerHTML = '<i class="material-icons">check</i>';
      femaleButton.appendChild(checkCircle);
    }

    // Remove check circle from male
    const maleCheckCircle = maleButton.querySelector(".check-circle");
    if (maleCheckCircle) {
      maleButton.removeChild(maleCheckCircle);
    }
  }
}

// Function to select class
function selectClass(classId) {
  selectedClass = classId;

  // Remove selected class from all cards
  document.querySelectorAll(".class-card").forEach((card) => {
    card.classList.remove("selected-class");

    // Remove check circle
    const checkCircle = card.querySelector(".check-circle");
    if (checkCircle) {
      card.removeChild(checkCircle);
    }
  });

  // Add selected class to the chosen card
  const selectedCard = document.getElementById(`class-${classId}`);
  if (selectedCard) {
    selectedCard.classList.add("selected-class");

    // Add check circle
    const checkCircle = document.createElement("div");
    checkCircle.className = "check-circle";
    checkCircle.innerHTML = '<i class="material-icons">check</i>';
    selectedCard.appendChild(checkCircle);
  }
}

// Function to check if user exists
async function checkExistingUser(nameToCheck) {
  try {
    const studentsInfo = localStorage.getItem("students_info");
    if (studentsInfo) {
      const students = JSON.parse(studentsInfo);
      return students.find(
        (student) => student.name === nameToCheck.trim().toLowerCase()
      );
    }
    return null;
  } catch (error) {
    console.error("Error checking existing user:", error);
    return null;
  }
}

// Function to handle registration
async function handleRegister() {
  const errorText = document.getElementById("errorText");

  if (!name.trim()) {
    errorText.textContent = "Chonde lowetsani dzina lanu!";
    return;
  }

  if (!selectedGender) {
    errorText.textContent =
      "Chonde sankhani ngati ndinu Mnyamata kapena Mtsikana!";
    return;
  }

  if (!selectedClass) {
    errorText.textContent = "Chonde sankhani kalasi yanu!";
    return;
  }

  try {
    // Check if user already exists
    const existingUser = await checkExistingUser(name);
    if (existingUser) {
      // Set login session
      localStorage.setItem("logged_in", JSON.stringify(existingUser));
      window.location.href = "home.html";
      return;
    }

    // Generate a unique student ID
    const studentId = `STD_${Date.now()}_${Math.floor(Math.random() * 1000)}`;
    const newStudent = {
      id: studentId,
      name: name.trim().toLowerCase(),
      class_id: selectedClass,
      gender: selectedGender,
      registered_at: new Date().toISOString(),
    };

    // Get existing students or initialize empty array
    const existingStudentsJson = localStorage.getItem("students_info");
    const existingStudents = existingStudentsJson
      ? JSON.parse(existingStudentsJson)
      : [];

    // Add new student to list
    const updatedStudents = [...existingStudents, newStudent];

    // Save updated students list
    localStorage.setItem("students_info", JSON.stringify(updatedStudents));

    // Set login session
    localStorage.setItem("logged_in", JSON.stringify(newStudent));

    // Initialize stats
    localStorage.setItem(
      `stats_${studentId}`,
      JSON.stringify({
        lessons_completed: 0,
        quizzes_taken: 0,
        total_score: 0,
        subjects_progress: {},
      })
    );

    window.location.href = "home.html";
  } catch (error) {
    console.error("Error saving student info:", error);
    errorText.textContent = "Pangopsekedwa vuto, yesesaninso!";
  }
}
