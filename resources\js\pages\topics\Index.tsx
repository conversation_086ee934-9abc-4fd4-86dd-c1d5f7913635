import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import AppLayout from '@/layouts/app-layout';
import TabLayout from '@/layouts/TabLayout';
import { BreadcrumbItem, NavItem, SchoolClass, Subject, Unit } from '@/types';
import { Head, router, usePage } from '@inertiajs/react';
import { Edit, MoreHorizontal, Plus, Search, Trash } from 'lucide-react';
import { useEffect, useState } from 'react';
import Create from './Create';
import DeleteTopic from './Delete';
import EditTopic from './Edit';
interface topic {
    id: number;
    name: string;
    unit_id: number;
}

interface Props {
    topics: {
        data: topic[];
        current_page: number;
        last_page: number;
        per_page: number;
        total: number;
        from: number;
        to: number;
    };
}

function Index({ topics }: Props) {
    const schoolClass = usePage().props.class as SchoolClass;
    const class_id = schoolClass.id;
    const className = schoolClass.name;
    const subjects = usePage().props.subjects as Subject[];
    const subject = usePage().props.subject as Subject;
    const unit = usePage().props.unit as Unit;
    const units = usePage().props.units as Unit[];
    const [searchTerm, setSearchTerm] = useState('');
    const [filteredTopics, setFilteredTopics] = useState<topic[]>(topics.data);
    const [editingTopic, setEditingTopic] = useState<topic | null>(null);
    const [deletingTopic, setDeletingTopic] = useState<topic | null>(null);

    useEffect(() => {
        if (searchTerm.trim() === '') {
            setFilteredTopics(topics.data);
        } else {
            const filtered = topics.data.filter((topic) => topic.name.toLowerCase().includes(searchTerm.toLowerCase()));
            setFilteredTopics(filtered);
        }
    }, [searchTerm, topics.data]);

    const breadcrumbs: BreadcrumbItem[] = [
        {
            title: 'Topics',
            href: '/topics',
        },
    ];

    const sidebarNavItems: NavItem[] = subjects.map((subjectItem: Subject) => ({
        title: subjectItem.name,
        href: route('topics.index', { class_id: class_id, subject_id: subjectItem.id }),
        route: route('topics.index', { class_id: class_id, subject_id: subjectItem.id }),
        isActive: subjectItem.id == subject.id,
    }));

    const setUnit = (unit: Unit) => {
        console.log(unit);
        router.visit(route('topics.index', { class_id: class_id, subject_id: subject.id, unit_id: unit.id }));
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Subject Topics" />
            <TabLayout title={className} description={`manage your topics for ${subject.name}`} sidebarNavItems={sidebarNavItems}>
                <div className="w-full">
                    <div className="mx-auto w-full px-4 sm:px-6 lg:px-8">
                        <div className="mb-6 flex items-center justify-between">
                            <div className="flex flex-col">
                                <h2 className="text-sm font-semibold">{subject.name}</h2>
                                <p className="text-2xl font-semibold text-blue-950">{unit.name}</p>
                            </div>
                            {unit && <Create />}
                        </div>
                        <Separator className="my-6" />

                        {/* Search and filter section */}
                        <div className="mb-6 flex items-center justify-between gap-2">
                            <div className="relative">
                                <Search className="absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 text-gray-500" />
                                <Input
                                    placeholder="Search topics by name..."
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                    className="pl-10"
                                />
                            </div>
                            <div className="flex items-center gap-2">
                                <div className="flex-shrink-0 text-base font-medium text-red-500">Change Unit: {'  '}</div>
                                <Select
                                    value={unit.id.toString()}
                                    onValueChange={(value) => setUnit(units.find((unit: Unit) => unit.id.toString() === value) as Unit)}
                                >
                                    <SelectTrigger>
                                        <SelectValue placeholder="Select a unit" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {units.map((unit) => (
                                            <SelectItem key={unit.id} value={unit.id.toString()}>
                                                {unit.name}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </div>
                        </div>

                        {/* Student list section */}
                        {topics.data.length > 0 && (
                            <div className="overflow-hidden rounded-lg border border-gray-200 bg-white shadow">
                                <table className="min-w-full divide-y divide-gray-200">
                                    <thead className="bg-gray-50">
                                        <tr>
                                            <th
                                                scope="col"
                                                className="px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase"
                                            >
                                                Name
                                            </th>

                                            <th
                                                scope="col"
                                                className="px-6 py-3 text-right text-xs font-medium tracking-wider text-gray-500 uppercase"
                                            >
                                                Actions
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody className="divide-y divide-gray-200 bg-white">
                                        {filteredTopics.map((topic) => (
                                            <tr key={topic.id} className="hover:bg-gray-50">
                                                <td className="px-6 py-4 whitespace-nowrap">
                                                    <div className="flex items-center">
                                                        <div className="flex h-10 w-10 flex-shrink-0 items-center justify-center rounded-full bg-blue-950 text-sm font-bold text-white">
                                                            {topic.name.charAt(0).toUpperCase()}
                                                        </div>
                                                        <div className="ml-4">
                                                            <div className="text-sm font-medium text-gray-900">{topic.name}</div>
                                                        </div>
                                                    </div>
                                                </td>

                                                <td className="px-6 py-4 text-right text-sm whitespace-nowrap">
                                                    <DropdownMenu>
                                                        <DropdownMenuTrigger className="cursor-pointer rounded-md p-1 text-gray-400 hover:bg-gray-100">
                                                            <MoreHorizontal className="h-5 w-5" />
                                                        </DropdownMenuTrigger>
                                                        <DropdownMenuContent align="end">
                                                            <DropdownMenuItem
                                                                className="cursor-pointer"
                                                                onClick={() => router.visit(route('topics.content', { topic_id: topic.id }))}
                                                            >
                                                                <Plus className="mr-2 h-4 w-4" />
                                                                Add content
                                                            </DropdownMenuItem>
                                                            <DropdownMenuItem className="cursor-pointer" onClick={() => setEditingTopic(topic)}>
                                                                <Edit className="mr-2 h-4 w-4" />
                                                                Edit
                                                            </DropdownMenuItem>
                                                            <DropdownMenuItem
                                                                variant="destructive"
                                                                className="cursor-pointer"
                                                                onClick={() => setDeletingTopic(topic)}
                                                            >
                                                                <Trash className="mr-2 h-4 w-4" />
                                                                Delete
                                                            </DropdownMenuItem>
                                                        </DropdownMenuContent>
                                                    </DropdownMenu>
                                                </td>
                                            </tr>
                                        ))}
                                    </tbody>
                                </table>
                                {filteredTopics.length === 0 && searchTerm !== '' && (
                                    <div className="p-6 text-center text-gray-500">No topics found matching "{searchTerm}"</div>
                                )}
                            </div>
                        )}

                        {/* Empty state when no students */}
                        {topics.data.length === 0 && (
                            <div className="flex flex-col items-center justify-center rounded-lg border-2 border-dashed border-gray-300 p-12 text-center">
                                <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth={2}
                                        d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"
                                    />
                                </svg>
                                <h3 className="mt-2 text-sm font-medium text-gray-900">No topics</h3>
                                <p className="mt-1 text-sm text-gray-500">Get started by adding a new topic to this subject.</p>
                                <div className="mt-6">
                                    <Create />
                                </div>
                            </div>
                        )}

                        {/* Pagination */}
                        {topics && topics.last_page > 1 && searchTerm === '' && (
                            <div className="mt-6 flex items-center justify-between">
                                <div className="text-sm text-gray-700">
                                    Showing {topics.from} to {topics.to} of {topics.total} results
                                </div>
                                <div className="flex space-x-2">
                                    {Array.from({ length: topics.last_page }, (_, i) => i + 1).map((page) => (
                                        <a
                                            key={page}
                                            href={`/topics?class_id=${class_id}&subject_id=${subject.id}&page=${page}`}
                                            className={`rounded px-3 py-1 ${
                                                page === topics.current_page
                                                    ? 'bg-blue-950 text-white'
                                                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                                            }`}
                                        >
                                            {page}
                                        </a>
                                    ))}
                                </div>
                            </div>
                        )}
                    </div>
                </div>
                {editingTopic && (
                    <EditTopic topic={editingTopic} open={!!editingTopic} onOpenChange={(open: boolean) => !open && setEditingTopic(null)} />
                )}

                {/* Delete Dialog */}
                {deletingTopic && (
                    <DeleteTopic topic={deletingTopic} open={!!deletingTopic} onOpenChange={(open: boolean) => !open && setDeletingTopic(null)} />
                )}
            </TabLayout>
        </AppLayout>
    );
}

export default Index;
