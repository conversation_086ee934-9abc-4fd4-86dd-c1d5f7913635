<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class CompletedTopic extends Model
{
    //
    protected $fillable = [
        'class_id',
        'user_id',
        'subject_id',
        'unit_id',
        'topic_id',
    ];

    public function class()
    {
        return $this->belongsTo(SchoolClass::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function subject()
    {
        return $this->belongsTo(Subject::class);
    }

    public function unit()
    {
        return $this->belongsTo(Unit::class);
    }

    public function topic()
    {
        return $this->belongsTo(Topic::class);
    }
}