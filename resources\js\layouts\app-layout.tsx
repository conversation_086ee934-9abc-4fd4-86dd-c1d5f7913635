import AppLayoutTemplate from '@/layouts/app/app-sidebar-layout';
import { type BreadcrumbItem } from '@/types';
import { usePage } from '@inertiajs/react';
import { type ReactNode, useEffect } from 'react';
import toast, { Toaster } from 'react-hot-toast';

interface PageProps {
    flash?: {
        success?: string;
        error?: string;
        warning?: string;
        info?: string;
    };
    [key: string]: unknown;
}

interface AppLayoutProps {
    children: ReactNode;
    breadcrumbs?: BreadcrumbItem[];
}

export default ({ children, breadcrumbs, ...props }: AppLayoutProps) => {
    const { flash } = usePage().props as unknown as PageProps;

    useEffect(() => {
        // Handle success messages
        if (flash?.success) {
            toast.success(flash.success, {
                duration: 3000,
                position: 'top-right',
                className: 'bg-green-50',
                iconTheme: {
                    primary: '#10B981',
                    secondary: '#ECFDF5',
                },
            });
        }

        // Handle error messages
        if (flash?.error) {
            toast.error(flash.error, {
                duration: 10000,
                position: 'top-right',
                className: 'bg-red-100',
                iconTheme: {
                    primary: '#EF4444',
                    secondary: '#FEF2F2',
                },
            });
        }

        // Handle warning messages
        if (flash?.warning) {
            toast(flash.warning, {
                duration: 10000,
                position: 'top-right',
                icon: '⚠️',
                className: 'bg-yellow-100',
                style: {
                    borderColor: '#F59E0B',
                },
            });
        }

        // Handle info messages
        if (flash?.info) {
            toast(flash.info, {
                duration: 3000,
                position: 'top-right',
                icon: 'ℹ️',
                className: 'bg-blue-50',
                style: {
                    borderColor: '#3B82F6',
                },
            });
        }
    }, [flash]);

    return (
        <AppLayoutTemplate breadcrumbs={breadcrumbs} {...props}>
            {children}
            <Toaster
                toastOptions={{
                    style: {
                        border: '1px solid #E5E7EB',
                        padding: '16px',
                        color: '#1F2937',
                    },
                }}
            />
        </AppLayoutTemplate>
    );
};
