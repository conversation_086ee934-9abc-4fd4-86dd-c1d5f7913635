<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\ContentBlock;
use App\Models\CompletedTopic;
use App\Models\Topic;
use App\Models\Unit;
use App\Models\Subject;
use App\Models\SchoolClass;
use Illuminate\Http\Request;
use Carbon\Carbon;

class LessonController extends Controller
{
    //
    public function getLesson(Request $request)
    {
        $lesson = ContentBlock::where('topic_id', $request->topic_id)->orderBy('order', 'asc')->get();
        return response()->json($lesson);
    }

    public function markAsComplete(Request $request)
    {
        // Validate the request
        $request->validate([
            'topic_id' => 'required|exists:topics,id',
            'user_id' => 'required|exists:users,id',
        ]);

        // Get the topic and its relationships
        $topic = Topic::with('unit.subject.schoolClass')->findOrFail($request->topic_id);

        // Check if the topic is already marked as completed
        $existingCompletion = CompletedTopic::where('user_id', $request->user_id)
            ->where('topic_id', $request->topic_id)
            ->first();

        if ($existingCompletion) {
            return response()->json([
                'success' => true,
                'message' => 'Topic was already marked as completed',
                'completed_at' => $existingCompletion->created_at
            ]);
        }

        // Create a new completion record
        $completedTopic = CompletedTopic::create([
            'user_id' => $request->user_id,
            'topic_id' => $request->topic_id,
            'unit_id' => $topic->unit_id,
            'subject_id' => $topic->unit->subject_id,
            'class_id' => $topic->unit->subject->class_id,
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Topic marked as completed successfully',
            'completed_at' => $completedTopic->created_at
        ]);
    }
}