import { Button } from '@/components/ui/button';
import { <PERSON><PERSON>, DialogContent, DialogDescription, Di<PERSON>Footer, <PERSON>alogHeader, DialogTitle } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Lesson } from '@/types';
import { router } from '@inertiajs/react';
import { useEffect, useState } from 'react';

interface EditLessonProps {
    lesson: Lesson;
    open: boolean;
    onOpenChange: (open: boolean) => void;
}

export default function EditLesson({ lesson, open, onOpenChange }: EditLessonProps) {
    const [title, setTitle] = useState(lesson.title);
    const [description, setDescription] = useState(lesson.description || '');
    const [processing, setProcessing] = useState(false);

    // Update local state when lesson changes
    useEffect(() => {
        if (lesson) {
            setTitle(lesson.title);
            setDescription(lesson.description || '');
        }
    }, [lesson]);

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        setProcessing(true);

        router.put(
            route('lessons.update', { lesson: lesson.id }),
            {
                title,
                description,
            },
            {
                onSuccess: () => {
                    setProcessing(false);
                    onOpenChange(false);
                },
                onError: () => {
                    setProcessing(false);
                },
            },
        );
    };

    return (
        <Dialog open={open} onOpenChange={onOpenChange}>
            <DialogContent className="sm:max-w-md">
                <DialogHeader>
                    <DialogTitle>Edit Lesson</DialogTitle>
                    <DialogDescription>Modify the details for this lesson</DialogDescription>
                </DialogHeader>

                <form onSubmit={handleSubmit} className="space-y-4 pt-4">
                    <div className="grid gap-2">
                        <Label htmlFor="title">Lesson Title</Label>
                        <Input
                            id="title"
                            type="text"
                            value={title}
                            onChange={(e) => setTitle(e.target.value)}
                            placeholder="Enter lesson title"
                            required
                            autoFocus
                        />
                    </div>

                    <div className="grid gap-2">
                        <Label htmlFor="description">Description (Optional)</Label>
                        <Textarea
                            id="description"
                            value={description}
                            onChange={(e) => setDescription(e.target.value)}
                            placeholder="Enter lesson description"
                            rows={3}
                        />
                    </div>

                    <DialogFooter className="mt-6">
                        <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
                            Cancel
                        </Button>
                        <Button type="submit" disabled={processing || !title.trim()}>
                            {processing ? 'Saving...' : 'Save Changes'}
                        </Button>
                    </DialogFooter>
                </form>
            </DialogContent>
        </Dialog>
    );
}
