<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Assessment extends Model
{
    //
    protected $fillable = [
        'title',
        'description',
        'class_id',
        'subject_id',
        'unit_id',
        'topic_id',
    ];

    public function class()
    {
        return $this->belongsTo(SchoolClass::class);
    }

    public function subject()
    {
        return $this->belongsTo(Subject::class);
    }

    public function unit()
    {
        return $this->belongsTo(Unit::class);
    }

    public function topic()
    {
        return $this->belongsTo(Topic::class);
    }

    public function questions()
    {
        return $this->hasMany(AssessmentQuestion::class);
    }

    public function results()
    {
        return $this->hasMany(AssessmentResult::class);
    }

    public function failedQuestions()
    {
        return $this->hasMany(AssessmentQuestionDetail::class, 'assessment_id')
            ->where('is_correct', false);
    }

    /**
     * Get the average result for a specific student across all their attempts
     */
    public function getStudentAverageResult($studentId)
    {
        $averageScore = AssessmentResult::getAverageScore($this->id, $studentId);

        if ($averageScore === null) {
            return null;
        }

        return [
            'average_score' => round($averageScore, 2),
            'status' => $averageScore >= 50 ? 'passed' : 'failed',
            'attempt_count' => $this->results()
                ->where('student_id', $studentId)
                ->where('progress', 'completed')
                ->count()
        ];
    }

    /**
     * Get all completed attempts for a student
     */
    public function getStudentAttempts($studentId)
    {
        return $this->results()
            ->where('student_id', $studentId)
            ->where('progress', 'completed')
            ->orderBy('attempt_number')
            ->get();
    }
}
