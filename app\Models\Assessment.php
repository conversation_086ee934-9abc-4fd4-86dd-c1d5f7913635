<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Assessment extends Model
{
    //
    protected $fillable = [
        'title',
        'description',
        'class_id',
        'subject_id',
        'unit_id',
        'topic_id',
    ];

    public function class()
    {
        return $this->belongsTo(SchoolClass::class);
    }

    public function subject()
    {
        return $this->belongsTo(Subject::class);
    }

    public function unit()
    {
        return $this->belongsTo(Unit::class);
    }

    public function topic()
    {
        return $this->belongsTo(Topic::class);
    }

    public function questions()
    {
        return $this->hasMany(AssessmentQuestion::class);
    }

    public function results()
    {
        return $this->hasMany(AssessmentResult::class);
    }

    public function failedQuestions()
    {
        return $this->hasMany(AssessmentQuestionDetail::class, 'assessment_id')
            ->where('is_correct', false);
    }
}
