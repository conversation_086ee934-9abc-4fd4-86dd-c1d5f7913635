<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class AssessmentResult extends Model
{
    //
    protected $fillable = [
        'assessment_id',
        'student_id',
        'attempt_number',
        'score',
        'failed_questions',
        'correct_questions',
        'status',
        'progress',
        'time_taken',
    ];

    public function assessment()
    {
        return $this->belongsTo(Assessment::class);
    }

    public function student()
    {
        return $this->belongsTo(User::class);
    }

    public function questionDetails()
    {
        return $this->hasMany(AssessmentQuestionDetail::class, 'assessment_result_id');
    }

    public function failedQuestions()
    {
        return $this->hasMany(AssessmentQuestionDetail::class, 'assessment_result_id')
            ->where('is_correct', false);
    }

    /**
     * Get all attempts for the same student and assessment
     */
    public function allAttempts()
    {
        return self::where('assessment_id', $this->assessment_id)
            ->where('student_id', $this->student_id)
            ->orderBy('attempt_number');
    }

    /**
     * Get the average score across all completed attempts for this student-assessment pair
     */
    public static function getAverageScore($assessmentId, $studentId)
    {
        return self::where('assessment_id', $assessmentId)
            ->where('student_id', $studentId)
            ->where('progress', 'completed')
            ->avg('score');
    }

    /**
     * Get the next attempt number for a student-assessment pair
     */
    public static function getNextAttemptNumber($assessmentId, $studentId)
    {
        $maxAttempt = self::where('assessment_id', $assessmentId)
            ->where('student_id', $studentId)
            ->max('attempt_number');

        return ($maxAttempt ?? 0) + 1;
    }

    /**
     * Check if this is the student's best attempt
     */
    public function isBestAttempt()
    {
        $bestScore = self::where('assessment_id', $this->assessment_id)
            ->where('student_id', $this->student_id)
            ->where('progress', 'completed')
            ->max('score');

        return $this->score == $bestScore;
    }
}
