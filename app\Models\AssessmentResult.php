<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class AssessmentResult extends Model
{
    //
    protected $fillable = [
        'assessment_id',
        'student_id',
        'score',
        'failed_questions',
        'correct_questions',
        'status',
    ];

    public function assessment()
    {
        return $this->belongsTo(Assessment::class);
    }

    public function student()
    {
        return $this->belongsTo(User::class);
    }

    public function questionDetails()
    {
        return $this->hasMany(AssessmentQuestionDetail::class, 'assessment_result_id');
    }

    public function failedQuestions()
    {
        return $this->hasMany(AssessmentQuestionDetail::class, 'assessment_result_id')
            ->where('is_correct', false);
    }
}
