import { useForm } from '@inertiajs/react';

import { <PERSON><PERSON>, DialogContent, DialogDescription, Di<PERSON>Footer, Di<PERSON>Header, DialogTitle } from '@/components/ui/dialog';

interface Unit {
    id: number;
    name: string;
    subject_id: number;
}

interface Props {
    unit: Unit;
    open: boolean;
    onOpenChange: (open: boolean) => void;
}

function DeleteUnit({ unit, open, onOpenChange }: Props) {
    const { delete: destroy, processing } = useForm();

    const handleDelete = () => {
        destroy(route('units.destroy', { unit: unit.id }), {
            preserveScroll: true,
            onSuccess: () => {
                onOpenChange(false);
            },
        });
    };

    return (
        <Dialog open={open} onOpenChange={onOpenChange}>
            <DialogContent className="sm:max-w-md">
                <DialogHeader>
                    <DialogTitle>Delete Unit</DialogTitle>
                    <DialogDescription>Are you sure you want to delete {unit.name}? This action cannot be undone.</DialogDescription>
                </DialogHeader>
                <DialogFooter>
                    <button
                        type="button"
                        onClick={() => onOpenChange(false)}
                        className="rounded-md px-4 py-2 text-sm font-medium text-gray-600 hover:bg-gray-100"
                        disabled={processing}
                    >
                        Cancel
                    </button>
                    <button
                        type="button"
                        onClick={handleDelete}
                        className="rounded-md bg-red-600 px-4 py-2 text-sm font-medium text-white hover:bg-red-700 disabled:opacity-50"
                        disabled={processing}
                    >
                        {processing ? 'Deleting...' : 'Delete Unit'}
                    </button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    );
}

export default DeleteUnit;
