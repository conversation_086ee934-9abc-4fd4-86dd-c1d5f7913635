<?php

use App\Http\Controllers\Api\AssessmentController;
use App\Http\Controllers\Api\LessonController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\StudentAuthController;
use App\Http\Middleware\StudentLoginRateLimiter;

Route::post('/student/login', [StudentAuthController::class, 'login'])
    ->middleware(StudentLoginRateLimiter::class);

Route::post('/student/login-qr', [StudentAuthController::class, 'loginWithQR'])
    ->middleware(StudentLoginRateLimiter::class);

// Get all students with QR tokens (for QR code generation page)
Route::get('/students/qr-tokens', function () {
    $students = \App\Models\User::where('role', 'student')
        ->whereNotNull('qr_code_token')
        ->select('id', 'name', 'qr_code_token')
        ->get()
        ->map(function ($student) {
            return [
                'id' => $student->id,
                'name' => $student->name,
                'qr_token' => $student->qr_code_token
            ];
        });

    return response()->json(['students' => $students]);
});

Route::get('/lesson', [LessonController::class, 'getLesson']);
Route::controller(AssessmentController::class)->group(function () {
    Route::get('/assessments/{user_id}/{subject_id}', 'getAssessments');
    Route::get('/assessments/details/{id}/{user_id}', 'getAssessment');
    Route::post('/assessments/{id}/submit', 'submitAnswers');
    Route::get('/assessments/{id}/result/{user_id}', 'getResult');
});

// Assessment statistics
Route::get('/assessment-stats', [AssessmentController::class, 'getStudentStats']);

// User information
Route::get('/user', function (Request $request) {
    return $request->user();
});

Route::get('/units/{subject_id}/{student_id}', [App\Http\Controllers\Api\UnitController::class, 'getUnits']);
Route::get('/topics/{unit_id}', [App\Http\Controllers\Api\TopicController::class, 'getTopics']);
Route::post('/lesson/mark-as-complete', [App\Http\Controllers\Api\LessonController::class, 'markAsComplete']);
