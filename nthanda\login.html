<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>Login to Learning</title>
        <link rel="stylesheet" href="styles.css">
        <script src="auth.js"></script>
        <script src="./axios.js"></script>
        <script
            src="https://unpkg.com/html5-qrcode@2.3.8/html5-qrcode.min.js"></script>
        <style>
            body {
                background-color: var(--color-yellow);
            }
            .welcome-container {
                background-color: var(--color-yellow);
            }
            .welcome-text {
                color: var(--color-blue);
            }
            .sub-text {
                color: var(--color-blue);
            }
            .pin-container {
                display: flex;
                flex-direction: column;
                align-items: center;
                z-index: 1;
                width: 100%;
                max-width: 250px;
            }
            .pin-input {
                width: 100%;
                padding: 15px;
                font-size: 24px;
                text-align: center;
                border-radius: 20px;
                border: 4px solid var(--color-dark-yellow);
                background-color: var(--color-white);
                color: var(--color-blue);
                box-shadow: 0 6px 10px rgba(0, 0, 0, 0.1);
                margin-bottom: 15px;
                font-family: "Fredoka", sans-serif;
                font-weight: 600;
                letter-spacing: 5px;
            }
            .pin-input:focus {
                outline: none;
                border-color: var(--color-blue);
            }
            .login-button {
                background-color: var(--color-green);
                border-radius: 30px;
                padding: 12px 40px;
                color: var(--color-white);
                font-weight: 700;
                font-size: 20px;
                cursor: pointer;
                width: 100%;
                max-width: 200px;
                text-align: center;
                letter-spacing: 1px;
                border: 4px solid var(--color-dark-green);
                border-left-width: 2px;
                border-right-width: 2px;
                border-top-width: 0;
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                transition: transform 0.2s;
                text-decoration: none;
                display: block;
            }
            .login-button:hover {
                transform: translateY(-2px);
            }
            .login-button:active {
                transform: translateY(1px);
            }
            .error-message {
                color: var(--color-red);
                font-weight: 600;
                margin-bottom: 10px;
                text-align: center;
                display: none;
                animation: shake 0.5s ease-in-out;
            }
            .animal-container {
                display: flex;
                flex-direction: row;
                align-items: center;
                justify-content: center;
                width: 100%;
                max-width: 250px;
                height: 90px;
                margin-bottom: 0px;
            }
            .animal {
            
                width: 80px;
                height: 80px;
                transition: all 0.5s ease;
            }
           
            @keyframes shake {
                0%, 100% { transform: translateX(0); }
                25% { transform: translateX(-10px); }
                50% { transform: translateX(10px); }
                75% { transform: translateX(-10px); }
            }
            .success-animation {
                display: none;
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background-color: #63f011c5;
                z-index: 100;
                justify-content: center;
                align-items: center;
                flex-direction: column;
            }
            .success-icon {
                font-size: 80px;
                color: var(--color-green);
                margin-bottom: 20px;
            }
            .pin-input.error {
                border-color: var(--color-red);
                animation: shake 0.5s ease-in-out;
            }

            /* QR Code Login Styles */
            .login-toggle {
                display: flex;
                justify-content: center;
                margin-bottom: 20px;
                gap: 10px;
            }

            .toggle-button {
                background-color: var(--color-white);
                border: 3px solid var(--color-blue);
                border-radius: 15px;
                padding: 8px 16px;
                color: var(--color-blue);
                font-weight: 600;
                font-size: 14px;
                cursor: pointer;
                transition: all 0.3s ease;
                font-family: "Fredoka", sans-serif;
            }

            .toggle-button.active {
                background-color: var(--color-blue);
                color: var(--color-white);
            }

            .toggle-button:hover {
                transform: translateY(-1px);
            }

            .qr-scanner-container {
                display: none;
                flex-direction: column;
                align-items: center;
                width: 100%;
                max-width: 300px;
                margin-bottom: 20px;
            }

            .qr-scanner-container.active {
                display: flex;
            }

            #qr-reader {
                width: 100%;
                max-width: 250px;
                border-radius: 15px;
                overflow: hidden;
                border: 4px solid var(--color-blue);
                box-shadow: 0 6px 10px rgba(0, 0, 0, 0.1);
            }

            .qr-instructions {
                text-align: center;
                color: var(--color-blue);
                font-weight: 500;
                margin-bottom: 15px;
                font-size: 16px;
            }

            .pin-container.active {
                display: flex;
            }

            .pin-container.hidden {
                display: none;
            }
        </style>
    </head>
    <body>
        <div class="welcome-container">
            <!-- Corner Images -->
            <img src="assets/welcome/book.png" alt="Book"
                class="corner-image top-left-image">
            <img src="assets/welcome/coin.png" alt="Coin"
                class="corner-image top-right-image">

            <div class="welcome-content">
                <!-- Logo -->
                <img src="assets/logo.png" alt="Logo" class="logo">

                <!-- Main Content -->
                <h1 class="welcome-text">Hello Friend!</h1>

                <p class="sub-text" id="sub-text">Please enter your PIN to start
                    learning</p>

                <!-- Login Method Toggle -->
                <div class="login-toggle">
                    <button class="toggle-button active" id="pin-toggle">PIN
                        Login</button>
                    <button class="toggle-button" id="qr-toggle">QR
                        Code</button>
                </div>

                <!-- PIN Input -->
                <div class="pin-container active" id="pin-container">
                    <input type="password" class="pin-input" id="pin-input"
                        maxlength="4" placeholder="****">
                    <div class="error-message" id="error-message">Oops! That PIN
                        is not correct. Try again!</div>
                </div>

                <!-- QR Code Scanner -->
                <div class="qr-scanner-container" id="qr-scanner-container">
                    <div class="qr-instructions">Point your camera at the QR
                        code</div>
                    <div id="qr-reader"></div>

                    <!-- Debug: Manual QR Test Button -->

                </div>

                <!-- Login Button -->
                <button class="login-button" id="login-button">Let's
                    Go!</button>
            </div>
        </div>

        <!-- Success Animation -->
        <div class="success-animation" id="success-animation">
            <div class="success-icon">🎉</div>
            <h2>Yay! Login Successful!</h2>
        </div>

        <script>
            document.addEventListener('DOMContentLoaded', function() {
                // Check if already logged in
                if (isLoggedIn()) {
                    window.location.href = 'main.html';
                    return;
                }

                // Initialize variables
                let html5QrCode = null;
                let currentLoginMode = 'pin'; // 'pin' or 'qr'
                let scannedQRToken = null;

                // Get DOM elements
                const pinToggle = document.getElementById('pin-toggle');
                const qrToggle = document.getElementById('qr-toggle');
                const pinContainer = document.getElementById('pin-container');
                const qrContainer = document.getElementById('qr-scanner-container');
                const subText = document.getElementById('sub-text');
                const pinInput = document.getElementById('pin-input');
                const loginButton = document.getElementById('login-button');

                // Focus on PIN input initially
                pinInput.focus();

                // Toggle between PIN and QR login
                pinToggle.addEventListener('click', function() {
                    switchToMode('pin');
                });

                qrToggle.addEventListener('click', function() {
                    switchToMode('qr');
                });

                function switchToMode(mode) {
                    currentLoginMode = mode;

                    if (mode === 'pin') {
                        // Switch to PIN mode
                        pinToggle.classList.add('active');
                        qrToggle.classList.remove('active');
                        pinContainer.classList.add('active');
                        pinContainer.classList.remove('hidden');
                        qrContainer.classList.remove('active');
                        subText.textContent = 'Please enter your PIN to start learning';

                        // Stop QR scanner if running
                        if (html5QrCode && html5QrCode.isScanning) {
                            html5QrCode.stop().then(() => {
                                console.log('QR scanner stopped');
                            }).catch(err => {
                                console.log('Error stopping QR scanner:', err);
                            });
                        }

                        pinInput.focus();
                    } else if (mode === 'qr') {
                        // Switch to QR mode
                        qrToggle.classList.add('active');
                        pinToggle.classList.remove('active');
                        pinContainer.classList.remove('active');
                        pinContainer.classList.add('hidden');
                        qrContainer.classList.add('active');
                        subText.textContent = 'Scan your QR code to start learning';

                        // Start QR scanner
                        startQRScanner();
                    }
                }

                // Handle PIN input to only allow numbers
                pinInput.addEventListener('input', function() {
                    this.value = this.value.replace(/[^0-9]/g, '');
                });

                // Login button click handler
                loginButton.addEventListener('click', loginStudent);

                // Enter key press handler
                pinInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        loginStudent();
                    }
                });

                // QR Scanner functions
                function startQRScanner() {
                    if (html5QrCode) {
                        return; // Already initialized
                    }

                    html5QrCode = new Html5Qrcode("qr-reader");

                    const config = {
                        fps: 10,
                        qrbox: { width: 200, height: 200 },
                        aspectRatio: 1.0
                    };

                    html5QrCode.start(
                        { facingMode: "environment" }, // Use back camera
                        config,
                        (decodedText, decodedResult) => {
                            // QR code successfully scanned
                            console.log('QR Code detected:', decodedText);
                            scannedQRToken = decodedText;

                            // Stop the scanner
                            html5QrCode.stop().then(() => {
                                console.log('QR scanner stopped after successful scan');
                                console.log('Attempting to login with QR token:', scannedQRToken);
                                // Automatically login with QR code
                                loginWithQR(scannedQRToken);
                            }).catch(err => {
                                console.log('Error stopping QR scanner:', err);
                                // Try to login anyway
                                loginWithQR(scannedQRToken);
                            });
                        },
                        (errorMessage) => {
                            // QR code parsing error (can be ignored)
                            // console.log('QR scan error:', errorMessage);
                        }
                    ).catch(err => {
                        console.error('Error starting QR scanner:', err);
                        showError('Unable to access camera. Please check permissions.');
                    });
                }

                function loginStudent(){
                    if (currentLoginMode === 'pin') {
                        const pin = document.getElementById('pin-input').value;
                        if (pin.length !== 4) {
                            showError("Please enter a 4-digit PIN");
                            return;
                        }

                        axios.post('https://lomoni.kaizen-mw.com/api/student/login', { pin: pin })
                        .then(response => {
                            if (response.data.success) {
                                handleSuccessfulLogin(response.data);
                            } else {
                                showError(response.data.message || "Incorrect PIN. Please try again!");
                            }
                        })
                        .catch(error => {
                            console.error("Error:", error);
                            showError("Login failed. Please try again.");
                        });
                    } else if (currentLoginMode === 'qr' && scannedQRToken) {
                        loginWithQR(scannedQRToken);
                    } else {
                        showError("Please scan a QR code or enter your PIN");
                    }
                }

                function loginWithQR(qrToken) {
                    console.log('loginWithQR called with token:', qrToken);

                    if (!qrToken) {
                        console.error('No QR token provided');
                        showError("No QR code detected. Please try again.");
                        return;
                    }

                    console.log('Making API request to login with QR...');
                    axios.post('https://lomoni.kaizen-mw.com/api/student/login-qr', { qr_token: qrToken })
                        .then(response => {
                            console.log('QR Login API response:', response);
                            if (response.data.success) {
                                console.log('QR Login successful, handling login...');
                                handleSuccessfulLogin(response.data);
                            } else {
                                console.log('QR Login failed:', response.data.message);
                                showError(response.data.message || "Invalid QR code. Please try again!");
                                // Reset QR scanner for another attempt
                                scannedQRToken = null;
                                setTimeout(() => {
                                    startQRScanner();
                                }, 2000);
                            }
                        })
                        .catch(error => {
                            console.error("QR Login Error:", error);
                            showError("QR login failed. Please try again.");
                            // Reset QR scanner for another attempt
                            scannedQRToken = null;
                            setTimeout(() => {
                                startQRScanner();
                            }, 2000);
                        });
                }

                function handleSuccessfulLogin(data) {
                    console.log('handleSuccessfulLogin called with data:', data);

                    // Save to local storage
                    localStorage.setItem("isLogged", "true");

                    // Store student data
                    const studentData = data.student;
                    localStorage.setItem("studentDetails", JSON.stringify(studentData));
                    localStorage.setItem("subjects", JSON.stringify(data.subjects));
                    localStorage.setItem("units", JSON.stringify(data.units));
                    localStorage.setItem("topics", JSON.stringify(data.topics));

                    // Store API token if provided
                    if (data.token) {
                        localStorage.setItem("apiToken", data.token);
                    }

                    console.log('Showing success animation...');
                    // Show success animation
                    document.getElementById('success-animation').style.display = 'flex';

                    // Redirect after short delay
                    setTimeout(function() {
                        console.log('Redirecting to main.html...');
                        window.location.href = "main.html";
                    }, 2500);
                }

                

                function showError(message) {
                    const errorElement = document.getElementById('error-message');
                    errorElement.textContent = message;
                    errorElement.style.display = 'block';
                    
                    const pinInput = document.getElementById('pin-input');
                    pinInput.classList.add('error');
                    
                    // Clear error after 3 seconds
                    setTimeout(function() {
                        errorElement.style.display = 'none';
                        pinInput.classList.remove('error');
                    }, 3000);
                }

                // Debug function to test QR login without camera
                function testQRLogin() {
                    const testToken = 'STU_L497muLQ1XUBhy3PkyOXwrUA70Yarbzu';
                    console.log('Testing QR login with token:', testToken);
                    scannedQRToken = testToken;
                    loginWithQR(testToken);
                }
            });
        </script>
    </body>
</html>